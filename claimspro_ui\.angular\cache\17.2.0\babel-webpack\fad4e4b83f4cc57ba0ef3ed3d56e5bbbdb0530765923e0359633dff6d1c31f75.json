{"ast": null, "code": "import { CommonModule } from \"@angular/common\";\nimport { SearchInputOverComponent } from \"./search-input-over/search-input-over.component\";\nimport { ReactiveFormsModule } from \"@angular/forms\";\nimport { MatButtonModule } from \"@angular/material/button\";\nimport { MatIconModule } from \"@angular/material/icon\";\nimport { SharedDirectivesModule } from \"../directives/shared-directives.module\";\nimport * as i0 from \"@angular/core\";\nexport class SearchModule {\n  static #_ = this.ɵfac = function SearchModule_Factory(t) {\n    return new (t || SearchModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: SearchModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [ReactiveFormsModule, MatIconModule, MatButtonModule, CommonModule, SharedDirectivesModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SearchModule, {\n    declarations: [SearchInputOverComponent],\n    imports: [ReactiveFormsModule, MatIconModule, MatButtonModule, CommonModule, SharedDirectivesModule],\n    exports: [SearchInputOverComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "SearchInputOverComponent", "ReactiveFormsModule", "MatButtonModule", "MatIconModule", "SharedDirectivesModule", "SearchModule", "_", "_2", "_3", "declarations", "imports", "exports"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\shared\\search\\search.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\";\nimport { CommonModule } from \"@angular/common\";\nimport { SearchInputOverComponent } from \"./search-input-over/search-input-over.component\";\nimport { ReactiveFormsModule } from \"@angular/forms\";\nimport { MatButtonModule } from \"@angular/material/button\";\nimport { MatIconModule } from \"@angular/material/icon\";\nimport { SharedDirectivesModule } from \"../directives/shared-directives.module\";\n\n@NgModule({\n  declarations: [SearchInputOverComponent],\n  exports: [SearchInputOverComponent],\n  imports: [ReactiveFormsModule, MatIconModule, MatButtonModule, CommonModule, SharedDirectivesModule]\n})\nexport class SearchModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,wBAAwB,QAAQ,iDAAiD;AAC1F,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,sBAAsB,QAAQ,wCAAwC;;AAO/E,OAAM,MAAOC,YAAY;EAAA,QAAAC,CAAA,G;qBAAZD,YAAY;EAAA;EAAA,QAAAE,EAAA,G;UAAZF;EAAY;EAAA,QAAAG,EAAA,G;cAFbP,mBAAmB,EAAEE,aAAa,EAAED,eAAe,EAAEH,YAAY,EAAEK,sBAAsB;EAAA;;;2EAExFC,YAAY;IAAAI,YAAA,GAJRT,wBAAwB;IAAAU,OAAA,GAE7BT,mBAAmB,EAAEE,aAAa,EAAED,eAAe,EAAEH,YAAY,EAAEK,sBAAsB;IAAAO,OAAA,GADzFX,wBAAwB;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}