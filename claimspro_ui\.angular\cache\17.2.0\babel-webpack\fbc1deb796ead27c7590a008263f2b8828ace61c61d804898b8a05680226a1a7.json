{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class NavigationService {\n  constructor() {\n    this.iconMenu = [{\n      name: 'HOME',\n      type: 'icon',\n      tooltip: 'Home',\n      icon: 'home',\n      state: 'home'\n    }, {\n      name: 'PROFILE',\n      type: 'icon',\n      tooltip: 'Profile',\n      icon: 'person',\n      state: 'profile/overview'\n    }, {\n      name: 'TOUR',\n      type: 'icon',\n      tooltip: 'Tour',\n      icon: 'flight_takeoff',\n      state: 'tour'\n    }, {\n      name: 'CHAT',\n      type: 'icon',\n      tooltip: 'Chat',\n      icon: 'chat',\n      state: 'chat'\n    }, {\n      type: 'separator',\n      name: 'Main Items'\n    }, {\n      name: 'Login',\n      type: 'link',\n      tooltip: 'Login',\n      icon: 'person'\n    }, {\n      name: 'DASHBOARD',\n      type: 'dropDown',\n      tooltip: 'Dashboard',\n      icon: 'dashboard',\n      sub: [{\n        name: 'Analytics',\n        state: 'dashboard/analytics'\n      }, {\n        name: 'Learning Management',\n        state: 'dashboard/learning-management'\n      }, {\n        name: 'Analytics Alt',\n        state: 'dashboard/analytics-alt'\n      }, {\n        name: 'Cryptocurrency',\n        state: 'dashboard/crypto'\n      }, {\n        name: 'Dark Cards',\n        state: 'dashboard/dark'\n      }]\n    }, {\n      name: 'My Practice',\n      type: 'dropDown',\n      icon: 'list',\n      sub: [{\n        name: 'Codes',\n        type: 'dropDown',\n        icon: 'list',\n        sub: [{\n          name: 'ICD Codes',\n          state: 'tables/mat-table'\n        }, {\n          name: 'CPT Codes',\n          state: 'cruds/ngx-table'\n        }, {\n          name: 'HCPCS Codes',\n          state: 'material/table'\n        }]\n      }, {\n        name: 'Basic Table',\n        state: 'tables/mat-table'\n      }, {\n        name: 'Places Of Service',\n        state: 'cruds/ngx-table'\n      }, {\n        name: 'Upload Claims',\n        state: 'material/table'\n      }]\n    }, {\n      name: 'Claims',\n      type: 'dropDown',\n      icon: 'list',\n      sub: [{\n        name: 'Select Practice',\n        state: 'tables/mat-table'\n      }, {\n        name: 'New',\n        state: 'cruds/ngx-table'\n      }, {\n        name: 'History',\n        state: 'material/table'\n      }]\n    }, {\n      name: 'Manage',\n      type: 'dropDown',\n      icon: 'list',\n      sub: [{\n        name: 'Users',\n        state: 'tables/mat-table'\n      }, {\n        name: 'Codes',\n        type: 'dropDown',\n        icon: 'list',\n        sub: [{\n          name: 'ICD Codes',\n          state: 'tables/mat-table'\n        }, {\n          name: 'CPT Codes',\n          state: 'cruds/ngx-table'\n        }, {\n          name: 'HCPCS Codes',\n          state: 'material/table'\n        }]\n      }, {\n        name: 'Fee Schedules',\n        type: 'dropDown',\n        icon: 'list',\n        sub: [{\n          name: 'Schedules',\n          state: 'tables/mat-table'\n        }, {\n          name: 'CPT Pricing',\n          state: 'cruds/ngx-table'\n        }]\n      }, {\n        name: 'Setup',\n        type: 'dropDown',\n        icon: 'list',\n        sub: [{\n          name: 'Providers',\n          state: 'tables/mat-table'\n        }, {\n          name: 'Practices',\n          state: 'cruds/ngx-table'\n        }, {\n          name: 'Clients',\n          state: 'cruds/ngx-table'\n        }, {\n          name: 'Networks',\n          state: 'cruds/ngx-table'\n        }, {\n          name: 'Payers',\n          state: 'cruds/ngx-table'\n        }]\n      }, {\n        name: 'Link Practices',\n        type: 'dropDown',\n        icon: 'list',\n        sub: [{\n          name: 'Providers->Practices',\n          state: 'tables/mat-table'\n        }, {\n          name: 'Payers->Practices',\n          state: 'cruds/ngx-table'\n        }]\n      }]\n    }, {\n      name: 'Batch',\n      type: 'dropDown',\n      icon: 'list',\n      sub: [{\n        name: 'List',\n        state: 'tables/mat-table'\n      }, {\n        name: 'Upload',\n        state: 'cruds/ngx-table'\n      }, {\n        name: 'Download',\n        state: 'material/table'\n      }, {\n        name: 'Import',\n        type: 'dropDown',\n        icon: 'list',\n        sub: [{\n          name: 'Practice Setup',\n          state: 'tables/mat-table'\n        }, {\n          name: 'Claims(X12)',\n          state: 'cruds/ngx-table'\n        }]\n      }]\n    }, {\n      name: 'Reports',\n      type: 'dropDown',\n      icon: 'receipt',\n      sub: [{\n        name: 'Report1',\n        state: 'invoice/list'\n      }, {\n        name: 'Report2',\n        state: 'invoice/5a9ae2106518248b68251fd1'\n      }, {\n        name: 'Report3',\n        state: 'invoice/list'\n      }, {\n        name: 'Report4',\n        state: 'invoice/list'\n      }, {\n        name: 'Report5',\n        state: 'invoice/list'\n      }]\n    }, {\n      name: 'Invoice',\n      type: 'dropDown',\n      icon: 'receipt',\n      sub: [{\n        name: 'Generate',\n        state: 'invoice/list'\n      }, {\n        name: 'View',\n        state: 'invoice/5a9ae2106518248b68251fd1'\n      }, {\n        name: 'Pay',\n        state: 'invoice/list'\n      }, {\n        name: 'Adjust',\n        state: 'invoice/list'\n      }]\n    }, {\n      name: 'ECOMMERCE',\n      type: 'dropDown',\n      tooltip: 'Shop',\n      icon: 'shopping_cart',\n      sub: [{\n        name: 'PRODUCTS',\n        state: 'shop'\n      }, {\n        name: 'PRODUCT DETAILS',\n        state: 'shop/products/5a9ae2106f155194e5c95d67'\n      }, {\n        name: 'CART',\n        state: 'shop/cart'\n      }, {\n        name: 'CHECKOUT',\n        state: 'shop/checkout'\n      }]\n    }, {\n      name: 'CHAT',\n      type: 'link',\n      tooltip: 'Chat',\n      icon: 'chat',\n      state: 'chat',\n      badges: [{\n        color: 'warn',\n        value: '1'\n      }]\n    }, {\n      name: 'INBOX',\n      type: 'link',\n      icon: 'inbox',\n      state: 'inbox',\n      badges: [{\n        color: 'primary',\n        value: '4'\n      }]\n    }, {\n      name: 'CALENDAR',\n      type: 'link',\n      tooltip: 'Calendar',\n      icon: 'date_range',\n      state: 'calendar'\n    }, {\n      name: 'Todo',\n      type: 'link',\n      tooltip: 'Todo',\n      icon: 'center_focus_strong',\n      state: 'todo/list'\n    }, {\n      name: 'DIALOGS',\n      type: 'dropDown',\n      tooltip: 'Dialogs',\n      icon: 'filter_none',\n      sub: [{\n        name: 'CONFIRM',\n        state: 'dialogs/confirm'\n      }, {\n        name: 'LOADER',\n        state: 'dialogs/loader'\n      }]\n    }, {\n      name: 'Material Kits',\n      type: 'dropDown',\n      tooltip: 'Material',\n      icon: 'favorite',\n      badges: [{\n        color: 'primary',\n        value: '60+'\n      }],\n      sub: [{\n        name: 'Form controls',\n        type: 'dropDown',\n        sub: [{\n          name: 'Autocomplete',\n          state: 'material/autocomplete'\n        }, {\n          name: 'Checkbox',\n          state: 'material/checkbox'\n        }, {\n          name: 'Datepicker',\n          state: 'material/datepicker'\n        }, {\n          name: 'Form Field',\n          state: 'material/form-field'\n        }, {\n          name: 'Input Field',\n          state: 'material/input-field'\n        }, {\n          name: 'Radio Button',\n          state: 'material/radio-button'\n        }, {\n          name: 'Select',\n          state: 'material/select'\n        }, {\n          name: 'Slider',\n          state: 'material/slider'\n        }, {\n          name: 'Slider Toggle',\n          state: 'material/slider-toggle'\n        }]\n      }, {\n        name: 'Navigation',\n        type: 'dropDown',\n        sub: [{\n          name: 'Menu',\n          state: 'material/menu'\n        }, {\n          name: 'Sidenav',\n          state: 'material/sidenav'\n        }, {\n          name: 'Toolbar',\n          state: 'material/toolbar'\n        }]\n      }, {\n        name: 'Layout',\n        type: 'dropDown',\n        sub: [{\n          name: 'Card',\n          state: 'material/card'\n        }, {\n          name: 'Divider',\n          state: 'material/divider'\n        }, {\n          name: 'Expansion Panel',\n          state: 'material/expansion-panel'\n        }, {\n          name: 'Grid',\n          state: 'material/grid'\n        }, {\n          name: 'List',\n          state: 'material/list'\n        }, {\n          name: 'Stepper',\n          state: 'material/stepper'\n        }, {\n          name: 'Tab',\n          state: 'material/tab-group'\n        }, {\n          name: 'Tree',\n          state: 'material/tree'\n        }]\n      }, {\n        name: 'Buttons & Indicators',\n        type: 'dropDown',\n        sub: [{\n          name: 'BUTTONS',\n          state: 'material/buttons'\n        }, {\n          name: 'Button Toggle',\n          state: 'material/button-toggle'\n        }, {\n          name: 'Badge',\n          state: 'material/badge'\n        }, {\n          name: 'Chips',\n          state: 'material/chips'\n        }, {\n          name: 'Icons',\n          state: 'material/icons'\n        }, {\n          name: 'Progress Spinner',\n          state: 'material/progress-spinner'\n        }, {\n          name: 'Progress Bar',\n          state: 'material/progress-bar'\n        }, {\n          name: 'Ripples',\n          state: 'material/ripples'\n        }]\n      }, {\n        name: 'Popups & Modals',\n        type: 'dropDown',\n        sub: [{\n          name: 'Tooltip',\n          state: 'material/tooltip'\n        }, {\n          name: 'Bottom Sheet',\n          state: 'material/bottom-sheet'\n        }, {\n          name: 'Dialog',\n          state: 'material/dialog'\n        }, {\n          name: 'Snackbar',\n          state: 'material/snackbar'\n        }]\n      }, {\n        name: 'Data Table',\n        type: 'dropDown',\n        sub: [{\n          name: 'paginator',\n          state: 'material/paginator'\n        }, {\n          name: 'Sort Header',\n          state: 'material/sort-header'\n        }, {\n          name: 'Table',\n          state: 'material/table'\n        }]\n      }]\n    }, {\n      name: 'FORMS',\n      type: 'dropDown',\n      tooltip: 'Forms',\n      icon: 'description',\n      sub: [{\n        name: 'BASIC',\n        state: 'forms/basic'\n      }, {\n        name: 'EDITOR',\n        state: 'forms/editor'\n      }, {\n        name: 'WIZARD',\n        state: 'forms/wizard'\n      }]\n    }, {\n      name: 'PROFILE',\n      type: 'dropDown',\n      tooltip: 'Profile',\n      icon: 'person',\n      badges: [{\n        color: 'primary',\n        value: '2'\n      }],\n      sub: [{\n        name: 'OVERVIEW',\n        state: 'profile/overview'\n      }, {\n        name: 'SETTINGS',\n        state: 'profile/settings'\n      }, {\n        name: 'BLANK',\n        state: 'profile/blank'\n      }]\n    }, {\n      name: 'TOUR',\n      type: 'link',\n      tooltip: 'Tour',\n      icon: 'flight_takeoff',\n      state: 'tour'\n    },\n    // {\n    //   name: 'MAP',\n    //   type: 'link',\n    //   tooltip: 'Map',\n    //   icon: 'add_location',\n    //   state: 'map'\n    // },\n    {\n      name: 'CHARTS',\n      type: 'dropDown',\n      tooltip: 'Charts',\n      icon: 'show_chart',\n      sub: [{\n        name: 'Chart js',\n        state: 'charts'\n      }, {\n        name: 'eChart',\n        type: 'dropDown',\n        state: 'chart',\n        sub: [{\n          name: 'Pie',\n          state: 'chart/pie'\n        }, {\n          name: 'Bar',\n          state: 'chart/bar'\n        }, {\n          name: 'Radar',\n          state: 'chart/radar'\n        }, {\n          name: \"Heatmap\",\n          state: \"chart/heatmap\"\n        }]\n      }]\n    }, {\n      name: 'Page Layouts',\n      type: 'dropDown',\n      icon: 'view_carousel',\n      sub: [{\n        name: 'Left sidebar card',\n        state: 'page-layouts/left-sidebar-card'\n      }, {\n        name: 'Right sidebar card',\n        state: 'page-layouts/right-sidebar-card'\n      }, {\n        name: 'Full width card',\n        state: 'page-layouts/full-width-card'\n      }, {\n        name: 'Full width card tab',\n        state: 'page-layouts/full-width-card-tab'\n      }, {\n        name: 'Full width plain',\n        state: 'page-layouts/full-width-plain'\n      }, {\n        name: 'Left sidebar plain',\n        state: 'page-layouts/left-sidebar-plain'\n      }]\n    }, {\n      name: 'SESSIONS',\n      type: 'dropDown',\n      tooltip: 'Pages',\n      icon: 'view_carousel',\n      sub: [{\n        name: 'SIGNUP',\n        state: 'sessions/signup'\n      }, {\n        name: 'Signup 2',\n        state: 'sessions/signup2'\n      }, {\n        name: 'Signup 3',\n        state: 'sessions/signup3'\n      }, {\n        name: 'Signup 4',\n        state: 'sessions/signup4'\n      }, {\n        name: 'SIGNIN',\n        state: 'sessions/signin'\n      }, {\n        name: 'Signin 2',\n        state: 'sessions/signin2'\n      }, {\n        name: 'Signin 3',\n        state: 'sessions/signin3'\n      }, {\n        name: 'Signin 4',\n        state: 'sessions/signin4'\n      }, {\n        name: 'FORGOT',\n        state: 'sessions/forgot-password'\n      }, {\n        name: 'LOCKSCREEN',\n        state: 'sessions/lockscreen'\n      }, {\n        name: 'NOTFOUND',\n        state: 'sessions/404'\n      }, {\n        name: 'ERROR',\n        state: 'sessions/error'\n      }]\n    }, {\n      name: 'Utilities',\n      type: 'dropDown',\n      icon: 'format_list_bulleted',\n      sub: [{\n        name: 'Color',\n        state: 'utilities/color'\n      }, {\n        name: 'Typography',\n        state: 'utilities/typography'\n      }]\n    }, {\n      name: 'OTHERS',\n      type: 'dropDown',\n      tooltip: 'Others',\n      icon: 'blur_on',\n      sub: [{\n        name: 'GALLERY',\n        state: 'others/gallery'\n      }, {\n        name: 'PRICINGS',\n        state: 'others/pricing'\n      }, {\n        name: 'USERS',\n        state: 'others/users'\n      }, {\n        name: 'BLANK',\n        state: 'others/blank'\n      }]\n    }, {\n      name: 'MATICONS',\n      type: 'link',\n      tooltip: 'Material Icons',\n      icon: 'store',\n      state: 'icons'\n    }, {\n      name: 'Multi Level',\n      type: 'dropDown',\n      tooltip: 'Multi Level',\n      icon: 'format_align_center',\n      sub: [{\n        name: 'Level Two',\n        state: 'fake-4'\n      }, {\n        name: 'Level Two',\n        type: 'dropDown',\n        sub: [{\n          name: 'Level Three',\n          state: 'fake-2'\n        }, {\n          name: 'Level Three',\n          type: 'dropDown',\n          sub: [{\n            name: 'Level Four',\n            state: 'fake-3'\n          }, {\n            name: 'Level Four',\n            type: 'dropDown',\n            sub: [{\n              name: 'Level Five',\n              state: 'fake-3'\n            }, {\n              name: 'Level Five',\n              type: 'link',\n              state: 'fake-3'\n            }]\n          }]\n        }]\n      }, {\n        name: 'Level Two',\n        state: 'fake-5'\n      }]\n    }, {\n      name: 'DOC',\n      type: 'extLink',\n      tooltip: 'Documentation',\n      icon: 'library_books',\n      state: 'http://demos.ui-lib.com/egret-doc/'\n    }];\n    this.separatorMenu = [{\n      type: 'separator',\n      name: 'CUSTOM COMPONENTS'\n    }, {\n      name: 'DASHBOARD',\n      type: 'dropDown',\n      tooltip: 'Dashboard',\n      icon: 'dashboard',\n      sub: [{\n        name: 'Analytics',\n        state: 'dashboard/analytics'\n      }, {\n        name: 'Learning Management',\n        state: 'dashboard/learning-management'\n      }, {\n        name: 'Analytics Alt',\n        state: 'dashboard/analytics-alt'\n      }, {\n        name: 'Cryptocurrency',\n        state: 'dashboard/crypto'\n      }, {\n        name: 'Dark Cards',\n        state: 'dashboard/dark'\n      }]\n    }, {\n      name: 'CRUD Table',\n      type: 'link',\n      tooltip: 'CRUD Table',\n      icon: 'list',\n      state: 'cruds/ngx-table'\n    }, {\n      name: 'ECOMMERCE',\n      type: 'dropDown',\n      tooltip: 'Shop',\n      icon: 'shopping_cart',\n      sub: [{\n        name: 'PRODUCTS',\n        state: 'shop'\n      }, {\n        name: 'PRODUCT DETAILS',\n        state: 'shop/products/5a9ae2106f155194e5c95d67'\n      }, {\n        name: 'CART',\n        state: 'shop/cart'\n      }, {\n        name: 'CHECKOUT',\n        state: 'shop/checkout'\n      }]\n    }, {\n      name: 'INBOX',\n      type: 'link',\n      icon: 'inbox',\n      state: 'inbox',\n      badges: [{\n        color: 'primary',\n        value: '4'\n      }]\n    }, {\n      name: 'Invoice',\n      type: 'dropDown',\n      icon: 'receipt',\n      sub: [{\n        name: 'List',\n        state: 'invoice/list'\n      }, {\n        name: 'View / Edit',\n        state: 'invoice/5a9ae2106518248b68251fd1'\n      }]\n    }, {\n      name: 'PROFILE',\n      type: 'dropDown',\n      tooltip: 'Profile',\n      icon: 'person',\n      badges: [{\n        color: 'primary',\n        value: '2'\n      }],\n      sub: [{\n        name: 'OVERVIEW',\n        state: 'profile/overview'\n      }, {\n        name: 'SETTINGS',\n        state: 'profile/settings'\n      }, {\n        name: 'BLANK',\n        state: 'profile/blank'\n      }]\n    }, {\n      name: 'CHAT',\n      type: 'link',\n      tooltip: 'Chat',\n      icon: 'chat',\n      state: 'chat',\n      badges: [{\n        color: 'warn',\n        value: '1'\n      }]\n    }, {\n      name: 'Todo',\n      type: 'link',\n      tooltip: 'Todo',\n      icon: 'center_focus_strong',\n      state: 'todo/list'\n    }, {\n      name: 'DIALOGS',\n      type: 'dropDown',\n      tooltip: 'Dialogs',\n      icon: 'filter_none',\n      sub: [{\n        name: 'CONFIRM',\n        state: 'dialogs/confirm'\n      }, {\n        name: 'LOADER',\n        state: 'dialogs/loader'\n      }]\n    }, {\n      type: 'separator',\n      name: 'INTEGRATED COMPONENTS'\n    }, {\n      name: 'Material Kits',\n      type: 'dropDown',\n      tooltip: 'Material',\n      icon: 'favorite',\n      badges: [{\n        color: 'primary',\n        value: '60+'\n      }],\n      sub: [{\n        name: 'Form controls',\n        type: 'dropDown',\n        sub: [{\n          name: 'Autocomplete',\n          state: 'material/autocomplete'\n        }, {\n          name: 'Checkbox',\n          state: 'material/checkbox'\n        }, {\n          name: 'Datepicker',\n          state: 'material/datepicker'\n        }, {\n          name: 'Form Field',\n          state: 'material/form-field'\n        }, {\n          name: 'Input Field',\n          state: 'material/input-field'\n        }, {\n          name: 'Radio Button',\n          state: 'material/radio-button'\n        }, {\n          name: 'Select',\n          state: 'material/select'\n        }, {\n          name: 'Slider',\n          state: 'material/slider'\n        }, {\n          name: 'Slider Toggle',\n          state: 'material/slider-toggle'\n        }]\n      }, {\n        name: 'Navigation',\n        type: 'dropDown',\n        sub: [{\n          name: 'Menu',\n          state: 'material/menu'\n        }, {\n          name: 'Sidenav',\n          state: 'material/sidenav'\n        }, {\n          name: 'Toolbar',\n          state: 'material/toolbar'\n        }]\n      }, {\n        name: 'Layout',\n        type: 'dropDown',\n        sub: [{\n          name: 'Card',\n          state: 'material/card'\n        }, {\n          name: 'Divider',\n          state: 'material/divider'\n        }, {\n          name: 'Expansion Panel',\n          state: 'material/expansion-panel'\n        }, {\n          name: 'Grid',\n          state: 'material/grid'\n        }, {\n          name: 'List',\n          state: 'material/list'\n        }, {\n          name: 'Stepper',\n          state: 'material/stepper'\n        }, {\n          name: 'Tab',\n          state: 'material/tab-group'\n        }, {\n          name: 'Tree',\n          state: 'material/tree'\n        }]\n      }, {\n        name: 'Buttons & Indicators',\n        type: 'dropDown',\n        sub: [{\n          name: 'BUTTONS',\n          state: 'material/buttons'\n        }, {\n          name: 'Button Toggle',\n          state: 'material/button-toggle'\n        }, {\n          name: 'Badge',\n          state: 'material/badge'\n        }, {\n          name: 'Chips',\n          state: 'material/chips'\n        }, {\n          name: 'Icons',\n          state: 'material/icons'\n        }, {\n          name: 'Progress Spinner',\n          state: 'material/progress-spinner'\n        }, {\n          name: 'Progress Bar',\n          state: 'material/progress-bar'\n        }, {\n          name: 'Ripples',\n          state: 'material/ripples'\n        }]\n      }, {\n        name: 'Popups & Modals',\n        type: 'dropDown',\n        sub: [{\n          name: 'Tooltip',\n          state: 'material/tooltip'\n        }, {\n          name: 'Bottom Sheet',\n          state: 'material/bottom-sheet'\n        }, {\n          name: 'Dialog',\n          state: 'material/dialog'\n        }, {\n          name: 'Snackbar',\n          state: 'material/snackbar'\n        }]\n      }, {\n        name: 'Data Table',\n        type: 'dropDown',\n        sub: [{\n          name: 'paginator',\n          state: 'material/paginator'\n        }, {\n          name: 'Sort Header',\n          state: 'material/sort-header'\n        }, {\n          name: 'Table',\n          state: 'material/table'\n        }]\n      }]\n    }, {\n      name: 'CALENDAR',\n      type: 'link',\n      icon: 'date_range',\n      state: 'calendar'\n    }, {\n      name: 'FORMS',\n      type: 'dropDown',\n      tooltip: 'Forms',\n      icon: 'description',\n      sub: [{\n        name: 'BASIC',\n        state: 'forms/basic'\n      }, {\n        name: 'EDITOR',\n        state: 'forms/editor'\n      }, {\n        name: 'WIZARD',\n        state: 'forms/wizard'\n      }]\n    }, {\n      name: 'Table',\n      type: 'link',\n      tooltip: 'Table',\n      icon: 'format_line_spacing',\n      state: 'tables/mat-table'\n    }, {\n      name: 'TOUR',\n      type: 'link',\n      tooltip: 'Tour',\n      icon: 'flight_takeoff',\n      state: 'tour'\n    },\n    // {\n    //   name: 'MAP',\n    //   type: 'link',\n    //   tooltip: 'Map',\n    //   icon: 'add_location',\n    //   state: 'map'\n    // },\n    {\n      name: 'CHARTS',\n      type: 'dropDown',\n      tooltip: 'Charts',\n      icon: 'show_chart',\n      sub: [{\n        name: 'Chart js',\n        state: 'charts'\n      }, {\n        name: 'eChart',\n        type: 'dropDown',\n        state: 'chart',\n        sub: [{\n          name: 'Pie',\n          state: 'chart/pie'\n        }, {\n          name: 'Bar',\n          state: 'chart/bar'\n        }, {\n          name: 'Radar',\n          state: 'chart/radar'\n        }, {\n          name: \"Heatmap\",\n          state: \"chart/heatmap\"\n        }]\n      }]\n    }, {\n      name: 'OTHER COMPONENTS',\n      type: 'separator'\n    }, {\n      name: 'Page Layouts',\n      type: 'dropDown',\n      icon: 'view_carousel',\n      sub: [{\n        name: 'Left sidebar card',\n        state: 'page-layouts/left-sidebar-card'\n      }, {\n        name: 'Right sidebar card',\n        state: 'page-layouts/right-sidebar-card'\n      }, {\n        name: 'Full width card',\n        state: 'page-layouts/full-width-card'\n      }, {\n        name: 'Full width card tab',\n        state: 'page-layouts/full-width-card-tab'\n      }, {\n        name: 'Full width plain',\n        state: 'page-layouts/full-width-plain'\n      }, {\n        name: 'Left sidebar plain',\n        state: 'page-layouts/left-sidebar-plain'\n      }]\n    }, {\n      name: 'SESSIONS',\n      type: 'dropDown',\n      tooltip: 'Pages',\n      icon: 'view_carousel',\n      sub: [{\n        name: 'SIGNUP',\n        state: 'sessions/signup'\n      }, {\n        name: 'Signup 2',\n        state: 'sessions/signup2'\n      }, {\n        name: 'Signup 3',\n        state: 'sessions/signup3'\n      }, {\n        name: 'Signup 4',\n        state: 'sessions/signup4'\n      }, {\n        name: 'SIGNIN',\n        state: 'sessions/signin'\n      }, {\n        name: 'Signin 2',\n        state: 'sessions/signin2'\n      }, {\n        name: 'Signin 3',\n        state: 'sessions/signin3'\n      }, {\n        name: 'Signin 4',\n        state: 'sessions/signin4'\n      }, {\n        name: 'FORGOT',\n        state: 'sessions/forgot-password'\n      }, {\n        name: 'LOCKSCREEN',\n        state: 'sessions/lockscreen'\n      }, {\n        name: 'NOTFOUND',\n        state: 'sessions/404'\n      }, {\n        name: 'ERROR',\n        state: 'sessions/error'\n      }]\n    }, {\n      name: 'Utilities',\n      type: 'dropDown',\n      icon: 'format_list_bulleted',\n      sub: [{\n        name: 'Color',\n        state: 'utilities/color'\n      }, {\n        name: 'Typography',\n        state: 'utilities/typography'\n      }]\n    }, {\n      name: 'OTHERS',\n      type: 'dropDown',\n      tooltip: 'Others',\n      icon: 'blur_on',\n      sub: [{\n        name: 'GALLERY',\n        state: 'others/gallery'\n      }, {\n        name: 'PRICINGS',\n        state: 'others/pricing'\n      }, {\n        name: 'USERS',\n        state: 'others/users'\n      }, {\n        name: 'BLANK',\n        state: 'others/blank'\n      }]\n    }, {\n      name: 'MATICONS',\n      type: 'link',\n      tooltip: 'Material Icons',\n      icon: 'store',\n      state: 'icons'\n    }, {\n      name: 'Multi Level',\n      type: 'dropDown',\n      tooltip: 'Multi Level',\n      icon: 'format_align_center',\n      sub: [{\n        name: 'Level Two',\n        state: 'fake-4'\n      }, {\n        name: 'Level Two',\n        type: 'dropDown',\n        state: 'fake-1',\n        sub: [{\n          name: 'Level Three',\n          state: 'fake-2'\n        }, {\n          name: 'Level Three',\n          type: 'dropDown',\n          state: 'fake-3',\n          sub: [{\n            name: 'Level Four',\n            state: 'fake-3'\n          }, {\n            name: 'Level Four',\n            type: 'dropDown',\n            state: 'fake-4',\n            sub: [{\n              name: 'Level Five',\n              state: 'fake-3'\n            }, {\n              name: 'Level Five',\n              type: 'link'\n            }]\n          }]\n        }]\n      }, {\n        name: 'Level Two',\n        state: 'fake-5'\n      }]\n    }, {\n      name: 'DOC',\n      type: 'extLink',\n      tooltip: 'Documentation',\n      icon: 'library_books',\n      state: 'http://demos.ui-lib.com/egret-doc/'\n    }];\n    this.plainMenu = [{\n      name: 'DASHBOARD',\n      type: 'dropDown',\n      tooltip: 'Dashboard',\n      icon: 'dashboard',\n      sub: [{\n        name: 'Analytics',\n        state: 'dashboard/analytics'\n      }, {\n        name: 'Learning Management',\n        state: 'dashboard/learning-management'\n      }, {\n        name: 'Analytics Alt',\n        state: 'dashboard/analytics-alt'\n      }, {\n        name: 'Cryptocurrency',\n        state: 'dashboard/crypto'\n      }, {\n        name: 'Dark Cards',\n        state: 'dashboard/dark'\n      }]\n    }, {\n      name: 'CRUD Table',\n      type: 'link',\n      tooltip: 'CRUD Table',\n      icon: 'list',\n      state: 'cruds/ngx-table'\n    }, {\n      name: 'ECOMMERCE',\n      type: 'dropDown',\n      tooltip: 'Shop',\n      icon: 'shopping_cart',\n      sub: [{\n        name: 'PRODUCTS',\n        state: 'shop'\n      }, {\n        name: 'PRODUCT DETAILS',\n        state: 'shop/products/5a9ae2106f155194e5c95d67'\n      }, {\n        name: 'CART',\n        state: 'shop/cart'\n      }, {\n        name: 'CHECKOUT',\n        state: 'shop/checkout'\n      }]\n    }, {\n      name: 'INBOX',\n      type: 'link',\n      icon: 'inbox',\n      state: 'inbox',\n      badges: [{\n        color: 'primary',\n        value: '4'\n      }]\n    }, {\n      name: 'Invoice',\n      type: 'dropDown',\n      icon: 'receipt',\n      sub: [{\n        name: 'List',\n        state: 'invoice/list'\n      }, {\n        name: 'View / Edit',\n        state: 'invoice/5a9ae2106518248b68251fd1'\n      }]\n    }, {\n      name: 'CHAT',\n      type: 'link',\n      tooltip: 'Chat',\n      icon: 'chat',\n      state: 'chat',\n      badges: [{\n        color: 'warn',\n        value: '1'\n      }]\n    }, {\n      name: 'CALENDAR',\n      type: 'link',\n      tooltip: 'Calendar',\n      icon: 'date_range',\n      state: 'calendar'\n    }, {\n      name: 'Todo',\n      type: 'link',\n      tooltip: 'Todo',\n      icon: 'center_focus_strong',\n      state: 'todo/list'\n    }, {\n      name: 'DIALOGS',\n      type: 'dropDown',\n      tooltip: 'Dialogs',\n      icon: 'filter_none',\n      sub: [{\n        name: 'CONFIRM',\n        state: 'dialogs/confirm'\n      }, {\n        name: 'LOADER',\n        state: 'dialogs/loader'\n      }]\n    }, {\n      name: 'Material Kits',\n      type: 'dropDown',\n      tooltip: 'Material',\n      icon: 'favorite',\n      badges: [{\n        color: 'primary',\n        value: '60+'\n      }],\n      sub: [{\n        name: 'Form controls',\n        type: 'dropDown',\n        sub: [{\n          name: 'Autocomplete',\n          state: 'material/autocomplete'\n        }, {\n          name: 'Checkbox',\n          state: 'material/checkbox'\n        }, {\n          name: 'Datepicker',\n          state: 'material/datepicker'\n        }, {\n          name: 'Form Field',\n          state: 'material/form-field'\n        }, {\n          name: 'Input Field',\n          state: 'material/input-field'\n        }, {\n          name: 'Radio Button',\n          state: 'material/radio-button'\n        }, {\n          name: 'Select',\n          state: 'material/select'\n        }, {\n          name: 'Slider',\n          state: 'material/slider'\n        }, {\n          name: 'Slider Toggle',\n          state: 'material/slider-toggle'\n        }]\n      }, {\n        name: 'Navigation',\n        type: 'dropDown',\n        sub: [{\n          name: 'Menu',\n          state: 'material/menu'\n        }, {\n          name: 'Sidenav',\n          state: 'material/sidenav'\n        }, {\n          name: 'Toolbar',\n          state: 'material/toolbar'\n        }]\n      }, {\n        name: 'Layout',\n        type: 'dropDown',\n        sub: [{\n          name: 'Card',\n          state: 'material/card'\n        }, {\n          name: 'Divider',\n          state: 'material/divider'\n        }, {\n          name: 'Expansion Panel',\n          state: 'material/expansion-panel'\n        }, {\n          name: 'Grid',\n          state: 'material/grid'\n        }, {\n          name: 'List',\n          state: 'material/list'\n        }, {\n          name: 'Stepper',\n          state: 'material/stepper'\n        }, {\n          name: 'Tab',\n          state: 'material/tab-group'\n        }, {\n          name: 'Tree',\n          state: 'material/tree'\n        }]\n      }, {\n        name: 'Buttons & Indicators',\n        type: 'dropDown',\n        sub: [{\n          name: 'BUTTONS',\n          state: 'material/buttons'\n        }, {\n          name: 'Button Toggle',\n          state: 'material/button-toggle'\n        }, {\n          name: 'Badge',\n          state: 'material/badge'\n        }, {\n          name: 'Chips',\n          state: 'material/chips'\n        }, {\n          name: 'Icons',\n          state: 'material/icons'\n        }, {\n          name: 'Progress Spinner',\n          state: 'material/progress-spinner'\n        }, {\n          name: 'Progress Bar',\n          state: 'material/progress-bar'\n        }, {\n          name: 'Ripples',\n          state: 'material/ripples'\n        }]\n      }, {\n        name: 'Popups & Modals',\n        type: 'dropDown',\n        sub: [{\n          name: 'Tooltip',\n          state: 'material/tooltip'\n        }, {\n          name: 'Bottom Sheet',\n          state: 'material/bottom-sheet'\n        }, {\n          name: 'Dialog',\n          state: 'material/dialog'\n        }, {\n          name: 'Snackbar',\n          state: 'material/snackbar'\n        }]\n      }, {\n        name: 'Data Table',\n        type: 'dropDown',\n        sub: [{\n          name: 'paginator',\n          state: 'material/paginator'\n        }, {\n          name: 'Sort Header',\n          state: 'material/sort-header'\n        }, {\n          name: 'Table',\n          state: 'material/table'\n        }]\n      }]\n    }, {\n      name: 'FORMS',\n      type: 'dropDown',\n      tooltip: 'Forms',\n      icon: 'description',\n      sub: [{\n        name: 'BASIC',\n        state: 'forms/basic'\n      }, {\n        name: 'EDITOR',\n        state: 'forms/editor'\n      }, {\n        name: 'WIZARD',\n        state: 'forms/wizard'\n      }]\n    }, {\n      name: 'Table',\n      type: 'link',\n      tooltip: 'Table',\n      icon: 'format_line_spacing',\n      state: 'tables/mat-table'\n    }, {\n      name: 'PROFILE',\n      type: 'dropDown',\n      tooltip: 'Profile',\n      icon: 'person',\n      badges: [{\n        color: 'primary',\n        value: '2'\n      }],\n      sub: [{\n        name: 'OVERVIEW',\n        state: 'profile/overview'\n      }, {\n        name: 'SETTINGS',\n        state: 'profile/settings'\n      }, {\n        name: 'BLANK',\n        state: 'profile/blank'\n      }]\n    }, {\n      name: 'TOUR',\n      type: 'link',\n      tooltip: 'Tour',\n      icon: 'flight_takeoff',\n      state: 'tour'\n    },\n    // {\n    //   name: 'MAP',\n    //   type: 'link',\n    //   tooltip: 'Map',\n    //   icon: 'add_location',\n    //   state: 'map'\n    // },\n    {\n      name: 'CHARTS',\n      type: 'dropDown',\n      tooltip: 'Charts',\n      icon: 'show_chart',\n      sub: [{\n        name: 'Chart js',\n        state: 'charts'\n      }, {\n        name: 'eChart',\n        type: 'dropDown',\n        state: 'chart',\n        sub: [{\n          name: 'Pie',\n          state: 'chart/pie'\n        }, {\n          name: 'Bar',\n          state: 'chart/bar'\n        }, {\n          name: 'Radar',\n          state: 'chart/radar'\n        }, {\n          name: \"Heatmap\",\n          state: \"chart/heatmap\"\n        }]\n      }]\n    }, {\n      name: 'Page Layouts',\n      type: 'dropDown',\n      icon: 'view_carousel',\n      sub: [{\n        name: 'Left sidebar card',\n        state: 'page-layouts/left-sidebar-card'\n      }, {\n        name: 'Right sidebar card',\n        state: 'page-layouts/right-sidebar-card'\n      }, {\n        name: 'Full width card',\n        state: 'page-layouts/full-width-card'\n      }, {\n        name: 'Full width card tab',\n        state: 'page-layouts/full-width-card-tab'\n      }, {\n        name: 'Full width plain',\n        state: 'page-layouts/full-width-plain'\n      }, {\n        name: 'Left sidebar plain',\n        state: 'page-layouts/left-sidebar-plain'\n      }]\n    }, {\n      name: 'SESSIONS',\n      type: 'dropDown',\n      tooltip: 'Pages',\n      icon: 'view_carousel',\n      sub: [{\n        name: 'SIGNUP',\n        state: 'sessions/signup'\n      }, {\n        name: 'Signup 2',\n        state: 'sessions/signup2'\n      }, {\n        name: 'Signup 3',\n        state: 'sessions/signup3'\n      }, {\n        name: 'Signup 4',\n        state: 'sessions/signup4'\n      }, {\n        name: 'SIGNIN',\n        state: 'sessions/signin'\n      }, {\n        name: 'Signin 2',\n        state: 'sessions/signin2'\n      }, {\n        name: 'Signin 3',\n        state: 'sessions/signin3'\n      }, {\n        name: 'Signin 4',\n        state: 'sessions/signin4'\n      }, {\n        name: 'FORGOT',\n        state: 'sessions/forgot-password'\n      }, {\n        name: 'LOCKSCREEN',\n        state: 'sessions/lockscreen'\n      }, {\n        name: 'NOTFOUND',\n        state: 'sessions/404'\n      }, {\n        name: 'ERROR',\n        state: 'sessions/error'\n      }]\n    }, {\n      name: 'Utilities',\n      type: 'dropDown',\n      icon: 'format_list_bulleted',\n      sub: [{\n        name: 'Color',\n        state: 'utilities/color'\n      }, {\n        name: 'Typography',\n        state: 'utilities/typography'\n      }]\n    }, {\n      name: 'OTHERS',\n      type: 'dropDown',\n      tooltip: 'Others',\n      icon: 'blur_on',\n      sub: [{\n        name: 'GALLERY',\n        state: 'others/gallery'\n      }, {\n        name: 'PRICINGS',\n        state: 'others/pricing'\n      }, {\n        name: 'USERS',\n        state: 'others/users'\n      }, {\n        name: 'BLANK',\n        state: 'others/blank'\n      }]\n    }, {\n      name: 'MATICONS',\n      type: 'link',\n      tooltip: 'Material Icons',\n      icon: 'store',\n      state: 'icons'\n    }, {\n      name: 'Multi Level',\n      type: 'dropDown',\n      tooltip: 'Multi Level',\n      icon: 'format_align_center',\n      sub: [{\n        name: 'Level Two',\n        state: 'fake-4'\n      }, {\n        name: 'Level Two',\n        type: 'dropDown',\n        state: 'fake-1',\n        sub: [{\n          name: 'Level Three',\n          state: 'fake-2'\n        }, {\n          name: 'Level Three',\n          type: 'dropDown',\n          state: 'fake-3',\n          sub: [{\n            name: 'Level Four',\n            state: 'fake-3'\n          }, {\n            name: 'Level Four',\n            type: 'dropDown',\n            state: 'fake-4',\n            sub: [{\n              name: 'Level Five',\n              state: 'fake-3'\n            }, {\n              name: 'Level Five',\n              type: 'link'\n            }]\n          }]\n        }]\n      }, {\n        name: 'Level Two',\n        state: 'fake-5'\n      }]\n    }, {\n      name: 'DOC',\n      type: 'extLink',\n      tooltip: 'Documentation',\n      icon: 'library_books',\n      state: 'http://demos.ui-lib.com/egret-doc/'\n    }];\n    // Icon menu TITLE at the very top of navigation.\n    // This title will appear if any icon type item is present in menu.\n    this.iconTypeMenuTitle = 'Frequently Accessed';\n    // sets iconMenu as default;\n    this.menuItems = new BehaviorSubject(this.iconMenu);\n    // navigation component has subscribed to this Observable\n    this.menuItems$ = this.menuItems.asObservable();\n  }\n  // Customizer component uses this method to change menu.\n  // You can remove this method and customizer component.\n  // Or you can customize this method to supply different menu for\n  // different user type.\n  publishNavigationChange(menuType) {\n    switch (menuType) {\n      case 'separator-menu':\n        this.menuItems.next(this.separatorMenu);\n        break;\n      case 'icon-menu':\n        this.menuItems.next(this.iconMenu);\n        break;\n      default:\n        this.menuItems.next(this.plainMenu);\n    }\n  }\n  static #_ = this.ɵfac = function NavigationService_Factory(t) {\n    return new (t || NavigationService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: NavigationService,\n    factory: NavigationService.ɵfac\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "NavigationService", "constructor", "iconMenu", "name", "type", "tooltip", "icon", "state", "sub", "badges", "color", "value", "separatorMenu", "plainMenu", "iconTypeMenuTitle", "menuItems", "menuItems$", "asObservable", "publishNavigationChange", "menuType", "next", "_", "_2", "factory", "ɵfac"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\shared\\services\\navigation.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\n\ninterface IMenuItem {\n  type: 'link' | 'dropDown' | 'icon' | 'separator' | 'extLink';\n  name?: string; // Used as display text for item and title for separator type\n  state?: string; // Router state\n  icon?: string; // Material icon name\n  svgIcon?: string; // UI Lib icon name\n  tooltip?: string; // Tooltip text\n  disabled?: boolean; // If true, item will not be appeared in sidenav.\n  sub?: IChildItem[]; // Dropdown items\n  badges?: IBadge[];\n}\ninterface IChildItem {\n  type?: string;\n  name: string; // Display text\n  state?: string; // Router state\n  icon?: string;  // Material icon name\n  svgIcon?: string; // UI Lib icon name\n  sub?: IChildItem[];\n}\n\ninterface IBadge {\n  color: string; // primary/accent/warn/hex color codes(#fff000)\n  value: string; // Display text\n}\n\n@Injectable()\nexport class NavigationService {\n  iconMenu: IMenuItem[] = [\n    {\n      name: 'HOME',\n      type: 'icon',\n      tooltip: 'Home',\n      icon: 'home',\n      state: 'home'\n    },\n    {\n      name: 'PROFILE',\n      type: 'icon',\n      tooltip: 'Profile',\n      icon: 'person',\n      state: 'profile/overview'\n    },\n    {\n      name: 'TOUR',\n      type: 'icon',\n      tooltip: 'Tour',\n      icon: 'flight_takeoff',\n      state: 'tour'\n    },\n    {\n      name: 'CHAT',\n      type: 'icon',\n      tooltip: 'Chat',\n      icon: 'chat',\n      state: 'chat',\n    },\n    {\n      type: 'separator',\n      name: 'Main Items'\n    },\n    {\n      name: 'Login',\n      type: 'link',\n      tooltip: 'Login',\n      icon: 'person'\n    },\n    {\n      name: 'DASHBOARD',\n      type: 'dropDown',\n      tooltip: 'Dashboard',\n      icon: 'dashboard',\n      sub: [\n        { name: 'Analytics', state: 'dashboard/analytics' },\n        { name: 'Learning Management', state: 'dashboard/learning-management' },\n        { name: 'Analytics Alt', state: 'dashboard/analytics-alt' },\n        { name: 'Cryptocurrency', state: 'dashboard/crypto' },\n        { name: 'Dark Cards', state: 'dashboard/dark' },\n      ]\n    },\n    {\n      name: 'My Practice',\n      type: 'dropDown',\n      icon: 'list',\n      sub: [\n        { name: 'Codes', \n          type: 'dropDown',\n          icon: 'list',\n          sub: [\n            {name: 'ICD Codes', state: 'tables/mat-table'},\n            {name: 'CPT Codes', state: 'cruds/ngx-table'},\n            {name: 'HCPCS Codes', state: 'material/table'}\n          ]\n        },\n        {name: 'Basic Table', state: 'tables/mat-table'},\n        {name: 'Places Of Service', state: 'cruds/ngx-table'},\n        {name: 'Upload Claims', state: 'material/table'}\n      ]\n    },   \n    {\n      name: 'Claims',\n      type: 'dropDown',\n      icon: 'list',\n      sub: [        \n        {name: 'Select Practice', state: 'tables/mat-table'},\n        {name: 'New', state: 'cruds/ngx-table'},\n        {name: 'History', state: 'material/table'}\n      ]\n    },  \n\t\n\t{\n      name: 'Manage',\n      type: 'dropDown',\n      icon: 'list',\n      sub: [\n\t\t{name: 'Users', state: 'tables/mat-table'},\n        { name: 'Codes', \n          type: 'dropDown',\n          icon: 'list',\n          sub: [\n            {name: 'ICD Codes', state: 'tables/mat-table'},\n            {name: 'CPT Codes', state: 'cruds/ngx-table'},\n            {name: 'HCPCS Codes', state: 'material/table'}\n          ]\n        },\n\t\t{ name: 'Fee Schedules', \n          type: 'dropDown',\n          icon: 'list',\n          sub: [\n            {name: 'Schedules', state: 'tables/mat-table'},\n            {name: 'CPT Pricing', state: 'cruds/ngx-table'}\n          ]\n        },\n\t\t{ name: 'Setup', \n          type: 'dropDown',\n          icon: 'list',\n          sub: [\n            {name: 'Providers', state: 'tables/mat-table'},\n            {name: 'Practices', state: 'cruds/ngx-table'},\n\t\t\t{name: 'Clients', state: 'cruds/ngx-table'},\n\t\t\t{name: 'Networks', state: 'cruds/ngx-table'},\n\t\t\t{name: 'Payers', state: 'cruds/ngx-table'}\n          ]\n        },\n        { name: 'Link Practices', \n          type: 'dropDown',\n          icon: 'list',\n          sub: [\n            {name: 'Providers->Practices', state: 'tables/mat-table'},\n            {name: 'Payers->Practices', state: 'cruds/ngx-table'}\n          ]\n        },\n      ]\n    },   \n\t{\n      name: 'Batch',\n      type: 'dropDown',\n      icon: 'list',\n      sub: [        \n        {name: 'List', state: 'tables/mat-table'},\n        {name: 'Upload', state: 'cruds/ngx-table'},\n        {name: 'Download', state: 'material/table'},\n\t\t{ name: 'Import', \n          type: 'dropDown',\n          icon: 'list',\n          sub: [\n            {name: 'Practice Setup', state: 'tables/mat-table'},\n            {name: 'Claims(X12)', state: 'cruds/ngx-table'}\n          ]\n        },\n      ]\n    },  \n\t{\n      name: 'Reports',\n      type: 'dropDown',\n      icon: 'receipt',\n      sub: [\n        { name: 'Report1', state: 'invoice/list' },\n        { name: 'Report2', state: 'invoice/5a9ae2106518248b68251fd1' },\n        { name: 'Report3', state: 'invoice/list' },\n        { name: 'Report4', state: 'invoice/list' },\n\t\t{ name: 'Report5', state: 'invoice/list' },\n      ]\n    },\n    {\n      name: 'Invoice',\n      type: 'dropDown',\n      icon: 'receipt',\n      sub: [\n        { name: 'Generate', state: 'invoice/list' },\n        { name: 'View', state: 'invoice/5a9ae2106518248b68251fd1' },\n        { name: 'Pay', state: 'invoice/list' },\n        { name: 'Adjust', state: 'invoice/list' }\n      ]\n    },\n    {\n      name: 'ECOMMERCE',\n      type: 'dropDown',\n      tooltip: 'Shop',\n      icon: 'shopping_cart',\n      sub: [\n        { name: 'PRODUCTS', state: 'shop' },\n        { name: 'PRODUCT DETAILS', state: 'shop/products/5a9ae2106f155194e5c95d67' },\n        { name: 'CART', state: 'shop/cart' },\n        { name: 'CHECKOUT', state: 'shop/checkout' }\n      ]\n    },\n    {\n      name: 'CHAT',\n      type: 'link',\n      tooltip: 'Chat',\n      icon: 'chat',\n      state: 'chat',\n      badges: [{ color: 'warn', value: '1' }]\n    },\n    {\n      name: 'INBOX',\n      type: 'link',\n      icon: 'inbox',\n      state: 'inbox',\n      badges: [{ color: 'primary', value: '4' }]\n    },\n    {\n      name: 'CALENDAR',\n      type: 'link',\n      tooltip: 'Calendar',\n      icon: 'date_range',\n      state: 'calendar'\n    },\n    {\n      name: 'Todo',\n      type: 'link',\n      tooltip: 'Todo',\n      icon: 'center_focus_strong',\n      state: 'todo/list'\n    },\n    {\n      name: 'DIALOGS',\n      type: 'dropDown',\n      tooltip: 'Dialogs',\n      icon: 'filter_none',\n      sub: [\n        { name: 'CONFIRM', state: 'dialogs/confirm' },\n        { name: 'LOADER', state: 'dialogs/loader' }\n      ]\n    },\n    {\n      name: 'Material Kits',\n      type: 'dropDown',\n      tooltip: 'Material',\n      icon: 'favorite',\n      badges: [{ color: 'primary', value: '60+' }],\n      sub: [\n        {\n          name: 'Form controls',\n          type: 'dropDown',\n          sub: [\n            { name: 'Autocomplete', state: 'material/autocomplete' },\n            { name: 'Checkbox', state: 'material/checkbox' },\n            { name: 'Datepicker', state: 'material/datepicker' },\n            { name: 'Form Field', state: 'material/form-field' },\n            { name: 'Input Field', state: 'material/input-field' },\n            { name: 'Radio Button', state: 'material/radio-button' },\n            { name: 'Select', state: 'material/select' },\n            { name: 'Slider', state: 'material/slider' },\n            { name: 'Slider Toggle', state: 'material/slider-toggle' }\n          ]\n        },\n        {\n          name: 'Navigation',\n          type: 'dropDown',\n          sub: [\n            { name: 'Menu', state: 'material/menu' },\n            { name: 'Sidenav', state: 'material/sidenav' },\n            { name: 'Toolbar', state: 'material/toolbar' }\n          ]\n        },\n        {\n          name: 'Layout',\n          type: 'dropDown',\n          sub: [\n            { name: 'Card', state: 'material/card' },\n            { name: 'Divider', state: 'material/divider' },\n            { name: 'Expansion Panel', state: 'material/expansion-panel' },\n            { name: 'Grid', state: 'material/grid' },\n            { name: 'List', state: 'material/list' },\n            { name: 'Stepper', state: 'material/stepper' },\n            { name: 'Tab', state: 'material/tab-group' },\n            { name: 'Tree', state: 'material/tree' }\n          ]\n        },\n        {\n          name: 'Buttons & Indicators',\n          type: 'dropDown',\n          sub: [\n            { name: 'BUTTONS', state: 'material/buttons' },\n            { name: 'Button Toggle', state: 'material/button-toggle' },\n            { name: 'Badge', state: 'material/badge' },\n            { name: 'Chips', state: 'material/chips' },\n            { name: 'Icons', state: 'material/icons' },\n            { name: 'Progress Spinner', state: 'material/progress-spinner' },\n            { name: 'Progress Bar', state: 'material/progress-bar' },\n            { name: 'Ripples', state: 'material/ripples' }\n          ]\n        },\n        {\n          name: 'Popups & Modals',\n          type: 'dropDown',\n          sub: [\n            { name: 'Tooltip', state: 'material/tooltip' },\n            { name: 'Bottom Sheet', state: 'material/bottom-sheet' },\n            { name: 'Dialog', state: 'material/dialog' },\n            { name: 'Snackbar', state: 'material/snackbar' }\n          ]\n        },\n        {\n          name: 'Data Table',\n          type: 'dropDown',\n          sub: [\n            { name: 'paginator', state: 'material/paginator' },\n            { name: 'Sort Header', state: 'material/sort-header' },\n            { name: 'Table', state: 'material/table' }\n          ]\n        }        \n      ]\n    },\n    {\n      name: 'FORMS',\n      type: 'dropDown',\n      tooltip: 'Forms',\n      icon: 'description',\n      sub: [\n        { name: 'BASIC', state: 'forms/basic' },\n        { name: 'EDITOR', state: 'forms/editor' },\n        { name: 'WIZARD', state: 'forms/wizard' }\n      ]\n    },\n    {\n      name: 'PROFILE',\n      type: 'dropDown',\n      tooltip: 'Profile',\n      icon: 'person',\n      badges: [{ color: 'primary', value: '2' }],\n      sub: [\n        { name: 'OVERVIEW', state: 'profile/overview' },\n        { name: 'SETTINGS', state: 'profile/settings' },\n        { name: 'BLANK', state: 'profile/blank' }\n      ]\n    },\n    {\n      name: 'TOUR',\n      type: 'link',\n      tooltip: 'Tour',\n      icon: 'flight_takeoff',\n      state: 'tour'\n    },\n    // {\n    //   name: 'MAP',\n    //   type: 'link',\n    //   tooltip: 'Map',\n    //   icon: 'add_location',\n    //   state: 'map'\n    // },\n\n    {\n      name: 'CHARTS',\n      type: 'dropDown',\n      tooltip: 'Charts',\n      icon: 'show_chart',\n      sub: [\n        { name: 'Chart js', state: 'charts' },\n        {\n          name: 'eChart',\n          type: 'dropDown',\n          state: 'chart',\n          sub: [\n            { name: 'Pie', state: 'chart/pie' },\n            { name: 'Bar', state: 'chart/bar' },\n            { name: 'Radar', state: 'chart/radar' },\n            { name: \"Heatmap\", state: \"chart/heatmap\" },\n          ]\n        }\n      ]\n    },\n    {\n      name: 'Page Layouts',\n      type: 'dropDown',\n      icon: 'view_carousel',\n      sub: [\n        { name: 'Left sidebar card', state: 'page-layouts/left-sidebar-card' },\n        { name: 'Right sidebar card', state: 'page-layouts/right-sidebar-card' },\n        { name: 'Full width card', state: 'page-layouts/full-width-card' },\n        { name: 'Full width card tab', state: 'page-layouts/full-width-card-tab' },\n\n        { name: 'Full width plain', state: 'page-layouts/full-width-plain' },\n        { name: 'Left sidebar plain', state: 'page-layouts/left-sidebar-plain' }\n      ]\n    },\n    {\n      name: 'SESSIONS',\n      type: 'dropDown',\n      tooltip: 'Pages',\n      icon: 'view_carousel',\n      sub: [\n        { name: 'SIGNUP', state: 'sessions/signup' },\n        { name: 'Signup 2', state: 'sessions/signup2' },\n        { name: 'Signup 3', state: 'sessions/signup3' },\n        { name: 'Signup 4', state: 'sessions/signup4' },\n        { name: 'SIGNIN', state: 'sessions/signin' },\n        { name: 'Signin 2', state: 'sessions/signin2' },\n        { name: 'Signin 3', state: 'sessions/signin3' },\n        { name: 'Signin 4', state: 'sessions/signin4' },\n        { name: 'FORGOT', state: 'sessions/forgot-password' },\n        { name: 'LOCKSCREEN', state: 'sessions/lockscreen' },\n        { name: 'NOTFOUND', state: 'sessions/404' },\n        { name: 'ERROR', state: 'sessions/error' }\n      ]\n    },\n    {\n      name: 'Utilities',\n      type: 'dropDown',\n      icon: 'format_list_bulleted',\n      sub: [\n        { name: 'Color', state: 'utilities/color' },\n        { name: 'Typography', state: 'utilities/typography' }\n      ]\n    },\n    {\n      name: 'OTHERS',\n      type: 'dropDown',\n      tooltip: 'Others',\n      icon: 'blur_on',\n      sub: [\n        { name: 'GALLERY', state: 'others/gallery' },\n        { name: 'PRICINGS', state: 'others/pricing' },\n        { name: 'USERS', state: 'others/users' },\n        { name: 'BLANK', state: 'others/blank' }\n      ]\n    },\n    {\n      name: 'MATICONS',\n      type: 'link',\n      tooltip: 'Material Icons',\n      icon: 'store',\n      state: 'icons'\n    },\n    {\n      name: 'Multi Level',\n      type: 'dropDown',\n      tooltip: 'Multi Level',\n      icon: 'format_align_center',\n      sub: [\n        { name: 'Level Two', state: 'fake-4' },\n        {\n          name: 'Level Two',\n          type: 'dropDown',\n          sub: [\n            { name: 'Level Three', state: 'fake-2' },\n            { \n              name: 'Level Three', \n              type: 'dropDown',\n              sub: [\n                { name: 'Level Four', state: 'fake-3' },\n                { \n                  name: 'Level Four', \n                  type: 'dropDown',\n                  sub: [\n                    { name: 'Level Five', state: 'fake-3' },\n                    { name: 'Level Five', type: 'link', state: 'fake-3' }\n                  ]\n                }\n              ]\n            }\n          ]\n        },\n        { name: 'Level Two', state: 'fake-5' }\n      ]\n    },\n    {\n      name: 'DOC',\n      type: 'extLink',\n      tooltip: 'Documentation',\n      icon: 'library_books',\n      state: 'http://demos.ui-lib.com/egret-doc/'\n    }\n  ];\n\n  separatorMenu: IMenuItem[] = [\n    {\n      type: 'separator',\n      name: 'CUSTOM COMPONENTS'\n    },\n    {\n      name: 'DASHBOARD',\n      type: 'dropDown',\n      tooltip: 'Dashboard',\n      icon: 'dashboard',\n      sub: [\n        { name: 'Analytics', state: 'dashboard/analytics' },\n        { name: 'Learning Management', state: 'dashboard/learning-management' },\n        { name: 'Analytics Alt', state: 'dashboard/analytics-alt' },\n        { name: 'Cryptocurrency', state: 'dashboard/crypto' },\n        { name: 'Dark Cards', state: 'dashboard/dark' },\n      ]\n    },\n    {\n      name: 'CRUD Table',\n      type: 'link',\n      tooltip: 'CRUD Table',\n      icon: 'list',\n      state: 'cruds/ngx-table'\n    },\n    {\n      name: 'ECOMMERCE',\n      type: 'dropDown',\n      tooltip: 'Shop',\n      icon: 'shopping_cart',\n      sub: [\n        { name: 'PRODUCTS', state: 'shop' },\n        { name: 'PRODUCT DETAILS', state: 'shop/products/5a9ae2106f155194e5c95d67' },\n        { name: 'CART', state: 'shop/cart' },\n        { name: 'CHECKOUT', state: 'shop/checkout' }\n      ]\n    },\n    {\n      name: 'INBOX',\n      type: 'link',\n      icon: 'inbox',\n      state: 'inbox',\n      badges: [{ color: 'primary', value: '4' }]\n    },\n    {\n      name: 'Invoice',\n      type: 'dropDown',\n      icon: 'receipt',\n      sub: [\n        { name: 'List', state: 'invoice/list' },\n        { name: 'View / Edit', state: 'invoice/5a9ae2106518248b68251fd1' },\n      ]\n    },\n    {\n      name: 'PROFILE',\n      type: 'dropDown',\n      tooltip: 'Profile',\n      icon: 'person',\n      badges: [{ color: 'primary', value: '2' }],\n      sub: [\n        { name: 'OVERVIEW', state: 'profile/overview' },\n        { name: 'SETTINGS', state: 'profile/settings' },\n        { name: 'BLANK', state: 'profile/blank' }\n      ]\n    },\n    {\n      name: 'CHAT',\n      type: 'link',\n      tooltip: 'Chat',\n      icon: 'chat',\n      state: 'chat',\n      badges: [{ color: 'warn', value: '1' }]\n    },\n    {\n      name: 'Todo',\n      type: 'link',\n      tooltip: 'Todo',\n      icon: 'center_focus_strong',\n      state: 'todo/list'\n    },\n    {\n      name: 'DIALOGS',\n      type: 'dropDown',\n      tooltip: 'Dialogs',\n      icon: 'filter_none',\n      sub: [\n        { name: 'CONFIRM', state: 'dialogs/confirm' },\n        { name: 'LOADER', state: 'dialogs/loader' }\n      ]\n    },\n    {\n      type: 'separator',\n      name: 'INTEGRATED COMPONENTS'\n    },\n    {\n      name: 'Material Kits',\n      type: 'dropDown',\n      tooltip: 'Material',\n      icon: 'favorite',\n      badges: [{ color: 'primary', value: '60+' }],\n      sub: [\n        {\n          name: 'Form controls',\n          type: 'dropDown',\n          sub: [\n            { name: 'Autocomplete', state: 'material/autocomplete' },\n            { name: 'Checkbox', state: 'material/checkbox' },\n            { name: 'Datepicker', state: 'material/datepicker' },\n            { name: 'Form Field', state: 'material/form-field' },\n            { name: 'Input Field', state: 'material/input-field' },\n            { name: 'Radio Button', state: 'material/radio-button' },\n            { name: 'Select', state: 'material/select' },\n            { name: 'Slider', state: 'material/slider' },\n            { name: 'Slider Toggle', state: 'material/slider-toggle' }\n          ]\n        },\n        {\n          name: 'Navigation',\n          type: 'dropDown',\n          sub: [\n            { name: 'Menu', state: 'material/menu' },\n            { name: 'Sidenav', state: 'material/sidenav' },\n            { name: 'Toolbar', state: 'material/toolbar' }\n          ]\n        },\n        {\n          name: 'Layout',\n          type: 'dropDown',\n          sub: [\n            { name: 'Card', state: 'material/card' },\n            { name: 'Divider', state: 'material/divider' },\n            { name: 'Expansion Panel', state: 'material/expansion-panel' },\n            { name: 'Grid', state: 'material/grid' },\n            { name: 'List', state: 'material/list' },\n            { name: 'Stepper', state: 'material/stepper' },\n            { name: 'Tab', state: 'material/tab-group' },\n            { name: 'Tree', state: 'material/tree' }\n          ]\n        },\n        {\n          name: 'Buttons & Indicators',\n          type: 'dropDown',\n          sub: [\n            { name: 'BUTTONS', state: 'material/buttons' },\n            { name: 'Button Toggle', state: 'material/button-toggle' },\n            { name: 'Badge', state: 'material/badge' },\n            { name: 'Chips', state: 'material/chips' },\n            { name: 'Icons', state: 'material/icons' },\n            { name: 'Progress Spinner', state: 'material/progress-spinner' },\n            { name: 'Progress Bar', state: 'material/progress-bar' },\n            { name: 'Ripples', state: 'material/ripples' }\n          ]\n        },\n        {\n          name: 'Popups & Modals',\n          type: 'dropDown',\n          sub: [\n            { name: 'Tooltip', state: 'material/tooltip' },\n            { name: 'Bottom Sheet', state: 'material/bottom-sheet' },\n            { name: 'Dialog', state: 'material/dialog' },\n            { name: 'Snackbar', state: 'material/snackbar' }\n          ]\n        },\n        {\n          name: 'Data Table',\n          type: 'dropDown',\n          sub: [\n            { name: 'paginator', state: 'material/paginator' },\n            { name: 'Sort Header', state: 'material/sort-header' },\n            { name: 'Table', state: 'material/table' }\n          ]\n        },\n      ]\n    },\n    {\n      name: 'CALENDAR',\n      type: 'link',\n      icon: 'date_range',\n      state: 'calendar'\n    },\n    {\n      name: 'FORMS',\n      type: 'dropDown',\n      tooltip: 'Forms',\n      icon: 'description',\n      sub: [\n        { name: 'BASIC', state: 'forms/basic' },\n        { name: 'EDITOR', state: 'forms/editor' },\n        { name: 'WIZARD', state: 'forms/wizard' }\n      ]\n    },\n    {\n      name: 'Table',\n      type: 'link',\n      tooltip: 'Table',\n      icon: 'format_line_spacing',\n      state: 'tables/mat-table',\n    },\n    {\n      name: 'TOUR',\n      type: 'link',\n      tooltip: 'Tour',\n      icon: 'flight_takeoff',\n      state: 'tour'\n    },\n    // {\n    //   name: 'MAP',\n    //   type: 'link',\n    //   tooltip: 'Map',\n    //   icon: 'add_location',\n    //   state: 'map'\n    // },\n\n    {\n      name: 'CHARTS',\n      type: 'dropDown',\n      tooltip: 'Charts',\n      icon: 'show_chart',\n      sub: [\n        { name: 'Chart js', state: 'charts' },\n        {\n          name: 'eChart',\n          type: 'dropDown',\n          state: 'chart',\n          sub: [\n            { name: 'Pie', state: 'chart/pie' },\n            { name: 'Bar', state: 'chart/bar' },\n            { name: 'Radar', state: 'chart/radar' },\n            { name: \"Heatmap\", state: \"chart/heatmap\" },\n          ]\n        }\n      ]\n    },\n    {\n      name: 'OTHER COMPONENTS',\n      type: 'separator',\n    },\n    {\n      name: 'Page Layouts',\n      type: 'dropDown',\n      icon: 'view_carousel',\n      sub: [\n        { name: 'Left sidebar card', state: 'page-layouts/left-sidebar-card' },\n        { name: 'Right sidebar card', state: 'page-layouts/right-sidebar-card' },\n        { name: 'Full width card', state: 'page-layouts/full-width-card' },\n        { name: 'Full width card tab', state: 'page-layouts/full-width-card-tab' },\n\n        { name: 'Full width plain', state: 'page-layouts/full-width-plain' },\n        { name: 'Left sidebar plain', state: 'page-layouts/left-sidebar-plain' }\n      ]\n    },\n    {\n      name: 'SESSIONS',\n      type: 'dropDown',\n      tooltip: 'Pages',\n      icon: 'view_carousel',\n      sub: [\n        { name: 'SIGNUP', state: 'sessions/signup' },\n        { name: 'Signup 2', state: 'sessions/signup2' },\n        { name: 'Signup 3', state: 'sessions/signup3' },\n        { name: 'Signup 4', state: 'sessions/signup4' },\n        { name: 'SIGNIN', state: 'sessions/signin' },\n        { name: 'Signin 2', state: 'sessions/signin2' },\n        { name: 'Signin 3', state: 'sessions/signin3' },\n        { name: 'Signin 4', state: 'sessions/signin4' },\n        { name: 'FORGOT', state: 'sessions/forgot-password' },\n        { name: 'LOCKSCREEN', state: 'sessions/lockscreen' },\n        { name: 'NOTFOUND', state: 'sessions/404' },\n        { name: 'ERROR', state: 'sessions/error' }\n      ]\n    },\n    {\n      name: 'Utilities',\n      type: 'dropDown',\n      icon: 'format_list_bulleted',\n      sub: [\n        { name: 'Color', state: 'utilities/color' },\n        { name: 'Typography', state: 'utilities/typography' }\n      ]\n    },\n    {\n      name: 'OTHERS',\n      type: 'dropDown',\n      tooltip: 'Others',\n      icon: 'blur_on',\n      sub: [\n        { name: 'GALLERY', state: 'others/gallery' },\n        { name: 'PRICINGS', state: 'others/pricing' },\n        { name: 'USERS', state: 'others/users' },\n        { name: 'BLANK', state: 'others/blank' }\n      ]\n    },\n    {\n      name: 'MATICONS',\n      type: 'link',\n      tooltip: 'Material Icons',\n      icon: 'store',\n      state: 'icons'\n    },\n    {\n      name: 'Multi Level',\n      type: 'dropDown',\n      tooltip: 'Multi Level',\n      icon: 'format_align_center',\n      sub: [\n        { name: 'Level Two', state: 'fake-4' },\n        {\n          name: 'Level Two',\n          type: 'dropDown',\n          state: 'fake-1',\n          sub: [\n            { name: 'Level Three', state: 'fake-2' },\n            { \n              name: 'Level Three', \n              type: 'dropDown',\n              state: 'fake-3',\n              sub: [\n                { name: 'Level Four', state: 'fake-3' },\n                { \n                  name: 'Level Four', \n                  type: 'dropDown',\n                  state: 'fake-4',\n                  sub: [\n                    { name: 'Level Five', state: 'fake-3' },\n                    { name: 'Level Five', type: 'link' }\n                  ]\n                }\n              ]\n            }\n          ]\n        },\n        { name: 'Level Two', state: 'fake-5' }\n      ]\n    },\n    {\n      name: 'DOC',\n      type: 'extLink',\n      tooltip: 'Documentation',\n      icon: 'library_books',\n      state: 'http://demos.ui-lib.com/egret-doc/'\n    }\n  ];\n\n  plainMenu: IMenuItem[] = [\n    {\n      name: 'DASHBOARD',\n      type: 'dropDown',\n      tooltip: 'Dashboard',\n      icon: 'dashboard',\n      sub: [\n        { name: 'Analytics', state: 'dashboard/analytics' },\n        { name: 'Learning Management', state: 'dashboard/learning-management' },\n        { name: 'Analytics Alt', state: 'dashboard/analytics-alt' },\n        { name: 'Cryptocurrency', state: 'dashboard/crypto' },\n        { name: 'Dark Cards', state: 'dashboard/dark' },\n      ]\n    },\n    {\n      name: 'CRUD Table',\n      type: 'link',\n      tooltip: 'CRUD Table',\n      icon: 'list',\n      state: 'cruds/ngx-table'\n    },\n    {\n      name: 'ECOMMERCE',\n      type: 'dropDown',\n      tooltip: 'Shop',\n      icon: 'shopping_cart',\n      sub: [\n        { name: 'PRODUCTS', state: 'shop' },\n        { name: 'PRODUCT DETAILS', state: 'shop/products/5a9ae2106f155194e5c95d67' },\n        { name: 'CART', state: 'shop/cart' },\n        { name: 'CHECKOUT', state: 'shop/checkout' }\n      ]\n    },\n    {\n      name: 'INBOX',\n      type: 'link',\n      icon: 'inbox',\n      state: 'inbox',\n      badges: [{ color: 'primary', value: '4' }]\n    },\n    {\n      name: 'Invoice',\n      type: 'dropDown',\n      icon: 'receipt',\n      sub: [\n        { name: 'List', state: 'invoice/list' },\n        { name: 'View / Edit', state: 'invoice/5a9ae2106518248b68251fd1' },\n      ]\n    },\n    {\n      name: 'CHAT',\n      type: 'link',\n      tooltip: 'Chat',\n      icon: 'chat',\n      state: 'chat',\n      badges: [{ color: 'warn', value: '1' }]\n    },\n    {\n      name: 'CALENDAR',\n      type: 'link',\n      tooltip: 'Calendar',\n      icon: 'date_range',\n      state: 'calendar'\n    },\n    {\n      name: 'Todo',\n      type: 'link',\n      tooltip: 'Todo',\n      icon: 'center_focus_strong',\n      state: 'todo/list'\n    },\n    {\n      name: 'DIALOGS',\n      type: 'dropDown',\n      tooltip: 'Dialogs',\n      icon: 'filter_none',\n      sub: [\n        { name: 'CONFIRM', state: 'dialogs/confirm' },\n        { name: 'LOADER', state: 'dialogs/loader' }\n      ]\n    },\n    {\n      name: 'Material Kits',\n      type: 'dropDown',\n      tooltip: 'Material',\n      icon: 'favorite',\n      badges: [{ color: 'primary', value: '60+' }],\n      sub: [\n        {\n          name: 'Form controls',\n          type: 'dropDown',\n          sub: [\n            { name: 'Autocomplete', state: 'material/autocomplete' },\n            { name: 'Checkbox', state: 'material/checkbox' },\n            { name: 'Datepicker', state: 'material/datepicker' },\n            { name: 'Form Field', state: 'material/form-field' },\n            { name: 'Input Field', state: 'material/input-field' },\n            { name: 'Radio Button', state: 'material/radio-button' },\n            { name: 'Select', state: 'material/select' },\n            { name: 'Slider', state: 'material/slider' },\n            { name: 'Slider Toggle', state: 'material/slider-toggle' }\n          ]\n        },\n        {\n          name: 'Navigation',\n          type: 'dropDown',\n          sub: [\n            { name: 'Menu', state: 'material/menu' },\n            { name: 'Sidenav', state: 'material/sidenav' },\n            { name: 'Toolbar', state: 'material/toolbar' }\n          ]\n        },\n        {\n          name: 'Layout',\n          type: 'dropDown',\n          sub: [\n            { name: 'Card', state: 'material/card' },\n            { name: 'Divider', state: 'material/divider' },\n            { name: 'Expansion Panel', state: 'material/expansion-panel' },\n            { name: 'Grid', state: 'material/grid' },\n            { name: 'List', state: 'material/list' },\n            { name: 'Stepper', state: 'material/stepper' },\n            { name: 'Tab', state: 'material/tab-group' },\n            { name: 'Tree', state: 'material/tree' }\n          ]\n        },\n        {\n          name: 'Buttons & Indicators',\n          type: 'dropDown',\n          sub: [\n            { name: 'BUTTONS', state: 'material/buttons' },\n            { name: 'Button Toggle', state: 'material/button-toggle' },\n            { name: 'Badge', state: 'material/badge' },\n            { name: 'Chips', state: 'material/chips' },\n            { name: 'Icons', state: 'material/icons' },\n            { name: 'Progress Spinner', state: 'material/progress-spinner' },\n            { name: 'Progress Bar', state: 'material/progress-bar' },\n            { name: 'Ripples', state: 'material/ripples' }\n          ]\n        },\n        {\n          name: 'Popups & Modals',\n          type: 'dropDown',\n          sub: [\n            { name: 'Tooltip', state: 'material/tooltip' },\n            { name: 'Bottom Sheet', state: 'material/bottom-sheet' },\n            { name: 'Dialog', state: 'material/dialog' },\n            { name: 'Snackbar', state: 'material/snackbar' }\n          ]\n        },\n        {\n          name: 'Data Table',\n          type: 'dropDown',\n          sub: [\n            { name: 'paginator', state: 'material/paginator' },\n            { name: 'Sort Header', state: 'material/sort-header' },\n            { name: 'Table', state: 'material/table' }\n          ]\n        }\n      ]\n    },\n    {\n      name: 'FORMS',\n      type: 'dropDown',\n      tooltip: 'Forms',\n      icon: 'description',\n      sub: [\n        { name: 'BASIC', state: 'forms/basic' },\n        { name: 'EDITOR', state: 'forms/editor' },\n        { name: 'WIZARD', state: 'forms/wizard' }\n      ]\n    },\n    {\n      name: 'Table',\n      type: 'link',\n      tooltip: 'Table',\n      icon: 'format_line_spacing',\n      state: 'tables/mat-table',\n    },\n    {\n      name: 'PROFILE',\n      type: 'dropDown',\n      tooltip: 'Profile',\n      icon: 'person',\n      badges: [{ color: 'primary', value: '2' }],\n      sub: [\n        { name: 'OVERVIEW', state: 'profile/overview' },\n        { name: 'SETTINGS', state: 'profile/settings' },\n        { name: 'BLANK', state: 'profile/blank' }\n      ]\n    },\n    {\n      name: 'TOUR',\n      type: 'link',\n      tooltip: 'Tour',\n      icon: 'flight_takeoff',\n      state: 'tour'\n    },\n    // {\n    //   name: 'MAP',\n    //   type: 'link',\n    //   tooltip: 'Map',\n    //   icon: 'add_location',\n    //   state: 'map'\n    // },\n\n    {\n      name: 'CHARTS',\n      type: 'dropDown',\n      tooltip: 'Charts',\n      icon: 'show_chart',\n      sub: [\n        { name: 'Chart js', state: 'charts' },\n        {\n          name: 'eChart',\n          type: 'dropDown',\n          state: 'chart',\n          sub: [\n            { name: 'Pie', state: 'chart/pie' },\n            { name: 'Bar', state: 'chart/bar' },\n            { name: 'Radar', state: 'chart/radar' },\n            { name: \"Heatmap\", state: \"chart/heatmap\" },\n          ]\n        }\n      ]\n    },\n    {\n      name: 'Page Layouts',\n      type: 'dropDown',\n      icon: 'view_carousel',\n      sub: [\n        { name: 'Left sidebar card', state: 'page-layouts/left-sidebar-card' },\n        { name: 'Right sidebar card', state: 'page-layouts/right-sidebar-card' },\n        { name: 'Full width card', state: 'page-layouts/full-width-card' },\n        { name: 'Full width card tab', state: 'page-layouts/full-width-card-tab' },\n\n        { name: 'Full width plain', state: 'page-layouts/full-width-plain' },\n        { name: 'Left sidebar plain', state: 'page-layouts/left-sidebar-plain' }\n      ]\n    },\n    {\n      name: 'SESSIONS',\n      type: 'dropDown',\n      tooltip: 'Pages',\n      icon: 'view_carousel',\n      sub: [\n        { name: 'SIGNUP', state: 'sessions/signup' },\n        { name: 'Signup 2', state: 'sessions/signup2' },\n        { name: 'Signup 3', state: 'sessions/signup3' },\n        { name: 'Signup 4', state: 'sessions/signup4' },\n        { name: 'SIGNIN', state: 'sessions/signin' },\n        { name: 'Signin 2', state: 'sessions/signin2' },\n        { name: 'Signin 3', state: 'sessions/signin3' },\n        { name: 'Signin 4', state: 'sessions/signin4' },\n        { name: 'FORGOT', state: 'sessions/forgot-password' },\n        { name: 'LOCKSCREEN', state: 'sessions/lockscreen' },\n        { name: 'NOTFOUND', state: 'sessions/404' },\n        { name: 'ERROR', state: 'sessions/error' }\n      ]\n    },\n    {\n      name: 'Utilities',\n      type: 'dropDown',\n      icon: 'format_list_bulleted',\n      sub: [\n        { name: 'Color', state: 'utilities/color' },\n        { name: 'Typography', state: 'utilities/typography' }\n      ]\n    },\n    {\n      name: 'OTHERS',\n      type: 'dropDown',\n      tooltip: 'Others',\n      icon: 'blur_on',\n      sub: [\n        { name: 'GALLERY', state: 'others/gallery' },\n        { name: 'PRICINGS', state: 'others/pricing' },\n        { name: 'USERS', state: 'others/users' },\n        { name: 'BLANK', state: 'others/blank' }\n      ]\n    },\n    {\n      name: 'MATICONS',\n      type: 'link',\n      tooltip: 'Material Icons',\n      icon: 'store',\n      state: 'icons'\n    },\n    {\n      name: 'Multi Level',\n      type: 'dropDown',\n      tooltip: 'Multi Level',\n      icon: 'format_align_center',\n      sub: [\n        { name: 'Level Two', state: 'fake-4' },\n        {\n          name: 'Level Two',\n          type: 'dropDown',\n          state: 'fake-1',\n          sub: [\n            { name: 'Level Three', state: 'fake-2' },\n            { \n              name: 'Level Three', \n              type: 'dropDown',\n              state: 'fake-3',\n              sub: [\n                { name: 'Level Four', state: 'fake-3' },\n                { \n                  name: 'Level Four', \n                  type: 'dropDown',\n                  state: 'fake-4',\n                  sub: [\n                    { name: 'Level Five', state: 'fake-3' },\n                    { name: 'Level Five', type: 'link' }\n                  ]\n                }\n              ]\n            }\n          ]\n        },\n        { name: 'Level Two', state: 'fake-5' }\n      ]\n    },\n    {\n      name: 'DOC',\n      type: 'extLink',\n      tooltip: 'Documentation',\n      icon: 'library_books',\n      state: 'http://demos.ui-lib.com/egret-doc/'\n    }\n  ];\n\n  // Icon menu TITLE at the very top of navigation.\n  // This title will appear if any icon type item is present in menu.\n  iconTypeMenuTitle = 'Frequently Accessed';\n  // sets iconMenu as default;\n  menuItems = new BehaviorSubject<IMenuItem[]>(this.iconMenu);\n  // navigation component has subscribed to this Observable\n  menuItems$ = this.menuItems.asObservable();\n  constructor() { }\n\n  // Customizer component uses this method to change menu.\n  // You can remove this method and customizer component.\n  // Or you can customize this method to supply different menu for\n  // different user type.\n  publishNavigationChange(menuType: string) {\n    switch (menuType) {\n      case 'separator-menu':\n        this.menuItems.next(this.separatorMenu);\n        break;\n      case 'icon-menu':\n        this.menuItems.next(this.iconMenu);\n        break;\n      default:\n        this.menuItems.next(this.plainMenu);\n    }\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,MAAM;;AA4BtC,OAAM,MAAOC,iBAAiB;EAsnC5BC,YAAA;IArnCA,KAAAC,QAAQ,GAAgB,CACtB;MACEC,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE;KACR,EACD;MACEJ,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,SAAS;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;KACR,EACD;MACEJ,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE;KACR,EACD;MACEJ,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE;KACR,EACD;MACEH,IAAI,EAAE,WAAW;MACjBD,IAAI,EAAE;KACP,EACD;MACEA,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,OAAO;MAChBC,IAAI,EAAE;KACP,EACD;MACEH,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,WAAW;MACpBC,IAAI,EAAE,WAAW;MACjBE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,WAAW;QAAEI,KAAK,EAAE;MAAqB,CAAE,EACnD;QAAEJ,IAAI,EAAE,qBAAqB;QAAEI,KAAK,EAAE;MAA+B,CAAE,EACvE;QAAEJ,IAAI,EAAE,eAAe;QAAEI,KAAK,EAAE;MAAyB,CAAE,EAC3D;QAAEJ,IAAI,EAAE,gBAAgB;QAAEI,KAAK,EAAE;MAAkB,CAAE,EACrD;QAAEJ,IAAI,EAAE,YAAY;QAAEI,KAAK,EAAE;MAAgB,CAAE;KAElD,EACD;MACEJ,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,UAAU;MAChBE,IAAI,EAAE,MAAM;MACZE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,UAAU;QAChBE,IAAI,EAAE,MAAM;QACZE,GAAG,EAAE,CACH;UAACL,IAAI,EAAE,WAAW;UAAEI,KAAK,EAAE;QAAkB,CAAC,EAC9C;UAACJ,IAAI,EAAE,WAAW;UAAEI,KAAK,EAAE;QAAiB,CAAC,EAC7C;UAACJ,IAAI,EAAE,aAAa;UAAEI,KAAK,EAAE;QAAgB,CAAC;OAEjD,EACD;QAACJ,IAAI,EAAE,aAAa;QAAEI,KAAK,EAAE;MAAkB,CAAC,EAChD;QAACJ,IAAI,EAAE,mBAAmB;QAAEI,KAAK,EAAE;MAAiB,CAAC,EACrD;QAACJ,IAAI,EAAE,eAAe;QAAEI,KAAK,EAAE;MAAgB,CAAC;KAEnD,EACD;MACEJ,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,UAAU;MAChBE,IAAI,EAAE,MAAM;MACZE,GAAG,EAAE,CACH;QAACL,IAAI,EAAE,iBAAiB;QAAEI,KAAK,EAAE;MAAkB,CAAC,EACpD;QAACJ,IAAI,EAAE,KAAK;QAAEI,KAAK,EAAE;MAAiB,CAAC,EACvC;QAACJ,IAAI,EAAE,SAAS;QAAEI,KAAK,EAAE;MAAgB,CAAC;KAE7C,EAEJ;MACKJ,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,UAAU;MAChBE,IAAI,EAAE,MAAM;MACZE,GAAG,EAAE,CACT;QAACL,IAAI,EAAE,OAAO;QAAEI,KAAK,EAAE;MAAkB,CAAC,EACpC;QAAEJ,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,UAAU;QAChBE,IAAI,EAAE,MAAM;QACZE,GAAG,EAAE,CACH;UAACL,IAAI,EAAE,WAAW;UAAEI,KAAK,EAAE;QAAkB,CAAC,EAC9C;UAACJ,IAAI,EAAE,WAAW;UAAEI,KAAK,EAAE;QAAiB,CAAC,EAC7C;UAACJ,IAAI,EAAE,aAAa;UAAEI,KAAK,EAAE;QAAgB,CAAC;OAEjD,EACP;QAAEJ,IAAI,EAAE,eAAe;QACfC,IAAI,EAAE,UAAU;QAChBE,IAAI,EAAE,MAAM;QACZE,GAAG,EAAE,CACH;UAACL,IAAI,EAAE,WAAW;UAAEI,KAAK,EAAE;QAAkB,CAAC,EAC9C;UAACJ,IAAI,EAAE,aAAa;UAAEI,KAAK,EAAE;QAAiB,CAAC;OAElD,EACP;QAAEJ,IAAI,EAAE,OAAO;QACPC,IAAI,EAAE,UAAU;QAChBE,IAAI,EAAE,MAAM;QACZE,GAAG,EAAE,CACH;UAACL,IAAI,EAAE,WAAW;UAAEI,KAAK,EAAE;QAAkB,CAAC,EAC9C;UAACJ,IAAI,EAAE,WAAW;UAAEI,KAAK,EAAE;QAAiB,CAAC,EACtD;UAACJ,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAiB,CAAC,EAC3C;UAACJ,IAAI,EAAE,UAAU;UAAEI,KAAK,EAAE;QAAiB,CAAC,EAC5C;UAACJ,IAAI,EAAE,QAAQ;UAAEI,KAAK,EAAE;QAAiB,CAAC;OAEpC,EACD;QAAEJ,IAAI,EAAE,gBAAgB;QACtBC,IAAI,EAAE,UAAU;QAChBE,IAAI,EAAE,MAAM;QACZE,GAAG,EAAE,CACH;UAACL,IAAI,EAAE,sBAAsB;UAAEI,KAAK,EAAE;QAAkB,CAAC,EACzD;UAACJ,IAAI,EAAE,mBAAmB;UAAEI,KAAK,EAAE;QAAiB,CAAC;OAExD;KAEJ,EACJ;MACKJ,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBE,IAAI,EAAE,MAAM;MACZE,GAAG,EAAE,CACH;QAACL,IAAI,EAAE,MAAM;QAAEI,KAAK,EAAE;MAAkB,CAAC,EACzC;QAACJ,IAAI,EAAE,QAAQ;QAAEI,KAAK,EAAE;MAAiB,CAAC,EAC1C;QAACJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAgB,CAAC,EACjD;QAAEJ,IAAI,EAAE,QAAQ;QACRC,IAAI,EAAE,UAAU;QAChBE,IAAI,EAAE,MAAM;QACZE,GAAG,EAAE,CACH;UAACL,IAAI,EAAE,gBAAgB;UAAEI,KAAK,EAAE;QAAkB,CAAC,EACnD;UAACJ,IAAI,EAAE,aAAa;UAAEI,KAAK,EAAE;QAAiB,CAAC;OAElD;KAEJ,EACJ;MACKJ,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,UAAU;MAChBE,IAAI,EAAE,SAAS;MACfE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,SAAS;QAAEI,KAAK,EAAE;MAAc,CAAE,EAC1C;QAAEJ,IAAI,EAAE,SAAS;QAAEI,KAAK,EAAE;MAAkC,CAAE,EAC9D;QAAEJ,IAAI,EAAE,SAAS;QAAEI,KAAK,EAAE;MAAc,CAAE,EAC1C;QAAEJ,IAAI,EAAE,SAAS;QAAEI,KAAK,EAAE;MAAc,CAAE,EAChD;QAAEJ,IAAI,EAAE,SAAS;QAAEI,KAAK,EAAE;MAAc,CAAE;KAEvC,EACD;MACEJ,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,UAAU;MAChBE,IAAI,EAAE,SAAS;MACfE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAc,CAAE,EAC3C;QAAEJ,IAAI,EAAE,MAAM;QAAEI,KAAK,EAAE;MAAkC,CAAE,EAC3D;QAAEJ,IAAI,EAAE,KAAK;QAAEI,KAAK,EAAE;MAAc,CAAE,EACtC;QAAEJ,IAAI,EAAE,QAAQ;QAAEI,KAAK,EAAE;MAAc,CAAE;KAE5C,EACD;MACEJ,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,eAAe;MACrBE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAM,CAAE,EACnC;QAAEJ,IAAI,EAAE,iBAAiB;QAAEI,KAAK,EAAE;MAAwC,CAAE,EAC5E;QAAEJ,IAAI,EAAE,MAAM;QAAEI,KAAK,EAAE;MAAW,CAAE,EACpC;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAe,CAAE;KAE/C,EACD;MACEJ,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,MAAM;MACbE,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAG,CAAE;KACvC,EACD;MACER,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,MAAM;MACZE,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,OAAO;MACdE,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAG,CAAE;KAC1C,EACD;MACER,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,UAAU;MACnBC,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE;KACR,EACD;MACEJ,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,qBAAqB;MAC3BC,KAAK,EAAE;KACR,EACD;MACEJ,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,SAAS;MAClBC,IAAI,EAAE,aAAa;MACnBE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,SAAS;QAAEI,KAAK,EAAE;MAAiB,CAAE,EAC7C;QAAEJ,IAAI,EAAE,QAAQ;QAAEI,KAAK,EAAE;MAAgB,CAAE;KAE9C,EACD;MACEJ,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,UAAU;MACnBC,IAAI,EAAE,UAAU;MAChBG,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAK,CAAE,CAAC;MAC5CH,GAAG,EAAE,CACH;QACEL,IAAI,EAAE,eAAe;QACrBC,IAAI,EAAE,UAAU;QAChBI,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,cAAc;UAAEI,KAAK,EAAE;QAAuB,CAAE,EACxD;UAAEJ,IAAI,EAAE,UAAU;UAAEI,KAAK,EAAE;QAAmB,CAAE,EAChD;UAAEJ,IAAI,EAAE,YAAY;UAAEI,KAAK,EAAE;QAAqB,CAAE,EACpD;UAAEJ,IAAI,EAAE,YAAY;UAAEI,KAAK,EAAE;QAAqB,CAAE,EACpD;UAAEJ,IAAI,EAAE,aAAa;UAAEI,KAAK,EAAE;QAAsB,CAAE,EACtD;UAAEJ,IAAI,EAAE,cAAc;UAAEI,KAAK,EAAE;QAAuB,CAAE,EACxD;UAAEJ,IAAI,EAAE,QAAQ;UAAEI,KAAK,EAAE;QAAiB,CAAE,EAC5C;UAAEJ,IAAI,EAAE,QAAQ;UAAEI,KAAK,EAAE;QAAiB,CAAE,EAC5C;UAAEJ,IAAI,EAAE,eAAe;UAAEI,KAAK,EAAE;QAAwB,CAAE;OAE7D,EACD;QACEJ,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE,UAAU;QAChBI,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,MAAM;UAAEI,KAAK,EAAE;QAAe,CAAE,EACxC;UAAEJ,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAkB,CAAE,EAC9C;UAAEJ,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAkB,CAAE;OAEjD,EACD;QACEJ,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,UAAU;QAChBI,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,MAAM;UAAEI,KAAK,EAAE;QAAe,CAAE,EACxC;UAAEJ,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAkB,CAAE,EAC9C;UAAEJ,IAAI,EAAE,iBAAiB;UAAEI,KAAK,EAAE;QAA0B,CAAE,EAC9D;UAAEJ,IAAI,EAAE,MAAM;UAAEI,KAAK,EAAE;QAAe,CAAE,EACxC;UAAEJ,IAAI,EAAE,MAAM;UAAEI,KAAK,EAAE;QAAe,CAAE,EACxC;UAAEJ,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAkB,CAAE,EAC9C;UAAEJ,IAAI,EAAE,KAAK;UAAEI,KAAK,EAAE;QAAoB,CAAE,EAC5C;UAAEJ,IAAI,EAAE,MAAM;UAAEI,KAAK,EAAE;QAAe,CAAE;OAE3C,EACD;QACEJ,IAAI,EAAE,sBAAsB;QAC5BC,IAAI,EAAE,UAAU;QAChBI,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAkB,CAAE,EAC9C;UAAEJ,IAAI,EAAE,eAAe;UAAEI,KAAK,EAAE;QAAwB,CAAE,EAC1D;UAAEJ,IAAI,EAAE,OAAO;UAAEI,KAAK,EAAE;QAAgB,CAAE,EAC1C;UAAEJ,IAAI,EAAE,OAAO;UAAEI,KAAK,EAAE;QAAgB,CAAE,EAC1C;UAAEJ,IAAI,EAAE,OAAO;UAAEI,KAAK,EAAE;QAAgB,CAAE,EAC1C;UAAEJ,IAAI,EAAE,kBAAkB;UAAEI,KAAK,EAAE;QAA2B,CAAE,EAChE;UAAEJ,IAAI,EAAE,cAAc;UAAEI,KAAK,EAAE;QAAuB,CAAE,EACxD;UAAEJ,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAkB,CAAE;OAEjD,EACD;QACEJ,IAAI,EAAE,iBAAiB;QACvBC,IAAI,EAAE,UAAU;QAChBI,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAkB,CAAE,EAC9C;UAAEJ,IAAI,EAAE,cAAc;UAAEI,KAAK,EAAE;QAAuB,CAAE,EACxD;UAAEJ,IAAI,EAAE,QAAQ;UAAEI,KAAK,EAAE;QAAiB,CAAE,EAC5C;UAAEJ,IAAI,EAAE,UAAU;UAAEI,KAAK,EAAE;QAAmB,CAAE;OAEnD,EACD;QACEJ,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE,UAAU;QAChBI,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,WAAW;UAAEI,KAAK,EAAE;QAAoB,CAAE,EAClD;UAAEJ,IAAI,EAAE,aAAa;UAAEI,KAAK,EAAE;QAAsB,CAAE,EACtD;UAAEJ,IAAI,EAAE,OAAO;UAAEI,KAAK,EAAE;QAAgB,CAAE;OAE7C;KAEJ,EACD;MACEJ,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,OAAO;MAChBC,IAAI,EAAE,aAAa;MACnBE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,OAAO;QAAEI,KAAK,EAAE;MAAa,CAAE,EACvC;QAAEJ,IAAI,EAAE,QAAQ;QAAEI,KAAK,EAAE;MAAc,CAAE,EACzC;QAAEJ,IAAI,EAAE,QAAQ;QAAEI,KAAK,EAAE;MAAc,CAAE;KAE5C,EACD;MACEJ,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,SAAS;MAClBC,IAAI,EAAE,QAAQ;MACdG,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAG,CAAE,CAAC;MAC1CH,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,OAAO;QAAEI,KAAK,EAAE;MAAe,CAAE;KAE5C,EACD;MACEJ,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE;KACR;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;MACEJ,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,QAAQ;MACjBC,IAAI,EAAE,YAAY;MAClBE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAQ,CAAE,EACrC;QACEJ,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,UAAU;QAChBG,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,KAAK;UAAEI,KAAK,EAAE;QAAW,CAAE,EACnC;UAAEJ,IAAI,EAAE,KAAK;UAAEI,KAAK,EAAE;QAAW,CAAE,EACnC;UAAEJ,IAAI,EAAE,OAAO;UAAEI,KAAK,EAAE;QAAa,CAAE,EACvC;UAAEJ,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAe,CAAE;OAE9C;KAEJ,EACD;MACEJ,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,UAAU;MAChBE,IAAI,EAAE,eAAe;MACrBE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,mBAAmB;QAAEI,KAAK,EAAE;MAAgC,CAAE,EACtE;QAAEJ,IAAI,EAAE,oBAAoB;QAAEI,KAAK,EAAE;MAAiC,CAAE,EACxE;QAAEJ,IAAI,EAAE,iBAAiB;QAAEI,KAAK,EAAE;MAA8B,CAAE,EAClE;QAAEJ,IAAI,EAAE,qBAAqB;QAAEI,KAAK,EAAE;MAAkC,CAAE,EAE1E;QAAEJ,IAAI,EAAE,kBAAkB;QAAEI,KAAK,EAAE;MAA+B,CAAE,EACpE;QAAEJ,IAAI,EAAE,oBAAoB;QAAEI,KAAK,EAAE;MAAiC,CAAE;KAE3E,EACD;MACEJ,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,OAAO;MAChBC,IAAI,EAAE,eAAe;MACrBE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,QAAQ;QAAEI,KAAK,EAAE;MAAiB,CAAE,EAC5C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,QAAQ;QAAEI,KAAK,EAAE;MAAiB,CAAE,EAC5C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,QAAQ;QAAEI,KAAK,EAAE;MAA0B,CAAE,EACrD;QAAEJ,IAAI,EAAE,YAAY;QAAEI,KAAK,EAAE;MAAqB,CAAE,EACpD;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAc,CAAE,EAC3C;QAAEJ,IAAI,EAAE,OAAO;QAAEI,KAAK,EAAE;MAAgB,CAAE;KAE7C,EACD;MACEJ,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,UAAU;MAChBE,IAAI,EAAE,sBAAsB;MAC5BE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,OAAO;QAAEI,KAAK,EAAE;MAAiB,CAAE,EAC3C;QAAEJ,IAAI,EAAE,YAAY;QAAEI,KAAK,EAAE;MAAsB,CAAE;KAExD,EACD;MACEJ,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,QAAQ;MACjBC,IAAI,EAAE,SAAS;MACfE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,SAAS;QAAEI,KAAK,EAAE;MAAgB,CAAE,EAC5C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAgB,CAAE,EAC7C;QAAEJ,IAAI,EAAE,OAAO;QAAEI,KAAK,EAAE;MAAc,CAAE,EACxC;QAAEJ,IAAI,EAAE,OAAO;QAAEI,KAAK,EAAE;MAAc,CAAE;KAE3C,EACD;MACEJ,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,gBAAgB;MACzBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;KACR,EACD;MACEJ,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,aAAa;MACtBC,IAAI,EAAE,qBAAqB;MAC3BE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,WAAW;QAAEI,KAAK,EAAE;MAAQ,CAAE,EACtC;QACEJ,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE,UAAU;QAChBI,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,aAAa;UAAEI,KAAK,EAAE;QAAQ,CAAE,EACxC;UACEJ,IAAI,EAAE,aAAa;UACnBC,IAAI,EAAE,UAAU;UAChBI,GAAG,EAAE,CACH;YAAEL,IAAI,EAAE,YAAY;YAAEI,KAAK,EAAE;UAAQ,CAAE,EACvC;YACEJ,IAAI,EAAE,YAAY;YAClBC,IAAI,EAAE,UAAU;YAChBI,GAAG,EAAE,CACH;cAAEL,IAAI,EAAE,YAAY;cAAEI,KAAK,EAAE;YAAQ,CAAE,EACvC;cAAEJ,IAAI,EAAE,YAAY;cAAEC,IAAI,EAAE,MAAM;cAAEG,KAAK,EAAE;YAAQ,CAAE;WAExD;SAEJ;OAEJ,EACD;QAAEJ,IAAI,EAAE,WAAW;QAAEI,KAAK,EAAE;MAAQ,CAAE;KAEzC,EACD;MACEJ,IAAI,EAAE,KAAK;MACXC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,eAAe;MACxBC,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE;KACR,CACF;IAED,KAAAK,aAAa,GAAgB,CAC3B;MACER,IAAI,EAAE,WAAW;MACjBD,IAAI,EAAE;KACP,EACD;MACEA,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,WAAW;MACpBC,IAAI,EAAE,WAAW;MACjBE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,WAAW;QAAEI,KAAK,EAAE;MAAqB,CAAE,EACnD;QAAEJ,IAAI,EAAE,qBAAqB;QAAEI,KAAK,EAAE;MAA+B,CAAE,EACvE;QAAEJ,IAAI,EAAE,eAAe;QAAEI,KAAK,EAAE;MAAyB,CAAE,EAC3D;QAAEJ,IAAI,EAAE,gBAAgB;QAAEI,KAAK,EAAE;MAAkB,CAAE,EACrD;QAAEJ,IAAI,EAAE,YAAY;QAAEI,KAAK,EAAE;MAAgB,CAAE;KAElD,EACD;MACEJ,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,YAAY;MACrBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE;KACR,EACD;MACEJ,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,eAAe;MACrBE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAM,CAAE,EACnC;QAAEJ,IAAI,EAAE,iBAAiB;QAAEI,KAAK,EAAE;MAAwC,CAAE,EAC5E;QAAEJ,IAAI,EAAE,MAAM;QAAEI,KAAK,EAAE;MAAW,CAAE,EACpC;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAe,CAAE;KAE/C,EACD;MACEJ,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,MAAM;MACZE,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,OAAO;MACdE,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAG,CAAE;KAC1C,EACD;MACER,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,UAAU;MAChBE,IAAI,EAAE,SAAS;MACfE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,MAAM;QAAEI,KAAK,EAAE;MAAc,CAAE,EACvC;QAAEJ,IAAI,EAAE,aAAa;QAAEI,KAAK,EAAE;MAAkC,CAAE;KAErE,EACD;MACEJ,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,SAAS;MAClBC,IAAI,EAAE,QAAQ;MACdG,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAG,CAAE,CAAC;MAC1CH,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,OAAO;QAAEI,KAAK,EAAE;MAAe,CAAE;KAE5C,EACD;MACEJ,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,MAAM;MACbE,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAG,CAAE;KACvC,EACD;MACER,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,qBAAqB;MAC3BC,KAAK,EAAE;KACR,EACD;MACEJ,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,SAAS;MAClBC,IAAI,EAAE,aAAa;MACnBE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,SAAS;QAAEI,KAAK,EAAE;MAAiB,CAAE,EAC7C;QAAEJ,IAAI,EAAE,QAAQ;QAAEI,KAAK,EAAE;MAAgB,CAAE;KAE9C,EACD;MACEH,IAAI,EAAE,WAAW;MACjBD,IAAI,EAAE;KACP,EACD;MACEA,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,UAAU;MACnBC,IAAI,EAAE,UAAU;MAChBG,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAK,CAAE,CAAC;MAC5CH,GAAG,EAAE,CACH;QACEL,IAAI,EAAE,eAAe;QACrBC,IAAI,EAAE,UAAU;QAChBI,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,cAAc;UAAEI,KAAK,EAAE;QAAuB,CAAE,EACxD;UAAEJ,IAAI,EAAE,UAAU;UAAEI,KAAK,EAAE;QAAmB,CAAE,EAChD;UAAEJ,IAAI,EAAE,YAAY;UAAEI,KAAK,EAAE;QAAqB,CAAE,EACpD;UAAEJ,IAAI,EAAE,YAAY;UAAEI,KAAK,EAAE;QAAqB,CAAE,EACpD;UAAEJ,IAAI,EAAE,aAAa;UAAEI,KAAK,EAAE;QAAsB,CAAE,EACtD;UAAEJ,IAAI,EAAE,cAAc;UAAEI,KAAK,EAAE;QAAuB,CAAE,EACxD;UAAEJ,IAAI,EAAE,QAAQ;UAAEI,KAAK,EAAE;QAAiB,CAAE,EAC5C;UAAEJ,IAAI,EAAE,QAAQ;UAAEI,KAAK,EAAE;QAAiB,CAAE,EAC5C;UAAEJ,IAAI,EAAE,eAAe;UAAEI,KAAK,EAAE;QAAwB,CAAE;OAE7D,EACD;QACEJ,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE,UAAU;QAChBI,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,MAAM;UAAEI,KAAK,EAAE;QAAe,CAAE,EACxC;UAAEJ,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAkB,CAAE,EAC9C;UAAEJ,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAkB,CAAE;OAEjD,EACD;QACEJ,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,UAAU;QAChBI,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,MAAM;UAAEI,KAAK,EAAE;QAAe,CAAE,EACxC;UAAEJ,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAkB,CAAE,EAC9C;UAAEJ,IAAI,EAAE,iBAAiB;UAAEI,KAAK,EAAE;QAA0B,CAAE,EAC9D;UAAEJ,IAAI,EAAE,MAAM;UAAEI,KAAK,EAAE;QAAe,CAAE,EACxC;UAAEJ,IAAI,EAAE,MAAM;UAAEI,KAAK,EAAE;QAAe,CAAE,EACxC;UAAEJ,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAkB,CAAE,EAC9C;UAAEJ,IAAI,EAAE,KAAK;UAAEI,KAAK,EAAE;QAAoB,CAAE,EAC5C;UAAEJ,IAAI,EAAE,MAAM;UAAEI,KAAK,EAAE;QAAe,CAAE;OAE3C,EACD;QACEJ,IAAI,EAAE,sBAAsB;QAC5BC,IAAI,EAAE,UAAU;QAChBI,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAkB,CAAE,EAC9C;UAAEJ,IAAI,EAAE,eAAe;UAAEI,KAAK,EAAE;QAAwB,CAAE,EAC1D;UAAEJ,IAAI,EAAE,OAAO;UAAEI,KAAK,EAAE;QAAgB,CAAE,EAC1C;UAAEJ,IAAI,EAAE,OAAO;UAAEI,KAAK,EAAE;QAAgB,CAAE,EAC1C;UAAEJ,IAAI,EAAE,OAAO;UAAEI,KAAK,EAAE;QAAgB,CAAE,EAC1C;UAAEJ,IAAI,EAAE,kBAAkB;UAAEI,KAAK,EAAE;QAA2B,CAAE,EAChE;UAAEJ,IAAI,EAAE,cAAc;UAAEI,KAAK,EAAE;QAAuB,CAAE,EACxD;UAAEJ,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAkB,CAAE;OAEjD,EACD;QACEJ,IAAI,EAAE,iBAAiB;QACvBC,IAAI,EAAE,UAAU;QAChBI,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAkB,CAAE,EAC9C;UAAEJ,IAAI,EAAE,cAAc;UAAEI,KAAK,EAAE;QAAuB,CAAE,EACxD;UAAEJ,IAAI,EAAE,QAAQ;UAAEI,KAAK,EAAE;QAAiB,CAAE,EAC5C;UAAEJ,IAAI,EAAE,UAAU;UAAEI,KAAK,EAAE;QAAmB,CAAE;OAEnD,EACD;QACEJ,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE,UAAU;QAChBI,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,WAAW;UAAEI,KAAK,EAAE;QAAoB,CAAE,EAClD;UAAEJ,IAAI,EAAE,aAAa;UAAEI,KAAK,EAAE;QAAsB,CAAE,EACtD;UAAEJ,IAAI,EAAE,OAAO;UAAEI,KAAK,EAAE;QAAgB,CAAE;OAE7C;KAEJ,EACD;MACEJ,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,MAAM;MACZE,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE;KACR,EACD;MACEJ,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,OAAO;MAChBC,IAAI,EAAE,aAAa;MACnBE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,OAAO;QAAEI,KAAK,EAAE;MAAa,CAAE,EACvC;QAAEJ,IAAI,EAAE,QAAQ;QAAEI,KAAK,EAAE;MAAc,CAAE,EACzC;QAAEJ,IAAI,EAAE,QAAQ;QAAEI,KAAK,EAAE;MAAc,CAAE;KAE5C,EACD;MACEJ,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,OAAO;MAChBC,IAAI,EAAE,qBAAqB;MAC3BC,KAAK,EAAE;KACR,EACD;MACEJ,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE;KACR;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;MACEJ,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,QAAQ;MACjBC,IAAI,EAAE,YAAY;MAClBE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAQ,CAAE,EACrC;QACEJ,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,UAAU;QAChBG,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,KAAK;UAAEI,KAAK,EAAE;QAAW,CAAE,EACnC;UAAEJ,IAAI,EAAE,KAAK;UAAEI,KAAK,EAAE;QAAW,CAAE,EACnC;UAAEJ,IAAI,EAAE,OAAO;UAAEI,KAAK,EAAE;QAAa,CAAE,EACvC;UAAEJ,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAe,CAAE;OAE9C;KAEJ,EACD;MACEJ,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE;KACP,EACD;MACED,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,UAAU;MAChBE,IAAI,EAAE,eAAe;MACrBE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,mBAAmB;QAAEI,KAAK,EAAE;MAAgC,CAAE,EACtE;QAAEJ,IAAI,EAAE,oBAAoB;QAAEI,KAAK,EAAE;MAAiC,CAAE,EACxE;QAAEJ,IAAI,EAAE,iBAAiB;QAAEI,KAAK,EAAE;MAA8B,CAAE,EAClE;QAAEJ,IAAI,EAAE,qBAAqB;QAAEI,KAAK,EAAE;MAAkC,CAAE,EAE1E;QAAEJ,IAAI,EAAE,kBAAkB;QAAEI,KAAK,EAAE;MAA+B,CAAE,EACpE;QAAEJ,IAAI,EAAE,oBAAoB;QAAEI,KAAK,EAAE;MAAiC,CAAE;KAE3E,EACD;MACEJ,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,OAAO;MAChBC,IAAI,EAAE,eAAe;MACrBE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,QAAQ;QAAEI,KAAK,EAAE;MAAiB,CAAE,EAC5C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,QAAQ;QAAEI,KAAK,EAAE;MAAiB,CAAE,EAC5C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,QAAQ;QAAEI,KAAK,EAAE;MAA0B,CAAE,EACrD;QAAEJ,IAAI,EAAE,YAAY;QAAEI,KAAK,EAAE;MAAqB,CAAE,EACpD;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAc,CAAE,EAC3C;QAAEJ,IAAI,EAAE,OAAO;QAAEI,KAAK,EAAE;MAAgB,CAAE;KAE7C,EACD;MACEJ,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,UAAU;MAChBE,IAAI,EAAE,sBAAsB;MAC5BE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,OAAO;QAAEI,KAAK,EAAE;MAAiB,CAAE,EAC3C;QAAEJ,IAAI,EAAE,YAAY;QAAEI,KAAK,EAAE;MAAsB,CAAE;KAExD,EACD;MACEJ,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,QAAQ;MACjBC,IAAI,EAAE,SAAS;MACfE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,SAAS;QAAEI,KAAK,EAAE;MAAgB,CAAE,EAC5C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAgB,CAAE,EAC7C;QAAEJ,IAAI,EAAE,OAAO;QAAEI,KAAK,EAAE;MAAc,CAAE,EACxC;QAAEJ,IAAI,EAAE,OAAO;QAAEI,KAAK,EAAE;MAAc,CAAE;KAE3C,EACD;MACEJ,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,gBAAgB;MACzBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;KACR,EACD;MACEJ,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,aAAa;MACtBC,IAAI,EAAE,qBAAqB;MAC3BE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,WAAW;QAAEI,KAAK,EAAE;MAAQ,CAAE,EACtC;QACEJ,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE,UAAU;QAChBG,KAAK,EAAE,QAAQ;QACfC,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,aAAa;UAAEI,KAAK,EAAE;QAAQ,CAAE,EACxC;UACEJ,IAAI,EAAE,aAAa;UACnBC,IAAI,EAAE,UAAU;UAChBG,KAAK,EAAE,QAAQ;UACfC,GAAG,EAAE,CACH;YAAEL,IAAI,EAAE,YAAY;YAAEI,KAAK,EAAE;UAAQ,CAAE,EACvC;YACEJ,IAAI,EAAE,YAAY;YAClBC,IAAI,EAAE,UAAU;YAChBG,KAAK,EAAE,QAAQ;YACfC,GAAG,EAAE,CACH;cAAEL,IAAI,EAAE,YAAY;cAAEI,KAAK,EAAE;YAAQ,CAAE,EACvC;cAAEJ,IAAI,EAAE,YAAY;cAAEC,IAAI,EAAE;YAAM,CAAE;WAEvC;SAEJ;OAEJ,EACD;QAAED,IAAI,EAAE,WAAW;QAAEI,KAAK,EAAE;MAAQ,CAAE;KAEzC,EACD;MACEJ,IAAI,EAAE,KAAK;MACXC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,eAAe;MACxBC,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE;KACR,CACF;IAED,KAAAM,SAAS,GAAgB,CACvB;MACEV,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,WAAW;MACpBC,IAAI,EAAE,WAAW;MACjBE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,WAAW;QAAEI,KAAK,EAAE;MAAqB,CAAE,EACnD;QAAEJ,IAAI,EAAE,qBAAqB;QAAEI,KAAK,EAAE;MAA+B,CAAE,EACvE;QAAEJ,IAAI,EAAE,eAAe;QAAEI,KAAK,EAAE;MAAyB,CAAE,EAC3D;QAAEJ,IAAI,EAAE,gBAAgB;QAAEI,KAAK,EAAE;MAAkB,CAAE,EACrD;QAAEJ,IAAI,EAAE,YAAY;QAAEI,KAAK,EAAE;MAAgB,CAAE;KAElD,EACD;MACEJ,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,YAAY;MACrBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE;KACR,EACD;MACEJ,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,eAAe;MACrBE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAM,CAAE,EACnC;QAAEJ,IAAI,EAAE,iBAAiB;QAAEI,KAAK,EAAE;MAAwC,CAAE,EAC5E;QAAEJ,IAAI,EAAE,MAAM;QAAEI,KAAK,EAAE;MAAW,CAAE,EACpC;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAe,CAAE;KAE/C,EACD;MACEJ,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,MAAM;MACZE,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,OAAO;MACdE,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAG,CAAE;KAC1C,EACD;MACER,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,UAAU;MAChBE,IAAI,EAAE,SAAS;MACfE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,MAAM;QAAEI,KAAK,EAAE;MAAc,CAAE,EACvC;QAAEJ,IAAI,EAAE,aAAa;QAAEI,KAAK,EAAE;MAAkC,CAAE;KAErE,EACD;MACEJ,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,MAAM;MACbE,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAG,CAAE;KACvC,EACD;MACER,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,UAAU;MACnBC,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE;KACR,EACD;MACEJ,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,qBAAqB;MAC3BC,KAAK,EAAE;KACR,EACD;MACEJ,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,SAAS;MAClBC,IAAI,EAAE,aAAa;MACnBE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,SAAS;QAAEI,KAAK,EAAE;MAAiB,CAAE,EAC7C;QAAEJ,IAAI,EAAE,QAAQ;QAAEI,KAAK,EAAE;MAAgB,CAAE;KAE9C,EACD;MACEJ,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,UAAU;MACnBC,IAAI,EAAE,UAAU;MAChBG,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAK,CAAE,CAAC;MAC5CH,GAAG,EAAE,CACH;QACEL,IAAI,EAAE,eAAe;QACrBC,IAAI,EAAE,UAAU;QAChBI,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,cAAc;UAAEI,KAAK,EAAE;QAAuB,CAAE,EACxD;UAAEJ,IAAI,EAAE,UAAU;UAAEI,KAAK,EAAE;QAAmB,CAAE,EAChD;UAAEJ,IAAI,EAAE,YAAY;UAAEI,KAAK,EAAE;QAAqB,CAAE,EACpD;UAAEJ,IAAI,EAAE,YAAY;UAAEI,KAAK,EAAE;QAAqB,CAAE,EACpD;UAAEJ,IAAI,EAAE,aAAa;UAAEI,KAAK,EAAE;QAAsB,CAAE,EACtD;UAAEJ,IAAI,EAAE,cAAc;UAAEI,KAAK,EAAE;QAAuB,CAAE,EACxD;UAAEJ,IAAI,EAAE,QAAQ;UAAEI,KAAK,EAAE;QAAiB,CAAE,EAC5C;UAAEJ,IAAI,EAAE,QAAQ;UAAEI,KAAK,EAAE;QAAiB,CAAE,EAC5C;UAAEJ,IAAI,EAAE,eAAe;UAAEI,KAAK,EAAE;QAAwB,CAAE;OAE7D,EACD;QACEJ,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE,UAAU;QAChBI,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,MAAM;UAAEI,KAAK,EAAE;QAAe,CAAE,EACxC;UAAEJ,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAkB,CAAE,EAC9C;UAAEJ,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAkB,CAAE;OAEjD,EACD;QACEJ,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,UAAU;QAChBI,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,MAAM;UAAEI,KAAK,EAAE;QAAe,CAAE,EACxC;UAAEJ,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAkB,CAAE,EAC9C;UAAEJ,IAAI,EAAE,iBAAiB;UAAEI,KAAK,EAAE;QAA0B,CAAE,EAC9D;UAAEJ,IAAI,EAAE,MAAM;UAAEI,KAAK,EAAE;QAAe,CAAE,EACxC;UAAEJ,IAAI,EAAE,MAAM;UAAEI,KAAK,EAAE;QAAe,CAAE,EACxC;UAAEJ,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAkB,CAAE,EAC9C;UAAEJ,IAAI,EAAE,KAAK;UAAEI,KAAK,EAAE;QAAoB,CAAE,EAC5C;UAAEJ,IAAI,EAAE,MAAM;UAAEI,KAAK,EAAE;QAAe,CAAE;OAE3C,EACD;QACEJ,IAAI,EAAE,sBAAsB;QAC5BC,IAAI,EAAE,UAAU;QAChBI,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAkB,CAAE,EAC9C;UAAEJ,IAAI,EAAE,eAAe;UAAEI,KAAK,EAAE;QAAwB,CAAE,EAC1D;UAAEJ,IAAI,EAAE,OAAO;UAAEI,KAAK,EAAE;QAAgB,CAAE,EAC1C;UAAEJ,IAAI,EAAE,OAAO;UAAEI,KAAK,EAAE;QAAgB,CAAE,EAC1C;UAAEJ,IAAI,EAAE,OAAO;UAAEI,KAAK,EAAE;QAAgB,CAAE,EAC1C;UAAEJ,IAAI,EAAE,kBAAkB;UAAEI,KAAK,EAAE;QAA2B,CAAE,EAChE;UAAEJ,IAAI,EAAE,cAAc;UAAEI,KAAK,EAAE;QAAuB,CAAE,EACxD;UAAEJ,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAkB,CAAE;OAEjD,EACD;QACEJ,IAAI,EAAE,iBAAiB;QACvBC,IAAI,EAAE,UAAU;QAChBI,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAkB,CAAE,EAC9C;UAAEJ,IAAI,EAAE,cAAc;UAAEI,KAAK,EAAE;QAAuB,CAAE,EACxD;UAAEJ,IAAI,EAAE,QAAQ;UAAEI,KAAK,EAAE;QAAiB,CAAE,EAC5C;UAAEJ,IAAI,EAAE,UAAU;UAAEI,KAAK,EAAE;QAAmB,CAAE;OAEnD,EACD;QACEJ,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE,UAAU;QAChBI,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,WAAW;UAAEI,KAAK,EAAE;QAAoB,CAAE,EAClD;UAAEJ,IAAI,EAAE,aAAa;UAAEI,KAAK,EAAE;QAAsB,CAAE,EACtD;UAAEJ,IAAI,EAAE,OAAO;UAAEI,KAAK,EAAE;QAAgB,CAAE;OAE7C;KAEJ,EACD;MACEJ,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,OAAO;MAChBC,IAAI,EAAE,aAAa;MACnBE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,OAAO;QAAEI,KAAK,EAAE;MAAa,CAAE,EACvC;QAAEJ,IAAI,EAAE,QAAQ;QAAEI,KAAK,EAAE;MAAc,CAAE,EACzC;QAAEJ,IAAI,EAAE,QAAQ;QAAEI,KAAK,EAAE;MAAc,CAAE;KAE5C,EACD;MACEJ,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,OAAO;MAChBC,IAAI,EAAE,qBAAqB;MAC3BC,KAAK,EAAE;KACR,EACD;MACEJ,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,SAAS;MAClBC,IAAI,EAAE,QAAQ;MACdG,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAG,CAAE,CAAC;MAC1CH,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,OAAO;QAAEI,KAAK,EAAE;MAAe,CAAE;KAE5C,EACD;MACEJ,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE;KACR;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;MACEJ,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,QAAQ;MACjBC,IAAI,EAAE,YAAY;MAClBE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAQ,CAAE,EACrC;QACEJ,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,UAAU;QAChBG,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,KAAK;UAAEI,KAAK,EAAE;QAAW,CAAE,EACnC;UAAEJ,IAAI,EAAE,KAAK;UAAEI,KAAK,EAAE;QAAW,CAAE,EACnC;UAAEJ,IAAI,EAAE,OAAO;UAAEI,KAAK,EAAE;QAAa,CAAE,EACvC;UAAEJ,IAAI,EAAE,SAAS;UAAEI,KAAK,EAAE;QAAe,CAAE;OAE9C;KAEJ,EACD;MACEJ,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,UAAU;MAChBE,IAAI,EAAE,eAAe;MACrBE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,mBAAmB;QAAEI,KAAK,EAAE;MAAgC,CAAE,EACtE;QAAEJ,IAAI,EAAE,oBAAoB;QAAEI,KAAK,EAAE;MAAiC,CAAE,EACxE;QAAEJ,IAAI,EAAE,iBAAiB;QAAEI,KAAK,EAAE;MAA8B,CAAE,EAClE;QAAEJ,IAAI,EAAE,qBAAqB;QAAEI,KAAK,EAAE;MAAkC,CAAE,EAE1E;QAAEJ,IAAI,EAAE,kBAAkB;QAAEI,KAAK,EAAE;MAA+B,CAAE,EACpE;QAAEJ,IAAI,EAAE,oBAAoB;QAAEI,KAAK,EAAE;MAAiC,CAAE;KAE3E,EACD;MACEJ,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,OAAO;MAChBC,IAAI,EAAE,eAAe;MACrBE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,QAAQ;QAAEI,KAAK,EAAE;MAAiB,CAAE,EAC5C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,QAAQ;QAAEI,KAAK,EAAE;MAAiB,CAAE,EAC5C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAkB,CAAE,EAC/C;QAAEJ,IAAI,EAAE,QAAQ;QAAEI,KAAK,EAAE;MAA0B,CAAE,EACrD;QAAEJ,IAAI,EAAE,YAAY;QAAEI,KAAK,EAAE;MAAqB,CAAE,EACpD;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAc,CAAE,EAC3C;QAAEJ,IAAI,EAAE,OAAO;QAAEI,KAAK,EAAE;MAAgB,CAAE;KAE7C,EACD;MACEJ,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,UAAU;MAChBE,IAAI,EAAE,sBAAsB;MAC5BE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,OAAO;QAAEI,KAAK,EAAE;MAAiB,CAAE,EAC3C;QAAEJ,IAAI,EAAE,YAAY;QAAEI,KAAK,EAAE;MAAsB,CAAE;KAExD,EACD;MACEJ,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,QAAQ;MACjBC,IAAI,EAAE,SAAS;MACfE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,SAAS;QAAEI,KAAK,EAAE;MAAgB,CAAE,EAC5C;QAAEJ,IAAI,EAAE,UAAU;QAAEI,KAAK,EAAE;MAAgB,CAAE,EAC7C;QAAEJ,IAAI,EAAE,OAAO;QAAEI,KAAK,EAAE;MAAc,CAAE,EACxC;QAAEJ,IAAI,EAAE,OAAO;QAAEI,KAAK,EAAE;MAAc,CAAE;KAE3C,EACD;MACEJ,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,gBAAgB;MACzBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;KACR,EACD;MACEJ,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE,aAAa;MACtBC,IAAI,EAAE,qBAAqB;MAC3BE,GAAG,EAAE,CACH;QAAEL,IAAI,EAAE,WAAW;QAAEI,KAAK,EAAE;MAAQ,CAAE,EACtC;QACEJ,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE,UAAU;QAChBG,KAAK,EAAE,QAAQ;QACfC,GAAG,EAAE,CACH;UAAEL,IAAI,EAAE,aAAa;UAAEI,KAAK,EAAE;QAAQ,CAAE,EACxC;UACEJ,IAAI,EAAE,aAAa;UACnBC,IAAI,EAAE,UAAU;UAChBG,KAAK,EAAE,QAAQ;UACfC,GAAG,EAAE,CACH;YAAEL,IAAI,EAAE,YAAY;YAAEI,KAAK,EAAE;UAAQ,CAAE,EACvC;YACEJ,IAAI,EAAE,YAAY;YAClBC,IAAI,EAAE,UAAU;YAChBG,KAAK,EAAE,QAAQ;YACfC,GAAG,EAAE,CACH;cAAEL,IAAI,EAAE,YAAY;cAAEI,KAAK,EAAE;YAAQ,CAAE,EACvC;cAAEJ,IAAI,EAAE,YAAY;cAAEC,IAAI,EAAE;YAAM,CAAE;WAEvC;SAEJ;OAEJ,EACD;QAAED,IAAI,EAAE,WAAW;QAAEI,KAAK,EAAE;MAAQ,CAAE;KAEzC,EACD;MACEJ,IAAI,EAAE,KAAK;MACXC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,eAAe;MACxBC,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE;KACR,CACF;IAED;IACA;IACA,KAAAO,iBAAiB,GAAG,qBAAqB;IACzC;IACA,KAAAC,SAAS,GAAG,IAAIhB,eAAe,CAAc,IAAI,CAACG,QAAQ,CAAC;IAC3D;IACA,KAAAc,UAAU,GAAG,IAAI,CAACD,SAAS,CAACE,YAAY,EAAE;EAC1B;EAEhB;EACA;EACA;EACA;EACAC,uBAAuBA,CAACC,QAAgB;IACtC,QAAQA,QAAQ;MACd,KAAK,gBAAgB;QACnB,IAAI,CAACJ,SAAS,CAACK,IAAI,CAAC,IAAI,CAACR,aAAa,CAAC;QACvC;MACF,KAAK,WAAW;QACd,IAAI,CAACG,SAAS,CAACK,IAAI,CAAC,IAAI,CAAClB,QAAQ,CAAC;QAClC;MACF;QACE,IAAI,CAACa,SAAS,CAACK,IAAI,CAAC,IAAI,CAACP,SAAS,CAAC;IACvC;EACF;EAAC,QAAAQ,CAAA,G;qBAvoCUrB,iBAAiB;EAAA;EAAA,QAAAsB,EAAA,G;WAAjBtB,iBAAiB;IAAAuB,OAAA,EAAjBvB,iBAAiB,CAAAwB;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}