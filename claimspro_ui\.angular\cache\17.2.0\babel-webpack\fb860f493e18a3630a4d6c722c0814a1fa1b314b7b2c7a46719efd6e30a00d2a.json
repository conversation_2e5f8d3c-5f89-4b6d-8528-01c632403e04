{"ast": null, "code": "/*\nLanguage: Wren\nDescription: Think Smalltalk in a Lua-sized package with a dash of Erlang and wrapped up in a familiar, modern syntax.\nCategory: scripting\nAuthor: @joshgoebel\nMaintainer: @joshgoebel\nWebsite: https://wren.io/\n*/\n\n/** @type LanguageFn */\nfunction wren(hljs) {\n  const regex = hljs.regex;\n  const IDENT_RE = /[a-zA-Z]\\w*/;\n  const KEYWORDS = [\"as\", \"break\", \"class\", \"construct\", \"continue\", \"else\", \"for\", \"foreign\", \"if\", \"import\", \"in\", \"is\", \"return\", \"static\", \"var\", \"while\"];\n  const LITERALS = [\"true\", \"false\", \"null\"];\n  const LANGUAGE_VARS = [\"this\", \"super\"];\n  const CORE_CLASSES = [\"Bool\", \"Class\", \"Fiber\", \"Fn\", \"List\", \"Map\", \"Null\", \"Num\", \"Object\", \"Range\", \"Sequence\", \"String\", \"System\"];\n  const OPERATORS = [\"-\", \"~\", /\\*/, \"%\", /\\.\\.\\./, /\\.\\./, /\\+/, \"<<\", \">>\", \">=\", \"<=\", \"<\", \">\", /\\^/, /!=/, /!/, /\\bis\\b/, \"==\", \"&&\", \"&\", /\\|\\|/, /\\|/, /\\?:/, \"=\"];\n  const FUNCTION = {\n    relevance: 0,\n    match: regex.concat(/\\b(?!(if|while|for|else|super)\\b)/, IDENT_RE, /(?=\\s*[({])/),\n    className: \"title.function\"\n  };\n  const FUNCTION_DEFINITION = {\n    match: regex.concat(regex.either(regex.concat(/\\b(?!(if|while|for|else|super)\\b)/, IDENT_RE), regex.either(...OPERATORS)), /(?=\\s*\\([^)]+\\)\\s*\\{)/),\n    className: \"title.function\",\n    starts: {\n      contains: [{\n        begin: /\\(/,\n        end: /\\)/,\n        contains: [{\n          relevance: 0,\n          scope: \"params\",\n          match: IDENT_RE\n        }]\n      }]\n    }\n  };\n  const CLASS_DEFINITION = {\n    variants: [{\n      match: [/class\\s+/, IDENT_RE, /\\s+is\\s+/, IDENT_RE]\n    }, {\n      match: [/class\\s+/, IDENT_RE]\n    }],\n    scope: {\n      2: \"title.class\",\n      4: \"title.class.inherited\"\n    },\n    keywords: KEYWORDS\n  };\n  const OPERATOR = {\n    relevance: 0,\n    match: regex.either(...OPERATORS),\n    className: \"operator\"\n  };\n  const TRIPLE_STRING = {\n    className: \"string\",\n    begin: /\"\"\"/,\n    end: /\"\"\"/\n  };\n  const PROPERTY = {\n    className: \"property\",\n    begin: regex.concat(/\\./, regex.lookahead(IDENT_RE)),\n    end: IDENT_RE,\n    excludeBegin: true,\n    relevance: 0\n  };\n  const FIELD = {\n    relevance: 0,\n    match: regex.concat(/\\b_/, IDENT_RE),\n    scope: \"variable\"\n  };\n\n  // CamelCase\n  const CLASS_REFERENCE = {\n    relevance: 0,\n    match: /\\b[A-Z]+[a-z]+([A-Z]+[a-z]+)*/,\n    scope: \"title.class\",\n    keywords: {\n      _: CORE_CLASSES\n    }\n  };\n\n  // TODO: add custom number modes\n  const NUMBER = hljs.C_NUMBER_MODE;\n  const SETTER = {\n    match: [IDENT_RE, /\\s*/, /=/, /\\s*/, /\\(/, IDENT_RE, /\\)\\s*\\{/],\n    scope: {\n      1: \"title.function\",\n      3: \"operator\",\n      6: \"params\"\n    }\n  };\n  const COMMENT_DOCS = hljs.COMMENT(/\\/\\*\\*/, /\\*\\//, {\n    contains: [{\n      match: /@[a-z]+/,\n      scope: \"doctag\"\n    }, \"self\"]\n  });\n  const SUBST = {\n    scope: \"subst\",\n    begin: /%\\(/,\n    end: /\\)/,\n    contains: [NUMBER, CLASS_REFERENCE, FUNCTION, FIELD, OPERATOR]\n  };\n  const STRING = {\n    scope: \"string\",\n    begin: /\"/,\n    end: /\"/,\n    contains: [SUBST, {\n      scope: \"char.escape\",\n      variants: [{\n        match: /\\\\\\\\|\\\\[\"0%abefnrtv]/\n      }, {\n        match: /\\\\x[0-9A-F]{2}/\n      }, {\n        match: /\\\\u[0-9A-F]{4}/\n      }, {\n        match: /\\\\U[0-9A-F]{8}/\n      }]\n    }]\n  };\n  SUBST.contains.push(STRING);\n  const ALL_KWS = [...KEYWORDS, ...LANGUAGE_VARS, ...LITERALS];\n  const VARIABLE = {\n    relevance: 0,\n    match: regex.concat(\"\\\\b(?!\", ALL_KWS.join(\"|\"), \"\\\\b)\", /[a-zA-Z_]\\w*(?:[?!]|\\b)/),\n    className: \"variable\"\n  };\n\n  // TODO: reconsider this in the future\n  const ATTRIBUTE = {\n    // scope: \"meta\",\n    scope: \"comment\",\n    variants: [{\n      begin: [/#!?/, /[A-Za-z_]+(?=\\()/],\n      beginScope: {\n        // 2: \"attr\"\n      },\n      keywords: {\n        literal: LITERALS\n      },\n      contains: [\n        // NUMBER,\n        // VARIABLE\n      ],\n      end: /\\)/\n    }, {\n      begin: [/#!?/, /[A-Za-z_]+/],\n      beginScope: {\n        // 2: \"attr\"\n      },\n      end: /$/\n    }]\n  };\n  return {\n    name: \"Wren\",\n    keywords: {\n      keyword: KEYWORDS,\n      \"variable.language\": LANGUAGE_VARS,\n      literal: LITERALS\n    },\n    contains: [ATTRIBUTE, NUMBER, STRING, TRIPLE_STRING, COMMENT_DOCS, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, CLASS_REFERENCE, CLASS_DEFINITION, SETTER, FUNCTION_DEFINITION, FUNCTION, OPERATOR, FIELD, PROPERTY, VARIABLE]\n  };\n}\nmodule.exports = wren;", "map": {"version": 3, "names": ["wren", "hljs", "regex", "IDENT_RE", "KEYWORDS", "LITERALS", "LANGUAGE_VARS", "CORE_CLASSES", "OPERATORS", "FUNCTION", "relevance", "match", "concat", "className", "FUNCTION_DEFINITION", "either", "starts", "contains", "begin", "end", "scope", "CLASS_DEFINITION", "variants", "keywords", "OPERATOR", "TRIPLE_STRING", "PROPERTY", "<PERSON><PERSON><PERSON>", "excludeBegin", "FIELD", "CLASS_REFERENCE", "_", "NUMBER", "C_NUMBER_MODE", "SETTER", "COMMENT_DOCS", "COMMENT", "SUBST", "STRING", "push", "ALL_KWS", "VARIABLE", "join", "ATTRIBUTE", "beginScope", "literal", "name", "keyword", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "module", "exports"], "sources": ["C:/OneDrive/Srikant/SWIP/ClaimsPro/claimspro200-src/claimspro_ui/node_modules/highlight.js/lib/languages/wren.js"], "sourcesContent": ["/*\nLanguage: Wren\nDescription: Think Smalltalk in a Lua-sized package with a dash of Erlang and wrapped up in a familiar, modern syntax.\nCategory: scripting\nAuthor: @joshgoebel\nMaintainer: @joshgoebel\nWebsite: https://wren.io/\n*/\n\n/** @type LanguageFn */\nfunction wren(hljs) {\n  const regex = hljs.regex;\n  const IDENT_RE = /[a-zA-Z]\\w*/;\n  const KEYWORDS = [\n    \"as\",\n    \"break\",\n    \"class\",\n    \"construct\",\n    \"continue\",\n    \"else\",\n    \"for\",\n    \"foreign\",\n    \"if\",\n    \"import\",\n    \"in\",\n    \"is\",\n    \"return\",\n    \"static\",\n    \"var\",\n    \"while\"\n  ];\n  const LITERALS = [\n    \"true\",\n    \"false\",\n    \"null\"\n  ];\n  const LANGUAGE_VARS = [\n    \"this\",\n    \"super\"\n  ];\n  const CORE_CLASSES = [\n    \"Bool\",\n    \"Class\",\n    \"Fiber\",\n    \"Fn\",\n    \"List\",\n    \"Map\",\n    \"Null\",\n    \"Num\",\n    \"Object\",\n    \"Range\",\n    \"Sequence\",\n    \"String\",\n    \"System\"\n  ];\n  const OPERATORS = [\n    \"-\",\n    \"~\",\n    /\\*/,\n    \"%\",\n    /\\.\\.\\./,\n    /\\.\\./,\n    /\\+/,\n    \"<<\",\n    \">>\",\n    \">=\",\n    \"<=\",\n    \"<\",\n    \">\",\n    /\\^/,\n    /!=/,\n    /!/,\n    /\\bis\\b/,\n    \"==\",\n    \"&&\",\n    \"&\",\n    /\\|\\|/,\n    /\\|/,\n    /\\?:/,\n    \"=\"\n  ];\n  const FUNCTION = {\n    relevance: 0,\n    match: regex.concat(/\\b(?!(if|while|for|else|super)\\b)/, IDENT_RE, /(?=\\s*[({])/),\n    className: \"title.function\"\n  };\n  const FUNCTION_DEFINITION = {\n    match: regex.concat(\n      regex.either(\n        regex.concat(/\\b(?!(if|while|for|else|super)\\b)/, IDENT_RE),\n        regex.either(...OPERATORS)\n      ),\n      /(?=\\s*\\([^)]+\\)\\s*\\{)/),\n    className: \"title.function\",\n    starts: { contains: [\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        contains: [\n          {\n            relevance: 0,\n            scope: \"params\",\n            match: IDENT_RE\n          }\n        ]\n      }\n    ] }\n  };\n  const CLASS_DEFINITION = {\n    variants: [\n      { match: [\n        /class\\s+/,\n        IDENT_RE,\n        /\\s+is\\s+/,\n        IDENT_RE\n      ] },\n      { match: [\n        /class\\s+/,\n        IDENT_RE\n      ] }\n    ],\n    scope: {\n      2: \"title.class\",\n      4: \"title.class.inherited\"\n    },\n    keywords: KEYWORDS\n  };\n\n  const OPERATOR = {\n    relevance: 0,\n    match: regex.either(...OPERATORS),\n    className: \"operator\"\n  };\n\n  const TRIPLE_STRING = {\n    className: \"string\",\n    begin: /\"\"\"/,\n    end: /\"\"\"/\n  };\n\n  const PROPERTY = {\n    className: \"property\",\n    begin: regex.concat(/\\./, regex.lookahead(IDENT_RE)),\n    end: IDENT_RE,\n    excludeBegin: true,\n    relevance: 0\n  };\n\n  const FIELD = {\n    relevance: 0,\n    match: regex.concat(/\\b_/, IDENT_RE),\n    scope: \"variable\"\n  };\n\n  // CamelCase\n  const CLASS_REFERENCE = {\n    relevance: 0,\n    match: /\\b[A-Z]+[a-z]+([A-Z]+[a-z]+)*/,\n    scope: \"title.class\",\n    keywords: { _: CORE_CLASSES }\n  };\n\n  // TODO: add custom number modes\n  const NUMBER = hljs.C_NUMBER_MODE;\n\n  const SETTER = {\n    match: [\n      IDENT_RE,\n      /\\s*/,\n      /=/,\n      /\\s*/,\n      /\\(/,\n      IDENT_RE,\n      /\\)\\s*\\{/\n    ],\n    scope: {\n      1: \"title.function\",\n      3: \"operator\",\n      6: \"params\"\n    }\n  };\n\n  const COMMENT_DOCS = hljs.COMMENT(\n    /\\/\\*\\*/,\n    /\\*\\//,\n    { contains: [\n      {\n        match: /@[a-z]+/,\n        scope: \"doctag\"\n      },\n      \"self\"\n    ] }\n  );\n  const SUBST = {\n    scope: \"subst\",\n    begin: /%\\(/,\n    end: /\\)/,\n    contains: [\n      NUMBER,\n      CLASS_REFERENCE,\n      FUNCTION,\n      FIELD,\n      OPERATOR\n    ]\n  };\n  const STRING = {\n    scope: \"string\",\n    begin: /\"/,\n    end: /\"/,\n    contains: [\n      SUBST,\n      {\n        scope: \"char.escape\",\n        variants: [\n          { match: /\\\\\\\\|\\\\[\"0%abefnrtv]/ },\n          { match: /\\\\x[0-9A-F]{2}/ },\n          { match: /\\\\u[0-9A-F]{4}/ },\n          { match: /\\\\U[0-9A-F]{8}/ }\n        ]\n      }\n    ]\n  };\n  SUBST.contains.push(STRING);\n\n  const ALL_KWS = [\n    ...KEYWORDS,\n    ...LANGUAGE_VARS,\n    ...LITERALS\n  ];\n  const VARIABLE = {\n    relevance: 0,\n    match: regex.concat(\n      \"\\\\b(?!\",\n      ALL_KWS.join(\"|\"),\n      \"\\\\b)\",\n      /[a-zA-Z_]\\w*(?:[?!]|\\b)/\n    ),\n    className: \"variable\"\n  };\n\n  // TODO: reconsider this in the future\n  const ATTRIBUTE = {\n    // scope: \"meta\",\n    scope: \"comment\",\n    variants: [\n      {\n        begin: [\n          /#!?/,\n          /[A-Za-z_]+(?=\\()/\n        ],\n        beginScope: {\n          // 2: \"attr\"\n        },\n        keywords: { literal: LITERALS },\n        contains: [\n          // NUMBER,\n          // VARIABLE\n        ],\n        end: /\\)/\n      },\n      {\n        begin: [\n          /#!?/,\n          /[A-Za-z_]+/\n        ],\n        beginScope: {\n          // 2: \"attr\"\n        },\n        end: /$/\n      }\n    ]\n  };\n\n  return {\n    name: \"Wren\",\n    keywords: {\n      keyword: KEYWORDS,\n      \"variable.language\": LANGUAGE_VARS,\n      literal: LITERALS\n    },\n    contains: [\n      ATTRIBUTE,\n      NUMBER,\n      STRING,\n      TRIPLE_STRING,\n      COMMENT_DOCS,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      CLASS_REFERENCE,\n      CLASS_DEFINITION,\n      SETTER,\n      FUNCTION_DEFINITION,\n      FUNCTION,\n      OPERATOR,\n      FIELD,\n      PROPERTY,\n      VARIABLE\n    ]\n  };\n}\n\nmodule.exports = wren;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB,MAAMC,KAAK,GAAGD,IAAI,CAACC,KAAK;EACxB,MAAMC,QAAQ,GAAG,aAAa;EAC9B,MAAMC,QAAQ,GAAG,CACf,IAAI,EACJ,OAAO,EACP,OAAO,EACP,WAAW,EACX,UAAU,EACV,MAAM,EACN,KAAK,EACL,SAAS,EACT,IAAI,EACJ,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,KAAK,EACL,OAAO,CACR;EACD,MAAMC,QAAQ,GAAG,CACf,MAAM,EACN,OAAO,EACP,MAAM,CACP;EACD,MAAMC,aAAa,GAAG,CACpB,MAAM,EACN,OAAO,CACR;EACD,MAAMC,YAAY,GAAG,CACnB,MAAM,EACN,OAAO,EACP,OAAO,EACP,IAAI,EACJ,MAAM,EACN,KAAK,EACL,MAAM,EACN,KAAK,EACL,QAAQ,EACR,OAAO,EACP,UAAU,EACV,QAAQ,EACR,QAAQ,CACT;EACD,MAAMC,SAAS,GAAG,CAChB,GAAG,EACH,GAAG,EACH,IAAI,EACJ,GAAG,EACH,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,GAAG,EACH,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,GAAG,EACH,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,GAAG,EACH,MAAM,EACN,IAAI,EACJ,KAAK,EACL,GAAG,CACJ;EACD,MAAMC,QAAQ,GAAG;IACfC,SAAS,EAAE,CAAC;IACZC,KAAK,EAAET,KAAK,CAACU,MAAM,CAAC,mCAAmC,EAAET,QAAQ,EAAE,aAAa,CAAC;IACjFU,SAAS,EAAE;EACb,CAAC;EACD,MAAMC,mBAAmB,GAAG;IAC1BH,KAAK,EAAET,KAAK,CAACU,MAAM,CACjBV,KAAK,CAACa,MAAM,CACVb,KAAK,CAACU,MAAM,CAAC,mCAAmC,EAAET,QAAQ,CAAC,EAC3DD,KAAK,CAACa,MAAM,CAAC,GAAGP,SAAS,CAC3B,CAAC,EACD,uBAAuB,CAAC;IAC1BK,SAAS,EAAE,gBAAgB;IAC3BG,MAAM,EAAE;MAAEC,QAAQ,EAAE,CAClB;QACEC,KAAK,EAAE,IAAI;QACXC,GAAG,EAAE,IAAI;QACTF,QAAQ,EAAE,CACR;UACEP,SAAS,EAAE,CAAC;UACZU,KAAK,EAAE,QAAQ;UACfT,KAAK,EAAER;QACT,CAAC;MAEL,CAAC;IACD;EACJ,CAAC;EACD,MAAMkB,gBAAgB,GAAG;IACvBC,QAAQ,EAAE,CACR;MAAEX,KAAK,EAAE,CACP,UAAU,EACVR,QAAQ,EACR,UAAU,EACVA,QAAQ;IACR,CAAC,EACH;MAAEQ,KAAK,EAAE,CACP,UAAU,EACVR,QAAQ;IACR,CAAC,CACJ;IACDiB,KAAK,EAAE;MACL,CAAC,EAAE,aAAa;MAChB,CAAC,EAAE;IACL,CAAC;IACDG,QAAQ,EAAEnB;EACZ,CAAC;EAED,MAAMoB,QAAQ,GAAG;IACfd,SAAS,EAAE,CAAC;IACZC,KAAK,EAAET,KAAK,CAACa,MAAM,CAAC,GAAGP,SAAS,CAAC;IACjCK,SAAS,EAAE;EACb,CAAC;EAED,MAAMY,aAAa,GAAG;IACpBZ,SAAS,EAAE,QAAQ;IACnBK,KAAK,EAAE,KAAK;IACZC,GAAG,EAAE;EACP,CAAC;EAED,MAAMO,QAAQ,GAAG;IACfb,SAAS,EAAE,UAAU;IACrBK,KAAK,EAAEhB,KAAK,CAACU,MAAM,CAAC,IAAI,EAAEV,KAAK,CAACyB,SAAS,CAACxB,QAAQ,CAAC,CAAC;IACpDgB,GAAG,EAAEhB,QAAQ;IACbyB,YAAY,EAAE,IAAI;IAClBlB,SAAS,EAAE;EACb,CAAC;EAED,MAAMmB,KAAK,GAAG;IACZnB,SAAS,EAAE,CAAC;IACZC,KAAK,EAAET,KAAK,CAACU,MAAM,CAAC,KAAK,EAAET,QAAQ,CAAC;IACpCiB,KAAK,EAAE;EACT,CAAC;;EAED;EACA,MAAMU,eAAe,GAAG;IACtBpB,SAAS,EAAE,CAAC;IACZC,KAAK,EAAE,+BAA+B;IACtCS,KAAK,EAAE,aAAa;IACpBG,QAAQ,EAAE;MAAEQ,CAAC,EAAExB;IAAa;EAC9B,CAAC;;EAED;EACA,MAAMyB,MAAM,GAAG/B,IAAI,CAACgC,aAAa;EAEjC,MAAMC,MAAM,GAAG;IACbvB,KAAK,EAAE,CACLR,QAAQ,EACR,KAAK,EACL,GAAG,EACH,KAAK,EACL,IAAI,EACJA,QAAQ,EACR,SAAS,CACV;IACDiB,KAAK,EAAE;MACL,CAAC,EAAE,gBAAgB;MACnB,CAAC,EAAE,UAAU;MACb,CAAC,EAAE;IACL;EACF,CAAC;EAED,MAAMe,YAAY,GAAGlC,IAAI,CAACmC,OAAO,CAC/B,QAAQ,EACR,MAAM,EACN;IAAEnB,QAAQ,EAAE,CACV;MACEN,KAAK,EAAE,SAAS;MAChBS,KAAK,EAAE;IACT,CAAC,EACD,MAAM;EACN,CACJ,CAAC;EACD,MAAMiB,KAAK,GAAG;IACZjB,KAAK,EAAE,OAAO;IACdF,KAAK,EAAE,KAAK;IACZC,GAAG,EAAE,IAAI;IACTF,QAAQ,EAAE,CACRe,MAAM,EACNF,eAAe,EACfrB,QAAQ,EACRoB,KAAK,EACLL,QAAQ;EAEZ,CAAC;EACD,MAAMc,MAAM,GAAG;IACblB,KAAK,EAAE,QAAQ;IACfF,KAAK,EAAE,GAAG;IACVC,GAAG,EAAE,GAAG;IACRF,QAAQ,EAAE,CACRoB,KAAK,EACL;MACEjB,KAAK,EAAE,aAAa;MACpBE,QAAQ,EAAE,CACR;QAAEX,KAAK,EAAE;MAAuB,CAAC,EACjC;QAAEA,KAAK,EAAE;MAAiB,CAAC,EAC3B;QAAEA,KAAK,EAAE;MAAiB,CAAC,EAC3B;QAAEA,KAAK,EAAE;MAAiB,CAAC;IAE/B,CAAC;EAEL,CAAC;EACD0B,KAAK,CAACpB,QAAQ,CAACsB,IAAI,CAACD,MAAM,CAAC;EAE3B,MAAME,OAAO,GAAG,CACd,GAAGpC,QAAQ,EACX,GAAGE,aAAa,EAChB,GAAGD,QAAQ,CACZ;EACD,MAAMoC,QAAQ,GAAG;IACf/B,SAAS,EAAE,CAAC;IACZC,KAAK,EAAET,KAAK,CAACU,MAAM,CACjB,QAAQ,EACR4B,OAAO,CAACE,IAAI,CAAC,GAAG,CAAC,EACjB,MAAM,EACN,yBACF,CAAC;IACD7B,SAAS,EAAE;EACb,CAAC;;EAED;EACA,MAAM8B,SAAS,GAAG;IAChB;IACAvB,KAAK,EAAE,SAAS;IAChBE,QAAQ,EAAE,CACR;MACEJ,KAAK,EAAE,CACL,KAAK,EACL,kBAAkB,CACnB;MACD0B,UAAU,EAAE;QACV;MAAA,CACD;MACDrB,QAAQ,EAAE;QAAEsB,OAAO,EAAExC;MAAS,CAAC;MAC/BY,QAAQ,EAAE;QACR;QACA;MAAA,CACD;MACDE,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,CACL,KAAK,EACL,YAAY,CACb;MACD0B,UAAU,EAAE;QACV;MAAA,CACD;MACDzB,GAAG,EAAE;IACP,CAAC;EAEL,CAAC;EAED,OAAO;IACL2B,IAAI,EAAE,MAAM;IACZvB,QAAQ,EAAE;MACRwB,OAAO,EAAE3C,QAAQ;MACjB,mBAAmB,EAAEE,aAAa;MAClCuC,OAAO,EAAExC;IACX,CAAC;IACDY,QAAQ,EAAE,CACR0B,SAAS,EACTX,MAAM,EACNM,MAAM,EACNb,aAAa,EACbU,YAAY,EACZlC,IAAI,CAAC+C,mBAAmB,EACxB/C,IAAI,CAACgD,oBAAoB,EACzBnB,eAAe,EACfT,gBAAgB,EAChBa,MAAM,EACNpB,mBAAmB,EACnBL,QAAQ,EACRe,QAAQ,EACRK,KAAK,EACLH,QAAQ,EACRe,QAAQ;EAEZ,CAAC;AACH;AAEAS,MAAM,CAACC,OAAO,GAAGnD,IAAI"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}