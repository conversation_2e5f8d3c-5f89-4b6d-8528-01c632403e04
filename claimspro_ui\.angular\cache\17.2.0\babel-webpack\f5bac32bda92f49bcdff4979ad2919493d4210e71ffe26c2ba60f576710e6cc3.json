{"ast": null, "code": "import { UntypedFormGroup, UntypedFormControl, Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/input\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"ngx-quill\";\nfunction MailComposeComponent_small_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 13);\n    i0.ɵɵtext(1, \" Email is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MailComposeComponent_small_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 13);\n    i0.ɵɵtext(1, \" Invaild email address \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MailComposeComponent_small_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 13);\n    i0.ɵɵtext(1, \" Subject is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MailComposeComponent_small_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 13);\n    i0.ɵɵtext(1, \" Message is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class MailComposeComponent {\n  constructor(composeDialog) {\n    this.composeDialog = composeDialog;\n    this.newMailData = {};\n  }\n  ngOnInit() {\n    this.mailForm = new UntypedFormGroup({\n      to: new UntypedFormControl('', [Validators.required, Validators.email]),\n      subject: new UntypedFormControl('', [Validators.required]),\n      message: new UntypedFormControl('', [Validators.required])\n    });\n  }\n  sendEmail() {\n    // console.log(this.mailForm.value);\n  }\n  closeDialog() {}\n  static #_ = this.ɵfac = function MailComposeComponent_Factory(t) {\n    return new (t || MailComposeComponent)(i0.ɵɵdirectiveInject(i1.MatDialog));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MailComposeComponent,\n    selectors: [[\"mail-compose\"]],\n    decls: 24,\n    vars: 5,\n    consts: [[1, \"p-4\"], [\"novalidate\", \"\", 3, \"formGroup\", \"submit\"], [1, \"!pb-4\"], [1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"to\", \"placeholder\", \"To\"], [\"class\", \"form-error-msg\", 4, \"ngIf\"], [\"matInput\", \"\", \"name\", \"subject\", \"formControlName\", \"subject\", \"placeholder\", \"Subject\", \"value\", \"\"], [\"theme\", \"snow\", \"formControlName\", \"message\"], [1, \"flex\"], [\"mat-mini-fab\", \"\", \"type\", \"submit\", 1, \"mat-primary\"], [\"mat-icon-button\", \"\"], [1, \"flex-grow\"], [\"type\", \"button\", \"mat-button\", \"\", \"matDialogClose\", \"\"], [1, \"form-error-msg\"]],\n    template: function MailComposeComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"form\", 1);\n        i0.ɵɵlistener(\"submit\", function MailComposeComponent_Template_form_submit_1_listener() {\n          return ctx.sendEmail();\n        });\n        i0.ɵɵelementStart(2, \"div\", 2)(3, \"mat-form-field\", 3);\n        i0.ɵɵelement(4, \"input\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(5, MailComposeComponent_small_5_Template, 2, 0, \"small\", 5)(6, MailComposeComponent_small_6_Template, 2, 0, \"small\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\", 2)(8, \"mat-form-field\", 3);\n        i0.ɵɵelement(9, \"input\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(10, MailComposeComponent_small_10_Template, 2, 0, \"small\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"div\", 2);\n        i0.ɵɵelement(12, \"quill-editor\", 7);\n        i0.ɵɵtemplate(13, MailComposeComponent_small_13_Template, 2, 0, \"small\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"div\", 8)(15, \"button\", 9)(16, \"mat-icon\");\n        i0.ɵɵtext(17, \"send\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"button\", 10)(19, \"mat-icon\");\n        i0.ɵɵtext(20, \"attachment\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(21, \"span\", 11);\n        i0.ɵɵelementStart(22, \"button\", 12);\n        i0.ɵɵtext(23, \"Cancel\");\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"formGroup\", ctx.mailForm);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.mailForm.controls.to.errors && (ctx.mailForm.controls.to.dirty || ctx.mailForm.controls.to.touched) && ctx.mailForm.controls.to.errors.required);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.mailForm.controls.to.errors && (ctx.mailForm.controls.to.dirty || ctx.mailForm.controls.to.touched) && ctx.mailForm.controls.to.errors.email);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.mailForm.controls.subject.errors && (ctx.mailForm.controls.subject.dirty || ctx.mailForm.controls.subject.touched) && ctx.mailForm.controls.subject.errors.required);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.mailForm.controls.message.errors && (ctx.mailForm.controls.message.dirty || ctx.mailForm.controls.message.touched) && ctx.mailForm.controls.message.errors.required);\n      }\n    },\n    dependencies: [i2.NgIf, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.FormGroupDirective, i3.FormControlName, i4.MatIcon, i5.MatButton, i5.MatIconButton, i5.MatMiniFabButton, i6.MatInput, i7.MatFormField, i1.MatDialogClose, i8.QuillEditorComponent],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["UntypedFormGroup", "UntypedFormControl", "Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "MailComposeComponent", "constructor", "composeDialog", "newMailData", "ngOnInit", "mailForm", "to", "required", "email", "subject", "message", "sendEmail", "closeDialog", "_", "ɵɵdirectiveInject", "i1", "MatDialog", "_2", "selectors", "decls", "vars", "consts", "template", "MailComposeComponent_Template", "rf", "ctx", "ɵɵlistener", "MailComposeComponent_Template_form_submit_1_listener", "ɵɵelement", "ɵɵtemplate", "MailComposeComponent_small_5_Template", "MailComposeComponent_small_6_Template", "MailComposeComponent_small_10_Template", "MailComposeComponent_small_13_Template", "ɵɵadvance", "ɵɵproperty", "controls", "errors", "dirty", "touched"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\views\\app-inbox\\mail-compose.component.ts", "C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\views\\app-inbox\\mail-compose.template.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\nimport { MatDialog as MatDialog } from '@angular/material/dialog';\nimport { UntypedFormGroup, UntypedFormControl, Validators } from '@angular/forms';\n\n@Component({\n  selector: 'mail-compose',\n  templateUrl: './mail-compose.template.html'\n})\nexport class MailComposeComponent implements OnInit {\n  newMailData = {};\n  mailForm: UntypedFormGroup;\n\n  constructor(private composeDialog: MatDialog) { }\n\n  ngOnInit() {\n    this.mailForm = new UntypedFormGroup({\n      to: new UntypedFormControl('', [\n        Validators.required,\n        Validators.email\n      ]),\n      subject: new UntypedFormControl('', [\n        Validators.required\n      ]),\n      message: new UntypedFormControl('', [\n        Validators.required\n      ])\n    })\n  }\n  sendEmail() {\n    // console.log(this.mailForm.value);\n  }\n  closeDialog() {\n\n  }\n}\n", "<div class=\"p-4\">\n    <form [formGroup]=\"mailForm\" novalidate (submit)=\"sendEmail()\">\n        <div class=\"!pb-4\">\n            <mat-form-field class=\"full-width\">\n                <input matInput formControlName=\"to\" placeholder=\"To\">\n            </mat-form-field>\n            <small\n                *ngIf=\"mailForm.controls.to.errors && (mailForm.controls.to.dirty || mailForm.controls.to.touched) && (mailForm.controls.to.errors.required)\"\n                class=\"form-error-msg\"> Email is required </small>\n            <small\n                *ngIf=\"mailForm.controls.to.errors && (mailForm.controls.to.dirty || mailForm.controls.to.touched) && (mailForm.controls.to.errors.email)\"\n                class=\"form-error-msg\"> Invaild email address </small>\n        </div>\n\n        <div class=\"!pb-4\">\n            <mat-form-field class=\"full-width\">\n                <input matInput name=\"subject\" formControlName=\"subject\" placeholder=\"Subject\" value=\"\">\n            </mat-form-field>\n            <small *ngIf=\"mailForm.controls.subject.errors && \n            (mailForm.controls.subject.dirty || mailForm.controls.subject.touched) && \n            (mailForm.controls.subject.errors.required)\" class=\"form-error-msg\"> Subject is required </small>\n        </div>\n        <div class=\"!pb-4\">\n            <quill-editor theme=\"snow\" formControlName=\"message\"></quill-editor>\n            <small *ngIf=\"mailForm.controls.message.errors && \n            (mailForm.controls.message.dirty || mailForm.controls.message.touched) && \n            (mailForm.controls.message.errors.required)\" class=\"form-error-msg\"> Message is required </small>\n        </div>\n\n        <div class=\"flex\">\n            <button mat-mini-fab class=\"mat-primary\" type=\"submit\"><mat-icon>send</mat-icon></button>\n            <button mat-icon-button><mat-icon>attachment</mat-icon></button>\n            <span class=\"flex-grow\"></span>\n            <button type=\"button\" mat-button matDialogClose>Cancel</button>\n        </div>\n    </form>\n</div>"], "mappings": "AAEA,SAASA,gBAAgB,EAAEC,kBAAkB,EAAEC,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;ICIrEC,EAAA,CAAAC,cAAA,gBAE2B;IAACD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IACtDH,EAAA,CAAAC,cAAA,gBAE2B;IAACD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAO1DH,EAAA,CAAAC,cAAA,gBAEoE;IAACD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAIjGH,EAAA,CAAAC,cAAA,gBAEoE;IAACD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;ADlB7G,OAAM,MAAOC,oBAAoB;EAI/BC,YAAoBC,aAAwB;IAAxB,KAAAA,aAAa,GAAbA,aAAa;IAHjC,KAAAC,WAAW,GAAG,EAAE;EAGgC;EAEhDC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,GAAG,IAAIZ,gBAAgB,CAAC;MACnCa,EAAE,EAAE,IAAIZ,kBAAkB,CAAC,EAAE,EAAE,CAC7BC,UAAU,CAACY,QAAQ,EACnBZ,UAAU,CAACa,KAAK,CACjB,CAAC;MACFC,OAAO,EAAE,IAAIf,kBAAkB,CAAC,EAAE,EAAE,CAClCC,UAAU,CAACY,QAAQ,CACpB,CAAC;MACFG,OAAO,EAAE,IAAIhB,kBAAkB,CAAC,EAAE,EAAE,CAClCC,UAAU,CAACY,QAAQ,CACpB;KACF,CAAC;EACJ;EACAI,SAASA,CAAA;IACP;EAAA;EAEFC,WAAWA,CAAA,GAEX;EAAC,QAAAC,CAAA,G;qBAzBUb,oBAAoB,EAAAJ,EAAA,CAAAkB,iBAAA,CAAAC,EAAA,CAAAC,SAAA;EAAA;EAAA,QAAAC,EAAA,G;UAApBjB,oBAAoB;IAAAkB,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCRjC5B,EAAA,CAAAC,cAAA,aAAiB;QAC2BD,EAAA,CAAA8B,UAAA,oBAAAC,qDAAA;UAAA,OAAUF,GAAA,CAAAd,SAAA,EAAW;QAAA,EAAC;QAC1Df,EAAA,CAAAC,cAAA,aAAmB;QAEXD,EAAA,CAAAgC,SAAA,eAAsD;QAC1DhC,EAAA,CAAAG,YAAA,EAAiB;QACjBH,EAAA,CAAAiC,UAAA,IAAAC,qCAAA,mBAEsD,IAAAC,qCAAA;QAI1DnC,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,aAAmB;QAEXD,EAAA,CAAAgC,SAAA,eAAwF;QAC5FhC,EAAA,CAAAG,YAAA,EAAiB;QACjBH,EAAA,CAAAiC,UAAA,KAAAG,sCAAA,mBAEiG;QACrGpC,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,cAAmB;QACfD,EAAA,CAAAgC,SAAA,uBAAoE;QACpEhC,EAAA,CAAAiC,UAAA,KAAAI,sCAAA,mBAEiG;QACrGrC,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,cAAkB;QACmDD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAChFH,EAAA,CAAAC,cAAA,kBAAwB;QAAUD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACvDH,EAAA,CAAAgC,SAAA,gBAA+B;QAC/BhC,EAAA,CAAAC,cAAA,kBAAgD;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;QAhCjEH,EAAA,CAAAsC,SAAA,EAAsB;QAAtBtC,EAAA,CAAAuC,UAAA,cAAAV,GAAA,CAAApB,QAAA,CAAsB;QAMfT,EAAA,CAAAsC,SAAA,GAA2I;QAA3ItC,EAAA,CAAAuC,UAAA,SAAAV,GAAA,CAAApB,QAAA,CAAA+B,QAAA,CAAA9B,EAAA,CAAA+B,MAAA,KAAAZ,GAAA,CAAApB,QAAA,CAAA+B,QAAA,CAAA9B,EAAA,CAAAgC,KAAA,IAAAb,GAAA,CAAApB,QAAA,CAAA+B,QAAA,CAAA9B,EAAA,CAAAiC,OAAA,KAAAd,GAAA,CAAApB,QAAA,CAAA+B,QAAA,CAAA9B,EAAA,CAAA+B,MAAA,CAAA9B,QAAA,CAA2I;QAG3IX,EAAA,CAAAsC,SAAA,EAAwI;QAAxItC,EAAA,CAAAuC,UAAA,SAAAV,GAAA,CAAApB,QAAA,CAAA+B,QAAA,CAAA9B,EAAA,CAAA+B,MAAA,KAAAZ,GAAA,CAAApB,QAAA,CAAA+B,QAAA,CAAA9B,EAAA,CAAAgC,KAAA,IAAAb,GAAA,CAAApB,QAAA,CAAA+B,QAAA,CAAA9B,EAAA,CAAAiC,OAAA,KAAAd,GAAA,CAAApB,QAAA,CAAA+B,QAAA,CAAA9B,EAAA,CAAA+B,MAAA,CAAA7B,KAAA,CAAwI;QAQrIZ,EAAA,CAAAsC,SAAA,GAEmC;QAFnCtC,EAAA,CAAAuC,UAAA,SAAAV,GAAA,CAAApB,QAAA,CAAA+B,QAAA,CAAA3B,OAAA,CAAA4B,MAAA,KAAAZ,GAAA,CAAApB,QAAA,CAAA+B,QAAA,CAAA3B,OAAA,CAAA6B,KAAA,IAAAb,GAAA,CAAApB,QAAA,CAAA+B,QAAA,CAAA3B,OAAA,CAAA8B,OAAA,KAAAd,GAAA,CAAApB,QAAA,CAAA+B,QAAA,CAAA3B,OAAA,CAAA4B,MAAA,CAAA9B,QAAA,CAEmC;QAInCX,EAAA,CAAAsC,SAAA,GAEmC;QAFnCtC,EAAA,CAAAuC,UAAA,SAAAV,GAAA,CAAApB,QAAA,CAAA+B,QAAA,CAAA1B,OAAA,CAAA2B,MAAA,KAAAZ,GAAA,CAAApB,QAAA,CAAA+B,QAAA,CAAA1B,OAAA,CAAA4B,KAAA,IAAAb,GAAA,CAAApB,QAAA,CAAA+B,QAAA,CAAA1B,OAAA,CAAA6B,OAAA,KAAAd,GAAA,CAAApB,QAAA,CAAA+B,QAAA,CAAA1B,OAAA,CAAA2B,MAAA,CAAA9B,QAAA,CAEmC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}