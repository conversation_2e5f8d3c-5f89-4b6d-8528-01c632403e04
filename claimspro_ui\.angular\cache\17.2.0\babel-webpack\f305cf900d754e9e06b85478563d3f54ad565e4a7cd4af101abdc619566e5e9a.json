{"ast": null, "code": "/*\nLanguage: LDIF\nContributors: <PERSON> <<EMAIL>>\nCategory: enterprise, config\nWebsite: https://en.wikipedia.org/wiki/LDAP_Data_Interchange_Format\n*/\n\n/** @type LanguageFn */\nfunction ldif(hljs) {\n  return {\n    name: 'LDIF',\n    contains: [{\n      className: 'attribute',\n      match: '^dn(?=:)',\n      relevance: 10\n    }, {\n      className: 'attribute',\n      match: '^\\\\w+(?=:)'\n    }, {\n      className: 'literal',\n      match: '^-'\n    }, hljs.HASH_COMMENT_MODE]\n  };\n}\nmodule.exports = ldif;", "map": {"version": 3, "names": ["ldif", "hljs", "name", "contains", "className", "match", "relevance", "HASH_COMMENT_MODE", "module", "exports"], "sources": ["C:/OneDrive/Srikant/SWIP/ClaimsPro/claimspro200-src/claimspro_ui/node_modules/highlight.js/lib/languages/ldif.js"], "sourcesContent": ["/*\nLanguage: LDIF\nContributors: <PERSON> <<EMAIL>>\nCategory: enterprise, config\nWebsite: https://en.wikipedia.org/wiki/LDAP_Data_Interchange_Format\n*/\n\n/** @type LanguageFn */\nfunction ldif(hljs) {\n  return {\n    name: 'LDIF',\n    contains: [\n      {\n        className: 'attribute',\n        match: '^dn(?=:)',\n        relevance: 10\n      },\n      {\n        className: 'attribute',\n        match: '^\\\\w+(?=:)'\n      },\n      {\n        className: 'literal',\n        match: '^-'\n      },\n      hljs.HASH_COMMENT_MODE\n    ]\n  };\n}\n\nmodule.exports = ldif;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB,OAAO;IACLC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,WAAW;MACtBC,KAAK,EAAE,UAAU;MACjBC,SAAS,EAAE;IACb,CAAC,EACD;MACEF,SAAS,EAAE,WAAW;MACtBC,KAAK,EAAE;IACT,CAAC,EACD;MACED,SAAS,EAAE,SAAS;MACpBC,KAAK,EAAE;IACT,CAAC,EACDJ,IAAI,CAACM,iBAAiB;EAE1B,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGT,IAAI"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}