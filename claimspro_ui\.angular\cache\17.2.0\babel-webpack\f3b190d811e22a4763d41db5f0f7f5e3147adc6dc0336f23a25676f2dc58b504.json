{"ast": null, "code": "/*\nLanguage: Excel formulae\nAuthor: <PERSON> <<EMAIL>>\nDescription: Excel formulae\nWebsite: https://products.office.com/en-us/excel/\n*/\n\n/** @type LanguageFn */\nfunction excel(hljs) {\n  // built-in functions imported from https://web.archive.org/web/20160513042710/https://support.office.com/en-us/article/Excel-functions-alphabetical-b3944572-255d-4efb-bb96-c6d90033e188\n  const BUILT_INS = [\"ABS\", \"ACCRINT\", \"ACCRINTM\", \"ACOS\", \"ACOSH\", \"ACOT\", \"ACOTH\", \"AGGREGATE\", \"ADDRESS\", \"AMOR<PERSON>GR<PERSON>\", \"AMOR<PERSON>IN<PERSON>\", \"AND\", \"ARABIC\", \"AREAS\", \"ASC\", \"ASIN\", \"ASINH\", \"ATAN\", \"ATAN2\", \"ATANH\", \"AVEDEV\", \"AVERAGE\", \"AVERAGEA\", \"AVERAGEIF\", \"AVERAGEIFS\", \"BAHTTEXT\", \"BASE\", \"<PERSON>ES<PERSON>L<PERSON>\", \"BESSELJ\", \"BESSEL<PERSON>\", \"BESSELY\", \"BETADIST\", \"BETA.DIST\", \"BETAINV\", \"BETA.INV\", \"BIN2DEC\", \"BIN2HEX\", \"BIN2OCT\", \"BINOMDIST\", \"BINOM.DIST\", \"BINOM.DIST.RANGE\", \"BINOM.INV\", \"BITAND\", \"BITLSHIFT\", \"BITOR\", \"BITRSHIFT\", \"BITXOR\", \"CALL\", \"CEILING\", \"CEILING.MATH\", \"CEILING.PRECISE\", \"CELL\", \"CHAR\", \"CHIDIST\", \"CHIINV\", \"CHITEST\", \"CHISQ.DIST\", \"CHISQ.DIST.RT\", \"CHISQ.INV\", \"CHISQ.INV.RT\", \"CHISQ.TEST\", \"CHOOSE\", \"CLEAN\", \"CODE\", \"COLUMN\", \"COLUMNS\", \"COMBIN\", \"COMBINA\", \"COMPLEX\", \"CONCAT\", \"CONCATENATE\", \"CONFIDENCE\", \"CONFIDENCE.NORM\", \"CONFIDENCE.T\", \"CONVERT\", \"CORREL\", \"COS\", \"COSH\", \"COT\", \"COTH\", \"COUNT\", \"COUNTA\", \"COUNTBLANK\", \"COUNTIF\", \"COUNTIFS\", \"COUPDAYBS\", \"COUPDAYS\", \"COUPDAYSNC\", \"COUPNCD\", \"COUPNUM\", \"COUPPCD\", \"COVAR\", \"COVARIANCE.P\", \"COVARIANCE.S\", \"CRITBINOM\", \"CSC\", \"CSCH\", \"CUBEKPIMEMBER\", \"CUBEMEMBER\", \"CUBEMEMBERPROPERTY\", \"CUBERANKEDMEMBER\", \"CUBESET\", \"CUBESETCOUNT\", \"CUBEVALUE\", \"CUMIPMT\", \"CUMPRINC\", \"DATE\", \"DATEDIF\", \"DATEVALUE\", \"DAVERAGE\", \"DAY\", \"DAYS\", \"DAYS360\", \"DB\", \"DBCS\", \"DCOUNT\", \"DCOUNTA\", \"DDB\", \"DEC2BIN\", \"DEC2HEX\", \"DEC2OCT\", \"DECIMAL\", \"DEGREES\", \"DELTA\", \"DEVSQ\", \"DGET\", \"DISC\", \"DMAX\", \"DMIN\", \"DOLLAR\", \"DOLLARDE\", \"DOLLARFR\", \"DPRODUCT\", \"DSTDEV\", \"DSTDEVP\", \"DSUM\", \"DURATION\", \"DVAR\", \"DVARP\", \"EDATE\", \"EFFECT\", \"ENCODEURL\", \"EOMONTH\", \"ERF\", \"ERF.PRECISE\", \"ERFC\", \"ERFC.PRECISE\", \"ERROR.TYPE\", \"EUROCONVERT\", \"EVEN\", \"EXACT\", \"EXP\", \"EXPON.DIST\", \"EXPONDIST\", \"FACT\", \"FACTDOUBLE\", \"FALSE|0\", \"F.DIST\", \"FDIST\", \"F.DIST.RT\", \"FILTERXML\", \"FIND\", \"FINDB\", \"F.INV\", \"F.INV.RT\", \"FINV\", \"FISHER\", \"FISHERINV\", \"FIXED\", \"FLOOR\", \"FLOOR.MATH\", \"FLOOR.PRECISE\", \"FORECAST\", \"FORECAST.ETS\", \"FORECAST.ETS.CONFINT\", \"FORECAST.ETS.SEASONALITY\", \"FORECAST.ETS.STAT\", \"FORECAST.LINEAR\", \"FORMULATEXT\", \"FREQUENCY\", \"F.TEST\", \"FTEST\", \"FV\", \"FVSCHEDULE\", \"GAMMA\", \"GAMMA.DIST\", \"GAMMADIST\", \"GAMMA.INV\", \"GAMMAINV\", \"GAMMALN\", \"GAMMALN.PRECISE\", \"GAUSS\", \"GCD\", \"GEOMEAN\", \"GESTEP\", \"GETPIVOTDATA\", \"GROWTH\", \"HARMEAN\", \"HEX2BIN\", \"HEX2DEC\", \"HEX2OCT\", \"HLOOKUP\", \"HOUR\", \"HYPERLINK\", \"HYPGEOM.DIST\", \"HYPGEOMDIST\", \"IF\", \"IFERROR\", \"IFNA\", \"IFS\", \"IMABS\", \"IMAGINARY\", \"IMARGUMENT\", \"IMCONJUGATE\", \"IMCOS\", \"IMCOSH\", \"IMCOT\", \"IMCSC\", \"IMCSCH\", \"IMDIV\", \"IMEXP\", \"IMLN\", \"IMLOG10\", \"IMLOG2\", \"IMPOWER\", \"IMPRODUCT\", \"IMREAL\", \"IMSEC\", \"IMSECH\", \"IMSIN\", \"IMSINH\", \"IMSQRT\", \"IMSUB\", \"IMSUM\", \"IMTAN\", \"INDEX\", \"INDIRECT\", \"INFO\", \"INT\", \"INTERCEPT\", \"INTRATE\", \"IPMT\", \"IRR\", \"ISBLANK\", \"ISERR\", \"ISERROR\", \"ISEVEN\", \"ISFORMULA\", \"ISLOGICAL\", \"ISNA\", \"ISNONTEXT\", \"ISNUMBER\", \"ISODD\", \"ISREF\", \"ISTEXT\", \"ISO.CEILING\", \"ISOWEEKNUM\", \"ISPMT\", \"JIS\", \"KURT\", \"LARGE\", \"LCM\", \"LEFT\", \"LEFTB\", \"LEN\", \"LENB\", \"LINEST\", \"LN\", \"LOG\", \"LOG10\", \"LOGEST\", \"LOGINV\", \"LOGNORM.DIST\", \"LOGNORMDIST\", \"LOGNORM.INV\", \"LOOKUP\", \"LOWER\", \"MATCH\", \"MAX\", \"MAXA\", \"MAXIFS\", \"MDETERM\", \"MDURATION\", \"MEDIAN\", \"MID\", \"MIDBs\", \"MIN\", \"MINIFS\", \"MINA\", \"MINUTE\", \"MINVERSE\", \"MIRR\", \"MMULT\", \"MOD\", \"MODE\", \"MODE.MULT\", \"MODE.SNGL\", \"MONTH\", \"MROUND\", \"MULTINOMIAL\", \"MUNIT\", \"N\", \"NA\", \"NEGBINOM.DIST\", \"NEGBINOMDIST\", \"NETWORKDAYS\", \"NETWORKDAYS.INTL\", \"NOMINAL\", \"NORM.DIST\", \"NORMDIST\", \"NORMINV\", \"NORM.INV\", \"NORM.S.DIST\", \"NORMSDIST\", \"NORM.S.INV\", \"NORMSINV\", \"NOT\", \"NOW\", \"NPER\", \"NPV\", \"NUMBERVALUE\", \"OCT2BIN\", \"OCT2DEC\", \"OCT2HEX\", \"ODD\", \"ODDFPRICE\", \"ODDFYIELD\", \"ODDLPRICE\", \"ODDLYIELD\", \"OFFSET\", \"OR\", \"PDURATION\", \"PEARSON\", \"PERCENTILE.EXC\", \"PERCENTILE.INC\", \"PERCENTILE\", \"PERCENTRANK.EXC\", \"PERCENTRANK.INC\", \"PERCENTRANK\", \"PERMUT\", \"PERMUTATIONA\", \"PHI\", \"PHONETIC\", \"PI\", \"PMT\", \"POISSON.DIST\", \"POISSON\", \"POWER\", \"PPMT\", \"PRICE\", \"PRICEDISC\", \"PRICEMAT\", \"PROB\", \"PRODUCT\", \"PROPER\", \"PV\", \"QUARTILE\", \"QUARTILE.EXC\", \"QUARTILE.INC\", \"QUOTIENT\", \"RADIANS\", \"RAND\", \"RANDBETWEEN\", \"RANK.AVG\", \"RANK.EQ\", \"RANK\", \"RATE\", \"RECEIVED\", \"REGISTER.ID\", \"REPLACE\", \"REPLACEB\", \"REPT\", \"RIGHT\", \"RIGHTB\", \"ROMAN\", \"ROUND\", \"ROUNDDOWN\", \"ROUNDUP\", \"ROW\", \"ROWS\", \"RRI\", \"RSQ\", \"RTD\", \"SEARCH\", \"SEARCHB\", \"SEC\", \"SECH\", \"SECOND\", \"SERIESSUM\", \"SHEET\", \"SHEETS\", \"SIGN\", \"SIN\", \"SINH\", \"SKEW\", \"SKEW.P\", \"SLN\", \"SLOPE\", \"SMALL\", \"SQL.REQUEST\", \"SQRT\", \"SQRTPI\", \"STANDARDIZE\", \"STDEV\", \"STDEV.P\", \"STDEV.S\", \"STDEVA\", \"STDEVP\", \"STDEVPA\", \"STEYX\", \"SUBSTITUTE\", \"SUBTOTAL\", \"SUM\", \"SUMIF\", \"SUMIFS\", \"SUMPRODUCT\", \"SUMSQ\", \"SUMX2MY2\", \"SUMX2PY2\", \"SUMXMY2\", \"SWITCH\", \"SYD\", \"T\", \"TAN\", \"TANH\", \"TBILLEQ\", \"TBILLPRICE\", \"TBILLYIELD\", \"T.DIST\", \"T.DIST.2T\", \"T.DIST.RT\", \"TDIST\", \"TEXT\", \"TEXTJOIN\", \"TIME\", \"TIMEVALUE\", \"T.INV\", \"T.INV.2T\", \"TINV\", \"TODAY\", \"TRANSPOSE\", \"TREND\", \"TRIM\", \"TRIMMEAN\", \"TRUE|0\", \"TRUNC\", \"T.TEST\", \"TTEST\", \"TYPE\", \"UNICHAR\", \"UNICODE\", \"UPPER\", \"VALUE\", \"VAR\", \"VAR.P\", \"VAR.S\", \"VARA\", \"VARP\", \"VARPA\", \"VDB\", \"VLOOKUP\", \"WEBSERVICE\", \"WEEKDAY\", \"WEEKNUM\", \"WEIBULL\", \"WEIBULL.DIST\", \"WORKDAY\", \"WORKDAY.INTL\", \"XIRR\", \"XNPV\", \"XOR\", \"YEAR\", \"YEARFRAC\", \"YIELD\", \"YIELDDISC\", \"YIELDMAT\", \"Z.TEST\", \"ZTEST\"];\n  return {\n    name: 'Excel formulae',\n    aliases: ['xlsx', 'xls'],\n    case_insensitive: true,\n    keywords: {\n      $pattern: /[a-zA-Z][\\w\\.]*/,\n      built_in: BUILT_INS\n    },\n    contains: [{\n      /* matches a beginning equal sign found in Excel formula examples */\n      begin: /^=/,\n      end: /[^=]/,\n      returnEnd: true,\n      illegal: /=/,\n      /* only allow single equal sign at front of line */\n      relevance: 10\n    }, /* technically, there can be more than 2 letters in column names, but this prevents conflict with some keywords */\n    {\n      /* matches a reference to a single cell */\n      className: 'symbol',\n      begin: /\\b[A-Z]{1,2}\\d+\\b/,\n      end: /[^\\d]/,\n      excludeEnd: true,\n      relevance: 0\n    }, {\n      /* matches a reference to a range of cells */\n      className: 'symbol',\n      begin: /[A-Z]{0,2}\\d*:[A-Z]{0,2}\\d*/,\n      relevance: 0\n    }, hljs.BACKSLASH_ESCAPE, hljs.QUOTE_STRING_MODE, {\n      className: 'number',\n      begin: hljs.NUMBER_RE + '(%)?',\n      relevance: 0\n    }, /* Excel formula comments are done by putting the comment in a function call to N() */\n    hljs.COMMENT(/\\bN\\(/, /\\)/, {\n      excludeBegin: true,\n      excludeEnd: true,\n      illegal: /\\n/\n    })]\n  };\n}\nmodule.exports = excel;", "map": {"version": 3, "names": ["excel", "hljs", "BUILT_INS", "name", "aliases", "case_insensitive", "keywords", "$pattern", "built_in", "contains", "begin", "end", "returnEnd", "illegal", "relevance", "className", "excludeEnd", "BACKSLASH_ESCAPE", "QUOTE_STRING_MODE", "NUMBER_RE", "COMMENT", "excludeBegin", "module", "exports"], "sources": ["C:/OneDrive/Srikant/SWIP/ClaimsPro/claimspro200-src/claimspro_ui/node_modules/highlight.js/lib/languages/excel.js"], "sourcesContent": ["/*\nLanguage: Excel formulae\nAuthor: <PERSON> <<EMAIL>>\nDescription: Excel formulae\nWebsite: https://products.office.com/en-us/excel/\n*/\n\n/** @type LanguageFn */\nfunction excel(hljs) {\n  // built-in functions imported from https://web.archive.org/web/20160513042710/https://support.office.com/en-us/article/Excel-functions-alphabetical-b3944572-255d-4efb-bb96-c6d90033e188\n  const BUILT_INS = [\n    \"ABS\",\n    \"ACCRINT\",\n    \"ACCRINTM\",\n    \"ACOS\",\n    \"ACOSH\",\n    \"ACOT\",\n    \"ACOTH\",\n    \"AGGREGATE\",\n    \"ADDRESS\",\n    \"AMOR<PERSON>GR<PERSON>\",\n    \"AMOR<PERSON>IN<PERSON>\",\n    \"AND\",\n    \"ARABIC\",\n    \"AREAS\",\n    \"ASC\",\n    \"ASIN\",\n    \"ASINH\",\n    \"ATAN\",\n    \"ATAN2\",\n    \"ATANH\",\n    \"AVEDEV\",\n    \"AVERAGE\",\n    \"AVERAGEA\",\n    \"AVERAGEIF\",\n    \"AVERAGEIFS\",\n    \"BAHTTEXT\",\n    \"BASE\",\n    \"<PERSON>ES<PERSON>L<PERSON>\",\n    \"BESSELJ\",\n    \"BESSEL<PERSON>\",\n    \"BESSELY\",\n    \"BETADIST\",\n    \"BETA.DIST\",\n    \"BETAINV\",\n    \"BETA.INV\",\n    \"BIN2DEC\",\n    \"BIN2HEX\",\n    \"BIN2OCT\",\n    \"BINOMDIST\",\n    \"BINOM.DIST\",\n    \"BINOM.DIST.RANGE\",\n    \"BINOM.INV\",\n    \"BITAND\",\n    \"BITLSHIFT\",\n    \"BITOR\",\n    \"BITRSHIFT\",\n    \"BITXOR\",\n    \"CALL\",\n    \"CEILING\",\n    \"CEILING.MATH\",\n    \"CEILING.PRECISE\",\n    \"CELL\",\n    \"CHAR\",\n    \"CHIDIST\",\n    \"CHIINV\",\n    \"CHITEST\",\n    \"CHISQ.DIST\",\n    \"CHISQ.DIST.RT\",\n    \"CHISQ.INV\",\n    \"CHISQ.INV.RT\",\n    \"CHISQ.TEST\",\n    \"CHOOSE\",\n    \"CLEAN\",\n    \"CODE\",\n    \"COLUMN\",\n    \"COLUMNS\",\n    \"COMBIN\",\n    \"COMBINA\",\n    \"COMPLEX\",\n    \"CONCAT\",\n    \"CONCATENATE\",\n    \"CONFIDENCE\",\n    \"CONFIDENCE.NORM\",\n    \"CONFIDENCE.T\",\n    \"CONVERT\",\n    \"CORREL\",\n    \"COS\",\n    \"COSH\",\n    \"COT\",\n    \"COTH\",\n    \"COUNT\",\n    \"COUNTA\",\n    \"COUNTBLANK\",\n    \"COUNTIF\",\n    \"COUNTIFS\",\n    \"COUPDAYBS\",\n    \"COUPDAYS\",\n    \"COUPDAYSNC\",\n    \"COUPNCD\",\n    \"COUPNUM\",\n    \"COUPPCD\",\n    \"COVAR\",\n    \"COVARIANCE.P\",\n    \"COVARIANCE.S\",\n    \"CRITBINOM\",\n    \"CSC\",\n    \"CSCH\",\n    \"CUBEKPIMEMBER\",\n    \"CUBEMEMBER\",\n    \"CUBEMEMBERPROPERTY\",\n    \"CUBERANKEDMEMBER\",\n    \"CUBESET\",\n    \"CUBESETCOUNT\",\n    \"CUBEVALUE\",\n    \"CUMIPMT\",\n    \"CUMPRINC\",\n    \"DATE\",\n    \"DATEDIF\",\n    \"DATEVALUE\",\n    \"DAVERAGE\",\n    \"DAY\",\n    \"DAYS\",\n    \"DAYS360\",\n    \"DB\",\n    \"DBCS\",\n    \"DCOUNT\",\n    \"DCOUNTA\",\n    \"DDB\",\n    \"DEC2BIN\",\n    \"DEC2HEX\",\n    \"DEC2OCT\",\n    \"DECIMAL\",\n    \"DEGREES\",\n    \"DELTA\",\n    \"DEVSQ\",\n    \"DGET\",\n    \"DISC\",\n    \"DMAX\",\n    \"DMIN\",\n    \"DOLLAR\",\n    \"DOLLARDE\",\n    \"DOLLARFR\",\n    \"DPRODUCT\",\n    \"DSTDEV\",\n    \"DSTDEVP\",\n    \"DSUM\",\n    \"DURATION\",\n    \"DVAR\",\n    \"DVARP\",\n    \"EDATE\",\n    \"EFFECT\",\n    \"ENCODEURL\",\n    \"EOMONTH\",\n    \"ERF\",\n    \"ERF.PRECISE\",\n    \"ERFC\",\n    \"ERFC.PRECISE\",\n    \"ERROR.TYPE\",\n    \"EUROCONVERT\",\n    \"EVEN\",\n    \"EXACT\",\n    \"EXP\",\n    \"EXPON.DIST\",\n    \"EXPONDIST\",\n    \"FACT\",\n    \"FACTDOUBLE\",\n    \"FALSE|0\",\n    \"F.DIST\",\n    \"FDIST\",\n    \"F.DIST.RT\",\n    \"FILTERXML\",\n    \"FIND\",\n    \"FINDB\",\n    \"F.INV\",\n    \"F.INV.RT\",\n    \"FINV\",\n    \"FISHER\",\n    \"FISHERINV\",\n    \"FIXED\",\n    \"FLOOR\",\n    \"FLOOR.MATH\",\n    \"FLOOR.PRECISE\",\n    \"FORECAST\",\n    \"FORECAST.ETS\",\n    \"FORECAST.ETS.CONFINT\",\n    \"FORECAST.ETS.SEASONALITY\",\n    \"FORECAST.ETS.STAT\",\n    \"FORECAST.LINEAR\",\n    \"FORMULATEXT\",\n    \"FREQUENCY\",\n    \"F.TEST\",\n    \"FTEST\",\n    \"FV\",\n    \"FVSCHEDULE\",\n    \"GAMMA\",\n    \"GAMMA.DIST\",\n    \"GAMMADIST\",\n    \"GAMMA.INV\",\n    \"GAMMAINV\",\n    \"GAMMALN\",\n    \"GAMMALN.PRECISE\",\n    \"GAUSS\",\n    \"GCD\",\n    \"GEOMEAN\",\n    \"GESTEP\",\n    \"GETPIVOTDATA\",\n    \"GROWTH\",\n    \"HARMEAN\",\n    \"HEX2BIN\",\n    \"HEX2DEC\",\n    \"HEX2OCT\",\n    \"HLOOKUP\",\n    \"HOUR\",\n    \"HYPERLINK\",\n    \"HYPGEOM.DIST\",\n    \"HYPGEOMDIST\",\n    \"IF\",\n    \"IFERROR\",\n    \"IFNA\",\n    \"IFS\",\n    \"IMABS\",\n    \"IMAGINARY\",\n    \"IMARGUMENT\",\n    \"IMCONJUGATE\",\n    \"IMCOS\",\n    \"IMCOSH\",\n    \"IMCOT\",\n    \"IMCSC\",\n    \"IMCSCH\",\n    \"IMDIV\",\n    \"IMEXP\",\n    \"IMLN\",\n    \"IMLOG10\",\n    \"IMLOG2\",\n    \"IMPOWER\",\n    \"IMPRODUCT\",\n    \"IMREAL\",\n    \"IMSEC\",\n    \"IMSECH\",\n    \"IMSIN\",\n    \"IMSINH\",\n    \"IMSQRT\",\n    \"IMSUB\",\n    \"IMSUM\",\n    \"IMTAN\",\n    \"INDEX\",\n    \"INDIRECT\",\n    \"INFO\",\n    \"INT\",\n    \"INTERCEPT\",\n    \"INTRATE\",\n    \"IPMT\",\n    \"IRR\",\n    \"ISBLANK\",\n    \"ISERR\",\n    \"ISERROR\",\n    \"ISEVEN\",\n    \"ISFORMULA\",\n    \"ISLOGICAL\",\n    \"ISNA\",\n    \"ISNONTEXT\",\n    \"ISNUMBER\",\n    \"ISODD\",\n    \"ISREF\",\n    \"ISTEXT\",\n    \"ISO.CEILING\",\n    \"ISOWEEKNUM\",\n    \"ISPMT\",\n    \"JIS\",\n    \"KURT\",\n    \"LARGE\",\n    \"LCM\",\n    \"LEFT\",\n    \"LEFTB\",\n    \"LEN\",\n    \"LENB\",\n    \"LINEST\",\n    \"LN\",\n    \"LOG\",\n    \"LOG10\",\n    \"LOGEST\",\n    \"LOGINV\",\n    \"LOGNORM.DIST\",\n    \"LOGNORMDIST\",\n    \"LOGNORM.INV\",\n    \"LOOKUP\",\n    \"LOWER\",\n    \"MATCH\",\n    \"MAX\",\n    \"MAXA\",\n    \"MAXIFS\",\n    \"MDETERM\",\n    \"MDURATION\",\n    \"MEDIAN\",\n    \"MID\",\n    \"MIDBs\",\n    \"MIN\",\n    \"MINIFS\",\n    \"MINA\",\n    \"MINUTE\",\n    \"MINVERSE\",\n    \"MIRR\",\n    \"MMULT\",\n    \"MOD\",\n    \"MODE\",\n    \"MODE.MULT\",\n    \"MODE.SNGL\",\n    \"MONTH\",\n    \"MROUND\",\n    \"MULTINOMIAL\",\n    \"MUNIT\",\n    \"N\",\n    \"NA\",\n    \"NEGBINOM.DIST\",\n    \"NEGBINOMDIST\",\n    \"NETWORKDAYS\",\n    \"NETWORKDAYS.INTL\",\n    \"NOMINAL\",\n    \"NORM.DIST\",\n    \"NORMDIST\",\n    \"NORMINV\",\n    \"NORM.INV\",\n    \"NORM.S.DIST\",\n    \"NORMSDIST\",\n    \"NORM.S.INV\",\n    \"NORMSINV\",\n    \"NOT\",\n    \"NOW\",\n    \"NPER\",\n    \"NPV\",\n    \"NUMBERVALUE\",\n    \"OCT2BIN\",\n    \"OCT2DEC\",\n    \"OCT2HEX\",\n    \"ODD\",\n    \"ODDFPRICE\",\n    \"ODDFYIELD\",\n    \"ODDLPRICE\",\n    \"ODDLYIELD\",\n    \"OFFSET\",\n    \"OR\",\n    \"PDURATION\",\n    \"PEARSON\",\n    \"PERCENTILE.EXC\",\n    \"PERCENTILE.INC\",\n    \"PERCENTILE\",\n    \"PERCENTRANK.EXC\",\n    \"PERCENTRANK.INC\",\n    \"PERCENTRANK\",\n    \"PERMUT\",\n    \"PERMUTATIONA\",\n    \"PHI\",\n    \"PHONETIC\",\n    \"PI\",\n    \"PMT\",\n    \"POISSON.DIST\",\n    \"POISSON\",\n    \"POWER\",\n    \"PPMT\",\n    \"PRICE\",\n    \"PRICEDISC\",\n    \"PRICEMAT\",\n    \"PROB\",\n    \"PRODUCT\",\n    \"PROPER\",\n    \"PV\",\n    \"QUARTILE\",\n    \"QUARTILE.EXC\",\n    \"QUARTILE.INC\",\n    \"QUOTIENT\",\n    \"RADIANS\",\n    \"RAND\",\n    \"RANDBETWEEN\",\n    \"RANK.AVG\",\n    \"RANK.EQ\",\n    \"RANK\",\n    \"RATE\",\n    \"RECEIVED\",\n    \"REGISTER.ID\",\n    \"REPLACE\",\n    \"REPLACEB\",\n    \"REPT\",\n    \"RIGHT\",\n    \"RIGHTB\",\n    \"ROMAN\",\n    \"ROUND\",\n    \"ROUNDDOWN\",\n    \"ROUNDUP\",\n    \"ROW\",\n    \"ROWS\",\n    \"RRI\",\n    \"RSQ\",\n    \"RTD\",\n    \"SEARCH\",\n    \"SEARCHB\",\n    \"SEC\",\n    \"SECH\",\n    \"SECOND\",\n    \"SERIESSUM\",\n    \"SHEET\",\n    \"SHEETS\",\n    \"SIGN\",\n    \"SIN\",\n    \"SINH\",\n    \"SKEW\",\n    \"SKEW.P\",\n    \"SLN\",\n    \"SLOPE\",\n    \"SMALL\",\n    \"SQL.REQUEST\",\n    \"SQRT\",\n    \"SQRTPI\",\n    \"STANDARDIZE\",\n    \"STDEV\",\n    \"STDEV.P\",\n    \"STDEV.S\",\n    \"STDEVA\",\n    \"STDEVP\",\n    \"STDEVPA\",\n    \"STEYX\",\n    \"SUBSTITUTE\",\n    \"SUBTOTAL\",\n    \"SUM\",\n    \"SUMIF\",\n    \"SUMIFS\",\n    \"SUMPRODUCT\",\n    \"SUMSQ\",\n    \"SUMX2MY2\",\n    \"SUMX2PY2\",\n    \"SUMXMY2\",\n    \"SWITCH\",\n    \"SYD\",\n    \"T\",\n    \"TAN\",\n    \"TANH\",\n    \"TBILLEQ\",\n    \"TBILLPRICE\",\n    \"TBILLYIELD\",\n    \"T.DIST\",\n    \"T.DIST.2T\",\n    \"T.DIST.RT\",\n    \"TDIST\",\n    \"TEXT\",\n    \"TEXTJOIN\",\n    \"TIME\",\n    \"TIMEVALUE\",\n    \"T.INV\",\n    \"T.INV.2T\",\n    \"TINV\",\n    \"TODAY\",\n    \"TRANSPOSE\",\n    \"TREND\",\n    \"TRIM\",\n    \"TRIMMEAN\",\n    \"TRUE|0\",\n    \"TRUNC\",\n    \"T.TEST\",\n    \"TTEST\",\n    \"TYPE\",\n    \"UNICHAR\",\n    \"UNICODE\",\n    \"UPPER\",\n    \"VALUE\",\n    \"VAR\",\n    \"VAR.P\",\n    \"VAR.S\",\n    \"VARA\",\n    \"VARP\",\n    \"VARPA\",\n    \"VDB\",\n    \"VLOOKUP\",\n    \"WEBSERVICE\",\n    \"WEEKDAY\",\n    \"WEEKNUM\",\n    \"WEIBULL\",\n    \"WEIBULL.DIST\",\n    \"WORKDAY\",\n    \"WORKDAY.INTL\",\n    \"XIRR\",\n    \"XNPV\",\n    \"XOR\",\n    \"YEAR\",\n    \"YEARFRAC\",\n    \"YIELD\",\n    \"YIELDDISC\",\n    \"YIELDMAT\",\n    \"Z.TEST\",\n    \"ZTEST\"\n  ];\n  return {\n    name: 'Excel formulae',\n    aliases: [\n      'xlsx',\n      'xls'\n    ],\n    case_insensitive: true,\n    keywords: {\n      $pattern: /[a-zA-Z][\\w\\.]*/,\n      built_in: BUILT_INS\n    },\n    contains: [\n      {\n        /* matches a beginning equal sign found in Excel formula examples */\n        begin: /^=/,\n        end: /[^=]/,\n        returnEnd: true,\n        illegal: /=/, /* only allow single equal sign at front of line */\n        relevance: 10\n      },\n      /* technically, there can be more than 2 letters in column names, but this prevents conflict with some keywords */\n      {\n        /* matches a reference to a single cell */\n        className: 'symbol',\n        begin: /\\b[A-Z]{1,2}\\d+\\b/,\n        end: /[^\\d]/,\n        excludeEnd: true,\n        relevance: 0\n      },\n      {\n        /* matches a reference to a range of cells */\n        className: 'symbol',\n        begin: /[A-Z]{0,2}\\d*:[A-Z]{0,2}\\d*/,\n        relevance: 0\n      },\n      hljs.BACKSLASH_ESCAPE,\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'number',\n        begin: hljs.NUMBER_RE + '(%)?',\n        relevance: 0\n      },\n      /* Excel formula comments are done by putting the comment in a function call to N() */\n      hljs.COMMENT(/\\bN\\(/, /\\)/,\n        {\n          excludeBegin: true,\n          excludeEnd: true,\n          illegal: /\\n/\n        })\n    ]\n  };\n}\n\nmodule.exports = excel;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,KAAKA,CAACC,IAAI,EAAE;EACnB;EACA,MAAMC,SAAS,GAAG,CAChB,KAAK,EACL,SAAS,EACT,UAAU,EACV,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,WAAW,EACX,SAAS,EACT,WAAW,EACX,UAAU,EACV,KAAK,EACL,QAAQ,EACR,OAAO,EACP,KAAK,EACL,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,OAAO,EACP,QAAQ,EACR,SAAS,EACT,UAAU,EACV,WAAW,EACX,YAAY,EACZ,UAAU,EACV,MAAM,EACN,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,UAAU,EACV,WAAW,EACX,SAAS,EACT,UAAU,EACV,SAAS,EACT,SAAS,EACT,SAAS,EACT,WAAW,EACX,YAAY,EACZ,kBAAkB,EAClB,WAAW,EACX,QAAQ,EACR,WAAW,EACX,OAAO,EACP,WAAW,EACX,QAAQ,EACR,MAAM,EACN,SAAS,EACT,cAAc,EACd,iBAAiB,EACjB,MAAM,EACN,MAAM,EACN,SAAS,EACT,QAAQ,EACR,SAAS,EACT,YAAY,EACZ,eAAe,EACf,WAAW,EACX,cAAc,EACd,YAAY,EACZ,QAAQ,EACR,OAAO,EACP,MAAM,EACN,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,SAAS,EACT,QAAQ,EACR,aAAa,EACb,YAAY,EACZ,iBAAiB,EACjB,cAAc,EACd,SAAS,EACT,QAAQ,EACR,KAAK,EACL,MAAM,EACN,KAAK,EACL,MAAM,EACN,OAAO,EACP,QAAQ,EACR,YAAY,EACZ,SAAS,EACT,UAAU,EACV,WAAW,EACX,UAAU,EACV,YAAY,EACZ,SAAS,EACT,SAAS,EACT,SAAS,EACT,OAAO,EACP,cAAc,EACd,cAAc,EACd,WAAW,EACX,KAAK,EACL,MAAM,EACN,eAAe,EACf,YAAY,EACZ,oBAAoB,EACpB,kBAAkB,EAClB,SAAS,EACT,cAAc,EACd,WAAW,EACX,SAAS,EACT,UAAU,EACV,MAAM,EACN,SAAS,EACT,WAAW,EACX,UAAU,EACV,KAAK,EACL,MAAM,EACN,SAAS,EACT,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,SAAS,EACT,KAAK,EACL,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,OAAO,EACP,OAAO,EACP,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,QAAQ,EACR,UAAU,EACV,UAAU,EACV,UAAU,EACV,QAAQ,EACR,SAAS,EACT,MAAM,EACN,UAAU,EACV,MAAM,EACN,OAAO,EACP,OAAO,EACP,QAAQ,EACR,WAAW,EACX,SAAS,EACT,KAAK,EACL,aAAa,EACb,MAAM,EACN,cAAc,EACd,YAAY,EACZ,aAAa,EACb,MAAM,EACN,OAAO,EACP,KAAK,EACL,YAAY,EACZ,WAAW,EACX,MAAM,EACN,YAAY,EACZ,SAAS,EACT,QAAQ,EACR,OAAO,EACP,WAAW,EACX,WAAW,EACX,MAAM,EACN,OAAO,EACP,OAAO,EACP,UAAU,EACV,MAAM,EACN,QAAQ,EACR,WAAW,EACX,OAAO,EACP,OAAO,EACP,YAAY,EACZ,eAAe,EACf,UAAU,EACV,cAAc,EACd,sBAAsB,EACtB,0BAA0B,EAC1B,mBAAmB,EACnB,iBAAiB,EACjB,aAAa,EACb,WAAW,EACX,QAAQ,EACR,OAAO,EACP,IAAI,EACJ,YAAY,EACZ,OAAO,EACP,YAAY,EACZ,WAAW,EACX,WAAW,EACX,UAAU,EACV,SAAS,EACT,iBAAiB,EACjB,OAAO,EACP,KAAK,EACL,SAAS,EACT,QAAQ,EACR,cAAc,EACd,QAAQ,EACR,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,MAAM,EACN,WAAW,EACX,cAAc,EACd,aAAa,EACb,IAAI,EACJ,SAAS,EACT,MAAM,EACN,KAAK,EACL,OAAO,EACP,WAAW,EACX,YAAY,EACZ,aAAa,EACb,OAAO,EACP,QAAQ,EACR,OAAO,EACP,OAAO,EACP,QAAQ,EACR,OAAO,EACP,OAAO,EACP,MAAM,EACN,SAAS,EACT,QAAQ,EACR,SAAS,EACT,WAAW,EACX,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,UAAU,EACV,MAAM,EACN,KAAK,EACL,WAAW,EACX,SAAS,EACT,MAAM,EACN,KAAK,EACL,SAAS,EACT,OAAO,EACP,SAAS,EACT,QAAQ,EACR,WAAW,EACX,WAAW,EACX,MAAM,EACN,WAAW,EACX,UAAU,EACV,OAAO,EACP,OAAO,EACP,QAAQ,EACR,aAAa,EACb,YAAY,EACZ,OAAO,EACP,KAAK,EACL,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,cAAc,EACd,aAAa,EACb,aAAa,EACb,QAAQ,EACR,OAAO,EACP,OAAO,EACP,KAAK,EACL,MAAM,EACN,QAAQ,EACR,SAAS,EACT,WAAW,EACX,QAAQ,EACR,KAAK,EACL,OAAO,EACP,KAAK,EACL,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,UAAU,EACV,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,WAAW,EACX,WAAW,EACX,OAAO,EACP,QAAQ,EACR,aAAa,EACb,OAAO,EACP,GAAG,EACH,IAAI,EACJ,eAAe,EACf,cAAc,EACd,aAAa,EACb,kBAAkB,EAClB,SAAS,EACT,WAAW,EACX,UAAU,EACV,SAAS,EACT,UAAU,EACV,aAAa,EACb,WAAW,EACX,YAAY,EACZ,UAAU,EACV,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,aAAa,EACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,KAAK,EACL,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,QAAQ,EACR,IAAI,EACJ,WAAW,EACX,SAAS,EACT,gBAAgB,EAChB,gBAAgB,EAChB,YAAY,EACZ,iBAAiB,EACjB,iBAAiB,EACjB,aAAa,EACb,QAAQ,EACR,cAAc,EACd,KAAK,EACL,UAAU,EACV,IAAI,EACJ,KAAK,EACL,cAAc,EACd,SAAS,EACT,OAAO,EACP,MAAM,EACN,OAAO,EACP,WAAW,EACX,UAAU,EACV,MAAM,EACN,SAAS,EACT,QAAQ,EACR,IAAI,EACJ,UAAU,EACV,cAAc,EACd,cAAc,EACd,UAAU,EACV,SAAS,EACT,MAAM,EACN,aAAa,EACb,UAAU,EACV,SAAS,EACT,MAAM,EACN,MAAM,EACN,UAAU,EACV,aAAa,EACb,SAAS,EACT,UAAU,EACV,MAAM,EACN,OAAO,EACP,QAAQ,EACR,OAAO,EACP,OAAO,EACP,WAAW,EACX,SAAS,EACT,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,QAAQ,EACR,SAAS,EACT,KAAK,EACL,MAAM,EACN,QAAQ,EACR,WAAW,EACX,OAAO,EACP,QAAQ,EACR,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,QAAQ,EACR,KAAK,EACL,OAAO,EACP,OAAO,EACP,aAAa,EACb,MAAM,EACN,QAAQ,EACR,aAAa,EACb,OAAO,EACP,SAAS,EACT,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,OAAO,EACP,YAAY,EACZ,UAAU,EACV,KAAK,EACL,OAAO,EACP,QAAQ,EACR,YAAY,EACZ,OAAO,EACP,UAAU,EACV,UAAU,EACV,SAAS,EACT,QAAQ,EACR,KAAK,EACL,GAAG,EACH,KAAK,EACL,MAAM,EACN,SAAS,EACT,YAAY,EACZ,YAAY,EACZ,QAAQ,EACR,WAAW,EACX,WAAW,EACX,OAAO,EACP,MAAM,EACN,UAAU,EACV,MAAM,EACN,WAAW,EACX,OAAO,EACP,UAAU,EACV,MAAM,EACN,OAAO,EACP,WAAW,EACX,OAAO,EACP,MAAM,EACN,UAAU,EACV,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,OAAO,EACP,MAAM,EACN,SAAS,EACT,SAAS,EACT,OAAO,EACP,OAAO,EACP,KAAK,EACL,OAAO,EACP,OAAO,EACP,MAAM,EACN,MAAM,EACN,OAAO,EACP,KAAK,EACL,SAAS,EACT,YAAY,EACZ,SAAS,EACT,SAAS,EACT,SAAS,EACT,cAAc,EACd,SAAS,EACT,cAAc,EACd,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM,EACN,UAAU,EACV,OAAO,EACP,WAAW,EACX,UAAU,EACV,QAAQ,EACR,OAAO,CACR;EACD,OAAO;IACLC,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE,CACP,MAAM,EACN,KAAK,CACN;IACDC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE;MACRC,QAAQ,EAAE,iBAAiB;MAC3BC,QAAQ,EAAEN;IACZ,CAAC;IACDO,QAAQ,EAAE,CACR;MACE;MACAC,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,MAAM;MACXC,SAAS,EAAE,IAAI;MACfC,OAAO,EAAE,GAAG;MAAE;MACdC,SAAS,EAAE;IACb,CAAC,EACD;IACA;MACE;MACAC,SAAS,EAAE,QAAQ;MACnBL,KAAK,EAAE,mBAAmB;MAC1BC,GAAG,EAAE,OAAO;MACZK,UAAU,EAAE,IAAI;MAChBF,SAAS,EAAE;IACb,CAAC,EACD;MACE;MACAC,SAAS,EAAE,QAAQ;MACnBL,KAAK,EAAE,6BAA6B;MACpCI,SAAS,EAAE;IACb,CAAC,EACDb,IAAI,CAACgB,gBAAgB,EACrBhB,IAAI,CAACiB,iBAAiB,EACtB;MACEH,SAAS,EAAE,QAAQ;MACnBL,KAAK,EAAET,IAAI,CAACkB,SAAS,GAAG,MAAM;MAC9BL,SAAS,EAAE;IACb,CAAC,EACD;IACAb,IAAI,CAACmB,OAAO,CAAC,OAAO,EAAE,IAAI,EACxB;MACEC,YAAY,EAAE,IAAI;MAClBL,UAAU,EAAE,IAAI;MAChBH,OAAO,EAAE;IACX,CAAC,CAAC;EAER,CAAC;AACH;AAEAS,MAAM,CAACC,OAAO,GAAGvB,KAAK"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}