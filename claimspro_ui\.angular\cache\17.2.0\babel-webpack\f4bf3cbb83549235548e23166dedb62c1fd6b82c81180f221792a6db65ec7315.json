{"ast": null, "code": "import { UntypedFormControl } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/material/form-field\";\nimport * as i4 from \"@angular/material/core\";\nimport * as i5 from \"@angular/material/select\";\nfunction MultipleSelectComponent_mat_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 2);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const topping_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", topping_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(topping_r1);\n  }\n}\nexport class MultipleSelectComponent {\n  constructor() {\n    this.toppings = new UntypedFormControl();\n    this.toppingList = ['Extra cheese', 'Mushroom', 'Onion', 'Pepperoni', 'Sausage', 'Tomato'];\n  }\n  ngOnInit() {}\n  static #_ = this.ɵfac = function MultipleSelectComponent_Factory(t) {\n    return new (t || MultipleSelectComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MultipleSelectComponent,\n    selectors: [[\"app-multiple-select\"]],\n    decls: 7,\n    vars: 2,\n    consts: [[\"multiple\", \"\", 3, \"formControl\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"]],\n    template: function MultipleSelectComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"p\");\n        i0.ɵɵtext(1, \" multiple-select works!\\n\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"mat-form-field\")(3, \"mat-label\");\n        i0.ɵɵtext(4, \"Toppings\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"mat-select\", 0);\n        i0.ɵɵtemplate(6, MultipleSelectComponent_mat_option_6_Template, 2, 2, \"mat-option\", 1);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"formControl\", ctx.toppings);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngForOf\", ctx.toppingList);\n      }\n    },\n    dependencies: [i1.NgForOf, i2.NgControlStatus, i2.FormControlDirective, i3.MatFormField, i3.MatLabel, i4.MatOption, i5.MatSelect],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["UntypedFormControl", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "topping_r1", "ɵɵadvance", "ɵɵtextInterpolate", "MultipleSelectComponent", "constructor", "toppings", "toppingList", "ngOnInit", "_", "_2", "selectors", "decls", "vars", "consts", "template", "MultipleSelectComponent_Template", "rf", "ctx", "ɵɵtemplate", "MultipleSelectComponent_mat_option_6_Template"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\assets\\examples\\material\\multiple-select\\multiple-select.component.ts", "C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\assets\\examples\\material\\multiple-select\\multiple-select.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport {UntypedFormControl} from '@angular/forms';\n\n@Component({\n  selector: 'app-multiple-select',\n  templateUrl: './multiple-select.component.html',\n  styleUrls: ['./multiple-select.component.scss']\n})\nexport class MultipleSelectComponent implements OnInit {\n\n  toppings = new UntypedFormControl();\n  toppingList: string[] = ['Extra cheese', 'Mushroom', 'Onion', 'Pepperoni', 'Sausage', 'Tomato'];\n  \n  constructor() { }\n\n  ngOnInit() {\n  }\n\n}\n", "<p>\n  multiple-select works!\n</p>\n<mat-form-field>\n  <mat-label>Toppings</mat-label>\n  <mat-select [formControl]=\"toppings\" multiple>\n    <mat-option *ngFor=\"let topping of toppingList\" [value]=\"topping\">{{topping}}</mat-option>\n  </mat-select>\n</mat-form-field>\n"], "mappings": "AACA,SAAQA,kBAAkB,QAAO,gBAAgB;;;;;;;;;ICK7CC,EAAA,CAAAC,cAAA,oBAAkE;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAA1CH,EAAA,CAAAI,UAAA,UAAAC,UAAA,CAAiB;IAACL,EAAA,CAAAM,SAAA,EAAW;IAAXN,EAAA,CAAAO,iBAAA,CAAAF,UAAA,CAAW;;;ADEjF,OAAM,MAAOG,uBAAuB;EAKlCC,YAAA;IAHA,KAAAC,QAAQ,GAAG,IAAIX,kBAAkB,EAAE;IACnC,KAAAY,WAAW,GAAa,CAAC,cAAc,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,CAAC;EAE/E;EAEhBC,QAAQA,CAAA,GACR;EAAC,QAAAC,CAAA,G;qBARUL,uBAAuB;EAAA;EAAA,QAAAM,EAAA,G;UAAvBN,uBAAuB;IAAAO,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCRpCrB,EAAA,CAAAC,cAAA,QAAG;QACDD,EAAA,CAAAE,MAAA,gCACF;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACJH,EAAA,CAAAC,cAAA,qBAAgB;QACHD,EAAA,CAAAE,MAAA,eAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAY;QAC/BH,EAAA,CAAAC,cAAA,oBAA8C;QAC5CD,EAAA,CAAAuB,UAAA,IAAAC,6CAAA,wBAA0F;QAC5FxB,EAAA,CAAAG,YAAA,EAAa;;;QAFDH,EAAA,CAAAM,SAAA,GAAwB;QAAxBN,EAAA,CAAAI,UAAA,gBAAAkB,GAAA,CAAAZ,QAAA,CAAwB;QACFV,EAAA,CAAAM,SAAA,EAAc;QAAdN,EAAA,CAAAI,UAAA,YAAAkB,GAAA,CAAAX,WAAA,CAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}