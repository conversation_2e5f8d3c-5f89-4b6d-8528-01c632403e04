{"ast": null, "code": "const isScrollable = el => {\n  const {\n    overflowY\n  } = getComputedStyle(el, null);\n  return overflowY !== 'visible' && overflowY !== 'clip';\n};\nclass Tooltip {\n  constructor(quill, boundsContainer) {\n    this.quill = quill;\n    this.boundsContainer = boundsContainer || document.body;\n    this.root = quill.addContainer('ql-tooltip');\n    // @ts-expect-error\n    this.root.innerHTML = this.constructor.TEMPLATE;\n    if (isScrollable(this.quill.root)) {\n      this.quill.root.addEventListener('scroll', () => {\n        this.root.style.marginTop = `${-1 * this.quill.root.scrollTop}px`;\n      });\n    }\n    this.hide();\n  }\n  hide() {\n    this.root.classList.add('ql-hidden');\n  }\n  position(reference) {\n    const left = reference.left + reference.width / 2 - this.root.offsetWidth / 2;\n    // root.scrollTop should be 0 if scrollContainer !== root\n    const top = reference.bottom + this.quill.root.scrollTop;\n    this.root.style.left = `${left}px`;\n    this.root.style.top = `${top}px`;\n    this.root.classList.remove('ql-flip');\n    const containerBounds = this.boundsContainer.getBoundingClientRect();\n    const rootBounds = this.root.getBoundingClientRect();\n    let shift = 0;\n    if (rootBounds.right > containerBounds.right) {\n      shift = containerBounds.right - rootBounds.right;\n      this.root.style.left = `${left + shift}px`;\n    }\n    if (rootBounds.left < containerBounds.left) {\n      shift = containerBounds.left - rootBounds.left;\n      this.root.style.left = `${left + shift}px`;\n    }\n    if (rootBounds.bottom > containerBounds.bottom) {\n      const height = rootBounds.bottom - rootBounds.top;\n      const verticalShift = reference.bottom - reference.top + height;\n      this.root.style.top = `${top - verticalShift}px`;\n      this.root.classList.add('ql-flip');\n    }\n    return shift;\n  }\n  show() {\n    this.root.classList.remove('ql-editing');\n    this.root.classList.remove('ql-hidden');\n  }\n}\nexport default Tooltip;", "map": {"version": 3, "names": ["isScrollable", "el", "overflowY", "getComputedStyle", "<PERSON><PERSON><PERSON>", "constructor", "quill", "boundsContainer", "document", "body", "root", "addContainer", "innerHTML", "TEMPLATE", "addEventListener", "style", "marginTop", "scrollTop", "hide", "classList", "add", "position", "reference", "left", "width", "offsetWidth", "top", "bottom", "remove", "containerBounds", "getBoundingClientRect", "rootBounds", "shift", "right", "height", "verticalShift", "show"], "sources": ["C:/OneDrive/Srikant/SWIP/ClaimsPro/claimspro200-src/claimspro_ui/node_modules/quill/ui/tooltip.js"], "sourcesContent": ["const isScrollable = el => {\n  const {\n    overflowY\n  } = getComputedStyle(el, null);\n  return overflowY !== 'visible' && overflowY !== 'clip';\n};\nclass Tooltip {\n  constructor(quill, boundsContainer) {\n    this.quill = quill;\n    this.boundsContainer = boundsContainer || document.body;\n    this.root = quill.addContainer('ql-tooltip');\n    // @ts-expect-error\n    this.root.innerHTML = this.constructor.TEMPLATE;\n    if (isScrollable(this.quill.root)) {\n      this.quill.root.addEventListener('scroll', () => {\n        this.root.style.marginTop = `${-1 * this.quill.root.scrollTop}px`;\n      });\n    }\n    this.hide();\n  }\n  hide() {\n    this.root.classList.add('ql-hidden');\n  }\n  position(reference) {\n    const left = reference.left + reference.width / 2 - this.root.offsetWidth / 2;\n    // root.scrollTop should be 0 if scrollContainer !== root\n    const top = reference.bottom + this.quill.root.scrollTop;\n    this.root.style.left = `${left}px`;\n    this.root.style.top = `${top}px`;\n    this.root.classList.remove('ql-flip');\n    const containerBounds = this.boundsContainer.getBoundingClientRect();\n    const rootBounds = this.root.getBoundingClientRect();\n    let shift = 0;\n    if (rootBounds.right > containerBounds.right) {\n      shift = containerBounds.right - rootBounds.right;\n      this.root.style.left = `${left + shift}px`;\n    }\n    if (rootBounds.left < containerBounds.left) {\n      shift = containerBounds.left - rootBounds.left;\n      this.root.style.left = `${left + shift}px`;\n    }\n    if (rootBounds.bottom > containerBounds.bottom) {\n      const height = rootBounds.bottom - rootBounds.top;\n      const verticalShift = reference.bottom - reference.top + height;\n      this.root.style.top = `${top - verticalShift}px`;\n      this.root.classList.add('ql-flip');\n    }\n    return shift;\n  }\n  show() {\n    this.root.classList.remove('ql-editing');\n    this.root.classList.remove('ql-hidden');\n  }\n}\nexport default Tooltip;\n"], "mappings": "AAAA,MAAMA,YAAY,GAAGC,EAAE,IAAI;EACzB,MAAM;IACJC;EACF,CAAC,GAAGC,gBAAgB,CAACF,EAAE,EAAE,IAAI,CAAC;EAC9B,OAAOC,SAAS,KAAK,SAAS,IAAIA,SAAS,KAAK,MAAM;AACxD,CAAC;AACD,MAAME,OAAO,CAAC;EACZC,WAAWA,CAACC,KAAK,EAAEC,eAAe,EAAE;IAClC,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,eAAe,GAAGA,eAAe,IAAIC,QAAQ,CAACC,IAAI;IACvD,IAAI,CAACC,IAAI,GAAGJ,KAAK,CAACK,YAAY,CAAC,YAAY,CAAC;IAC5C;IACA,IAAI,CAACD,IAAI,CAACE,SAAS,GAAG,IAAI,CAACP,WAAW,CAACQ,QAAQ;IAC/C,IAAIb,YAAY,CAAC,IAAI,CAACM,KAAK,CAACI,IAAI,CAAC,EAAE;MACjC,IAAI,CAACJ,KAAK,CAACI,IAAI,CAACI,gBAAgB,CAAC,QAAQ,EAAE,MAAM;QAC/C,IAAI,CAACJ,IAAI,CAACK,KAAK,CAACC,SAAS,GAAI,GAAE,CAAC,CAAC,GAAG,IAAI,CAACV,KAAK,CAACI,IAAI,CAACO,SAAU,IAAG;MACnE,CAAC,CAAC;IACJ;IACA,IAAI,CAACC,IAAI,CAAC,CAAC;EACb;EACAA,IAAIA,CAAA,EAAG;IACL,IAAI,CAACR,IAAI,CAACS,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;EACtC;EACAC,QAAQA,CAACC,SAAS,EAAE;IAClB,MAAMC,IAAI,GAAGD,SAAS,CAACC,IAAI,GAAGD,SAAS,CAACE,KAAK,GAAG,CAAC,GAAG,IAAI,CAACd,IAAI,CAACe,WAAW,GAAG,CAAC;IAC7E;IACA,MAAMC,GAAG,GAAGJ,SAAS,CAACK,MAAM,GAAG,IAAI,CAACrB,KAAK,CAACI,IAAI,CAACO,SAAS;IACxD,IAAI,CAACP,IAAI,CAACK,KAAK,CAACQ,IAAI,GAAI,GAAEA,IAAK,IAAG;IAClC,IAAI,CAACb,IAAI,CAACK,KAAK,CAACW,GAAG,GAAI,GAAEA,GAAI,IAAG;IAChC,IAAI,CAAChB,IAAI,CAACS,SAAS,CAACS,MAAM,CAAC,SAAS,CAAC;IACrC,MAAMC,eAAe,GAAG,IAAI,CAACtB,eAAe,CAACuB,qBAAqB,CAAC,CAAC;IACpE,MAAMC,UAAU,GAAG,IAAI,CAACrB,IAAI,CAACoB,qBAAqB,CAAC,CAAC;IACpD,IAAIE,KAAK,GAAG,CAAC;IACb,IAAID,UAAU,CAACE,KAAK,GAAGJ,eAAe,CAACI,KAAK,EAAE;MAC5CD,KAAK,GAAGH,eAAe,CAACI,KAAK,GAAGF,UAAU,CAACE,KAAK;MAChD,IAAI,CAACvB,IAAI,CAACK,KAAK,CAACQ,IAAI,GAAI,GAAEA,IAAI,GAAGS,KAAM,IAAG;IAC5C;IACA,IAAID,UAAU,CAACR,IAAI,GAAGM,eAAe,CAACN,IAAI,EAAE;MAC1CS,KAAK,GAAGH,eAAe,CAACN,IAAI,GAAGQ,UAAU,CAACR,IAAI;MAC9C,IAAI,CAACb,IAAI,CAACK,KAAK,CAACQ,IAAI,GAAI,GAAEA,IAAI,GAAGS,KAAM,IAAG;IAC5C;IACA,IAAID,UAAU,CAACJ,MAAM,GAAGE,eAAe,CAACF,MAAM,EAAE;MAC9C,MAAMO,MAAM,GAAGH,UAAU,CAACJ,MAAM,GAAGI,UAAU,CAACL,GAAG;MACjD,MAAMS,aAAa,GAAGb,SAAS,CAACK,MAAM,GAAGL,SAAS,CAACI,GAAG,GAAGQ,MAAM;MAC/D,IAAI,CAACxB,IAAI,CAACK,KAAK,CAACW,GAAG,GAAI,GAAEA,GAAG,GAAGS,aAAc,IAAG;MAChD,IAAI,CAACzB,IAAI,CAACS,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC;IACpC;IACA,OAAOY,KAAK;EACd;EACAI,IAAIA,CAAA,EAAG;IACL,IAAI,CAAC1B,IAAI,CAACS,SAAS,CAACS,MAAM,CAAC,YAAY,CAAC;IACxC,IAAI,CAAClB,IAAI,CAACS,SAAS,CAACS,MAAM,CAAC,WAAW,CAAC;EACzC;AACF;AACA,eAAexB,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}