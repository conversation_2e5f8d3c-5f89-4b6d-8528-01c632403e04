{"ast": null, "code": "import { MatSort } from '@angular/material/sort';\nimport { MatTableDataSource } from '@angular/material/table';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/table\";\nimport * as i2 from \"@angular/material/sort\";\nfunction SortingTableComponent_th_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 9);\n    i0.ɵɵtext(1, \" No. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SortingTableComponent_td_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r10.position, \" \");\n  }\n}\nfunction SortingTableComponent_th_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 9);\n    i0.ɵɵtext(1, \" Name \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SortingTableComponent_td_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r11 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r11.name, \" \");\n  }\n}\nfunction SortingTableComponent_th_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 9);\n    i0.ɵɵtext(1, \" Weight \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SortingTableComponent_td_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r12 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r12.weight, \" \");\n  }\n}\nfunction SortingTableComponent_th_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 9);\n    i0.ɵɵtext(1, \" Symbol \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SortingTableComponent_td_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r13 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", element_r13.symbol, \" \");\n  }\n}\nfunction SortingTableComponent_tr_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 11);\n  }\n}\nfunction SortingTableComponent_tr_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 12);\n  }\n}\nconst ELEMENT_DATA = [{\n  position: 1,\n  name: 'Hydrogen',\n  weight: 1.0079,\n  symbol: 'H'\n}, {\n  position: 2,\n  name: 'Helium',\n  weight: 4.0026,\n  symbol: 'He'\n}, {\n  position: 3,\n  name: 'Lithium',\n  weight: 6.941,\n  symbol: 'Li'\n}, {\n  position: 4,\n  name: 'Beryllium',\n  weight: 9.0122,\n  symbol: 'Be'\n}, {\n  position: 5,\n  name: 'Boron',\n  weight: 10.811,\n  symbol: 'B'\n}, {\n  position: 6,\n  name: 'Carbon',\n  weight: 12.0107,\n  symbol: 'C'\n}, {\n  position: 7,\n  name: 'Nitrogen',\n  weight: 14.0067,\n  symbol: 'N'\n}, {\n  position: 8,\n  name: 'Oxygen',\n  weight: 15.9994,\n  symbol: 'O'\n}, {\n  position: 9,\n  name: 'Fluorine',\n  weight: 18.9984,\n  symbol: 'F'\n}, {\n  position: 10,\n  name: 'Neon',\n  weight: 20.1797,\n  symbol: 'Ne'\n}];\nexport class SortingTableComponent {\n  constructor() {\n    this.displayedColumns = ['position', 'name', 'weight', 'symbol'];\n    this.dataSource = new MatTableDataSource(ELEMENT_DATA);\n  }\n  ngOnInit() {\n    this.dataSource.sort = this.sort;\n  }\n  static #_ = this.ɵfac = function SortingTableComponent_Factory(t) {\n    return new (t || SortingTableComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SortingTableComponent,\n    selectors: [[\"app-sorting-table\"]],\n    viewQuery: function SortingTableComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatSort, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n      }\n    },\n    decls: 15,\n    vars: 3,\n    consts: [[\"mat-table\", \"\", \"matSort\", \"\", 1, \"mat-elevation-z8\", 3, \"dataSource\"], [\"matColumnDef\", \"position\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"name\"], [\"matColumnDef\", \"weight\"], [\"matColumnDef\", \"symbol\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\"], [\"mat-cell\", \"\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"]],\n    template: function SortingTableComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"table\", 0);\n        i0.ɵɵelementContainerStart(1, 1);\n        i0.ɵɵtemplate(2, SortingTableComponent_th_2_Template, 2, 0, \"th\", 2)(3, SortingTableComponent_td_3_Template, 2, 1, \"td\", 3);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(4, 4);\n        i0.ɵɵtemplate(5, SortingTableComponent_th_5_Template, 2, 0, \"th\", 2)(6, SortingTableComponent_td_6_Template, 2, 1, \"td\", 3);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(7, 5);\n        i0.ɵɵtemplate(8, SortingTableComponent_th_8_Template, 2, 0, \"th\", 2)(9, SortingTableComponent_td_9_Template, 2, 1, \"td\", 3);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(10, 6);\n        i0.ɵɵtemplate(11, SortingTableComponent_th_11_Template, 2, 0, \"th\", 2)(12, SortingTableComponent_td_12_Template, 2, 1, \"td\", 3);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵtemplate(13, SortingTableComponent_tr_13_Template, 1, 0, \"tr\", 7)(14, SortingTableComponent_tr_14_Template, 1, 0, \"tr\", 8);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"dataSource\", ctx.dataSource);\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n      }\n    },\n    dependencies: [i1.MatTable, i1.MatHeaderCellDef, i1.MatHeaderRowDef, i1.MatColumnDef, i1.MatCellDef, i1.MatRowDef, i1.MatHeaderCell, i1.MatCell, i1.MatHeaderRow, i1.MatRow, i2.MatSort, i2.MatSortHeader],\n    styles: [\"table[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\nth.mat-sort-header-sorted[_ngcontent-%COMP%] {\\n  color: black;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hc3NldHMvZXhhbXBsZXMvbWF0ZXJpYWwvc29ydGluZy10YWJsZS9zb3J0aW5nLXRhYmxlLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksV0FBQTtBQUNKOztBQUVFO0VBQ0UsWUFBQTtBQUNKIiwic291cmNlc0NvbnRlbnQiOlsidGFibGUge1xuICAgIHdpZHRoOiAxMDAlO1xuICB9XG4gIFxuICB0aC5tYXQtc29ydC1oZWFkZXItc29ydGVkIHtcbiAgICBjb2xvcjogYmxhY2s7XG4gIH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["MatSort", "MatTableDataSource", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "element_r10", "position", "element_r11", "name", "element_r12", "weight", "element_r13", "symbol", "ɵɵelement", "ELEMENT_DATA", "SortingTableComponent", "constructor", "displayedColumns", "dataSource", "ngOnInit", "sort", "_", "_2", "selectors", "viewQuery", "SortingTableComponent_Query", "rf", "ctx", "ɵɵelementContainerStart", "ɵɵtemplate", "SortingTableComponent_th_2_Template", "SortingTableComponent_td_3_Template", "ɵɵelementContainerEnd", "SortingTableComponent_th_5_Template", "SortingTableComponent_td_6_Template", "SortingTableComponent_th_8_Template", "SortingTableComponent_td_9_Template", "SortingTableComponent_th_11_Template", "SortingTableComponent_td_12_Template", "SortingTableComponent_tr_13_Template", "SortingTableComponent_tr_14_Template", "ɵɵproperty"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\assets\\examples\\material\\sorting-table\\sorting-table.component.ts", "C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\assets\\examples\\material\\sorting-table\\sorting-table.component.html"], "sourcesContent": ["import {Component, OnInit, ViewChild} from '@angular/core';\nimport { MatSort } from '@angular/material/sort';\nimport { MatTableDataSource as MatTableDataSource } from '@angular/material/table';\n\nexport interface PeriodicElement {\n  name: string;\n  position: number;\n  weight: number;\n  symbol: string;\n}\n\nconst ELEMENT_DATA: PeriodicElement[] = [\n  {position: 1, name: 'Hydrogen', weight: 1.0079, symbol: 'H'},\n  {position: 2, name: 'Helium', weight: 4.0026, symbol: 'He'},\n  {position: 3, name: '<PERSON>thium', weight: 6.941, symbol: 'Li'},\n  {position: 4, name: 'Beryllium', weight: 9.0122, symbol: 'Be'},\n  {position: 5, name: '<PERSON><PERSON>', weight: 10.811, symbol: 'B'},\n  {position: 6, name: 'Carbon', weight: 12.0107, symbol: 'C'},\n  {position: 7, name: 'Nitrogen', weight: 14.0067, symbol: 'N'},\n  {position: 8, name: 'Oxygen', weight: 15.9994, symbol: 'O'},\n  {position: 9, name: 'Fluorine', weight: 18.9984, symbol: 'F'},\n  {position: 10, name: 'Neon', weight: 20.1797, symbol: 'Ne'},\n];\n\n@Component({\n  selector: 'app-sorting-table',\n  templateUrl: './sorting-table.component.html',\n  styleUrls: ['./sorting-table.component.scss']\n})\nexport class SortingTableComponent implements OnInit {\n\n  displayedColumns: string[] = ['position', 'name', 'weight', 'symbol'];\n  dataSource = new MatTableDataSource(ELEMENT_DATA);\n\n  @ViewChild(MatSort) sort: MatSort;\n\n  ngOnInit() {\n    this.dataSource.sort = this.sort;\n  }\n\n}\n", "<table mat-table [dataSource]=\"dataSource\" matSort class=\"mat-elevation-z8\">\n\n  <!-- Position Column -->\n  <ng-container matColumnDef=\"position\">\n    <th mat-header-cell *matHeaderCellDef mat-sort-header> No. </th>\n    <td mat-cell *matCellDef=\"let element\"> {{element.position}} </td>\n  </ng-container>\n\n  <!-- Name Column -->\n  <ng-container matColumnDef=\"name\">\n    <th mat-header-cell *matHeaderCellDef mat-sort-header> Name </th>\n    <td mat-cell *matCellDef=\"let element\"> {{element.name}} </td>\n  </ng-container>\n\n  <!-- Weight Column -->\n  <ng-container matColumnDef=\"weight\">\n    <th mat-header-cell *matHeaderCellDef mat-sort-header> Weight </th>\n    <td mat-cell *matCellDef=\"let element\"> {{element.weight}} </td>\n  </ng-container>\n\n  <!-- Symbol Column -->\n  <ng-container matColumnDef=\"symbol\">\n    <th mat-header-cell *matHeaderCellDef mat-sort-header> Symbol </th>\n    <td mat-cell *matCellDef=\"let element\"> {{element.symbol}} </td>\n  </ng-container>\n\n  <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n  <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\n</table>\n"], "mappings": "AACA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,SAASC,kBAAwC,QAAQ,yBAAyB;;;;;;ICE9EC,EAAA,CAAAC,cAAA,YAAsD;IAACD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAChEH,EAAA,CAAAC,cAAA,aAAuC;IAACD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAA1BH,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAK,kBAAA,MAAAC,WAAA,CAAAC,QAAA,MAAqB;;;;;IAK7DP,EAAA,CAAAC,cAAA,YAAsD;IAACD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACjEH,EAAA,CAAAC,cAAA,aAAuC;IAACD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAtBH,EAAA,CAAAI,SAAA,EAAiB;IAAjBJ,EAAA,CAAAK,kBAAA,MAAAG,WAAA,CAAAC,IAAA,MAAiB;;;;;IAKzDT,EAAA,CAAAC,cAAA,YAAsD;IAACD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACnEH,EAAA,CAAAC,cAAA,aAAuC;IAACD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAxBH,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAK,kBAAA,MAAAK,WAAA,CAAAC,MAAA,MAAmB;;;;;IAK3DX,EAAA,CAAAC,cAAA,YAAsD;IAACD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACnEH,EAAA,CAAAC,cAAA,aAAuC;IAACD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAxBH,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAK,kBAAA,MAAAO,WAAA,CAAAC,MAAA,MAAmB;;;;;IAG7Db,EAAA,CAAAc,SAAA,aAA4D;;;;;IAC5Dd,EAAA,CAAAc,SAAA,aAAkE;;;ADhBpE,MAAMC,YAAY,GAAsB,CACtC;EAACR,QAAQ,EAAE,CAAC;EAAEE,IAAI,EAAE,UAAU;EAAEE,MAAM,EAAE,MAAM;EAAEE,MAAM,EAAE;AAAG,CAAC,EAC5D;EAACN,QAAQ,EAAE,CAAC;EAAEE,IAAI,EAAE,QAAQ;EAAEE,MAAM,EAAE,MAAM;EAAEE,MAAM,EAAE;AAAI,CAAC,EAC3D;EAACN,QAAQ,EAAE,CAAC;EAAEE,IAAI,EAAE,SAAS;EAAEE,MAAM,EAAE,KAAK;EAAEE,MAAM,EAAE;AAAI,CAAC,EAC3D;EAACN,QAAQ,EAAE,CAAC;EAAEE,IAAI,EAAE,WAAW;EAAEE,MAAM,EAAE,MAAM;EAAEE,MAAM,EAAE;AAAI,CAAC,EAC9D;EAACN,QAAQ,EAAE,CAAC;EAAEE,IAAI,EAAE,OAAO;EAAEE,MAAM,EAAE,MAAM;EAAEE,MAAM,EAAE;AAAG,CAAC,EACzD;EAACN,QAAQ,EAAE,CAAC;EAAEE,IAAI,EAAE,QAAQ;EAAEE,MAAM,EAAE,OAAO;EAAEE,MAAM,EAAE;AAAG,CAAC,EAC3D;EAACN,QAAQ,EAAE,CAAC;EAAEE,IAAI,EAAE,UAAU;EAAEE,MAAM,EAAE,OAAO;EAAEE,MAAM,EAAE;AAAG,CAAC,EAC7D;EAACN,QAAQ,EAAE,CAAC;EAAEE,IAAI,EAAE,QAAQ;EAAEE,MAAM,EAAE,OAAO;EAAEE,MAAM,EAAE;AAAG,CAAC,EAC3D;EAACN,QAAQ,EAAE,CAAC;EAAEE,IAAI,EAAE,UAAU;EAAEE,MAAM,EAAE,OAAO;EAAEE,MAAM,EAAE;AAAG,CAAC,EAC7D;EAACN,QAAQ,EAAE,EAAE;EAAEE,IAAI,EAAE,MAAM;EAAEE,MAAM,EAAE,OAAO;EAAEE,MAAM,EAAE;AAAI,CAAC,CAC5D;AAOD,OAAM,MAAOG,qBAAqB;EALlCC,YAAA;IAOE,KAAAC,gBAAgB,GAAa,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC;IACrE,KAAAC,UAAU,GAAG,IAAIpB,kBAAkB,CAACgB,YAAY,CAAC;;EAIjDK,QAAQA,CAAA;IACN,IAAI,CAACD,UAAU,CAACE,IAAI,GAAG,IAAI,CAACA,IAAI;EAClC;EAAC,QAAAC,CAAA,G;qBATUN,qBAAqB;EAAA;EAAA,QAAAO,EAAA,G;UAArBP,qBAAqB;IAAAQ,SAAA;IAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBAKrB7B,OAAO;;;;;;;;;;;;QClCpBE,EAAA,CAAAC,cAAA,eAA4E;QAG1ED,EAAA,CAAA6B,uBAAA,MAAsC;QACpC7B,EAAA,CAAA8B,UAAA,IAAAC,mCAAA,gBAAgE,IAAAC,mCAAA;QAElEhC,EAAA,CAAAiC,qBAAA,EAAe;QAGfjC,EAAA,CAAA6B,uBAAA,MAAkC;QAChC7B,EAAA,CAAA8B,UAAA,IAAAI,mCAAA,gBAAiE,IAAAC,mCAAA;QAEnEnC,EAAA,CAAAiC,qBAAA,EAAe;QAGfjC,EAAA,CAAA6B,uBAAA,MAAoC;QAClC7B,EAAA,CAAA8B,UAAA,IAAAM,mCAAA,gBAAmE,IAAAC,mCAAA;QAErErC,EAAA,CAAAiC,qBAAA,EAAe;QAGfjC,EAAA,CAAA6B,uBAAA,OAAoC;QAClC7B,EAAA,CAAA8B,UAAA,KAAAQ,oCAAA,gBAAmE,KAAAC,oCAAA;QAErEvC,EAAA,CAAAiC,qBAAA,EAAe;QAEfjC,EAAA,CAAA8B,UAAA,KAAAU,oCAAA,gBAA4D,KAAAC,oCAAA;QAE9DzC,EAAA,CAAAG,YAAA,EAAQ;;;QA5BSH,EAAA,CAAA0C,UAAA,eAAAd,GAAA,CAAAT,UAAA,CAAyB;QA0BpBnB,EAAA,CAAAI,SAAA,IAAiC;QAAjCJ,EAAA,CAAA0C,UAAA,oBAAAd,GAAA,CAAAV,gBAAA,CAAiC;QACpBlB,EAAA,CAAAI,SAAA,EAA0B;QAA1BJ,EAAA,CAAA0C,UAAA,qBAAAd,GAAA,CAAAV,gBAAA,CAA0B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}