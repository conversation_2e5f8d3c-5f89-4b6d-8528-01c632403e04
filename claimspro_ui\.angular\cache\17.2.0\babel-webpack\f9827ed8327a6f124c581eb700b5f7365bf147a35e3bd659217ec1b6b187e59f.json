{"ast": null, "code": "/*\nLanguage: STEP Part 21\nContributors: <PERSON> <<EMAIL>>\nDescription: Syntax highlighter for STEP Part 21 files (ISO 10303-21).\nWebsite: https://en.wikipedia.org/wiki/ISO_10303-21\n*/\n\nfunction step21(hljs) {\n  const STEP21_IDENT_RE = '[A-Z_][A-Z0-9_.]*';\n  const STEP21_KEYWORDS = {\n    $pattern: STEP21_IDENT_RE,\n    keyword: [\"HEADER\", \"ENDSEC\", \"DATA\"]\n  };\n  const STEP21_START = {\n    className: 'meta',\n    begin: 'ISO-10303-21;',\n    relevance: 10\n  };\n  const STEP21_CLOSE = {\n    className: 'meta',\n    begin: 'END-ISO-10303-21;',\n    relevance: 10\n  };\n  return {\n    name: 'STEP Part 21',\n    aliases: ['p21', 'step', 'stp'],\n    case_insensitive: true,\n    // STEP 21 is case insensitive in theory, in practice all non-comments are capitalized.\n    keywords: STEP21_KEYWOR<PERSON>,\n    contains: [STEP21_START, STEP21_CLOSE, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, hljs.COMMENT('/\\\\*\\\\*!', '\\\\*/'), hljs.C_NUMBER_MODE, hljs.inherit(hljs.APOS_STRING_MODE, {\n      illegal: null\n    }), hljs.inherit(hljs.QUOTE_STRING_MODE, {\n      illegal: null\n    }), {\n      className: 'string',\n      begin: \"'\",\n      end: \"'\"\n    }, {\n      className: 'symbol',\n      variants: [{\n        begin: '#',\n        end: '\\\\d+',\n        illegal: '\\\\W'\n      }]\n    }]\n  };\n}\nmodule.exports = step21;", "map": {"version": 3, "names": ["step21", "hljs", "STEP21_IDENT_RE", "STEP21_KEYWORDS", "$pattern", "keyword", "STEP21_START", "className", "begin", "relevance", "STEP21_CLOSE", "name", "aliases", "case_insensitive", "keywords", "contains", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "COMMENT", "C_NUMBER_MODE", "inherit", "APOS_STRING_MODE", "illegal", "QUOTE_STRING_MODE", "end", "variants", "module", "exports"], "sources": ["C:/OneDrive/Srikant/SWIP/ClaimsPro/claimspro200-src/claimspro_ui/node_modules/highlight.js/lib/languages/step21.js"], "sourcesContent": ["/*\nLanguage: STEP Part 21\nContributors: <PERSON> <<EMAIL>>\nDescription: Syntax highlighter for STEP Part 21 files (ISO 10303-21).\nWebsite: https://en.wikipedia.org/wiki/ISO_10303-21\n*/\n\nfunction step21(hljs) {\n  const STEP21_IDENT_RE = '[A-Z_][A-Z0-9_.]*';\n  const STEP21_KEYWORDS = {\n    $pattern: STEP21_IDENT_RE,\n    keyword: [\n      \"HEADER\",\n      \"ENDSEC\",\n      \"DATA\"\n    ]\n  };\n  const STEP21_START = {\n    className: 'meta',\n    begin: 'ISO-10303-21;',\n    relevance: 10\n  };\n  const STEP21_CLOSE = {\n    className: 'meta',\n    begin: 'END-ISO-10303-21;',\n    relevance: 10\n  };\n\n  return {\n    name: 'STEP Part 21',\n    aliases: [\n      'p21',\n      'step',\n      'stp'\n    ],\n    case_insensitive: true, // STEP 21 is case insensitive in theory, in practice all non-comments are capitalized.\n    keywords: STEP21_KEYWOR<PERSON>,\n    contains: [\n      STEP21_START,\n      STEP21_CLOSE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.COMMENT('/\\\\*\\\\*!', '\\\\*/'),\n      hljs.C_NUMBER_MODE,\n      hljs.inherit(hljs.APOS_STRING_MODE, { illegal: null }),\n      hljs.inherit(hljs.QUOTE_STRING_MODE, { illegal: null }),\n      {\n        className: 'string',\n        begin: \"'\",\n        end: \"'\"\n      },\n      {\n        className: 'symbol',\n        variants: [\n          {\n            begin: '#',\n            end: '\\\\d+',\n            illegal: '\\\\W'\n          }\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = step21;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,MAAMC,eAAe,GAAG,mBAAmB;EAC3C,MAAMC,eAAe,GAAG;IACtBC,QAAQ,EAAEF,eAAe;IACzBG,OAAO,EAAE,CACP,QAAQ,EACR,QAAQ,EACR,MAAM;EAEV,CAAC;EACD,MAAMC,YAAY,GAAG;IACnBC,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE,eAAe;IACtBC,SAAS,EAAE;EACb,CAAC;EACD,MAAMC,YAAY,GAAG;IACnBH,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE,mBAAmB;IAC1BC,SAAS,EAAE;EACb,CAAC;EAED,OAAO;IACLE,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE,CACP,KAAK,EACL,MAAM,EACN,KAAK,CACN;IACDC,gBAAgB,EAAE,IAAI;IAAE;IACxBC,QAAQ,EAAEX,eAAe;IACzBY,QAAQ,EAAE,CACRT,YAAY,EACZI,YAAY,EACZT,IAAI,CAACe,mBAAmB,EACxBf,IAAI,CAACgB,oBAAoB,EACzBhB,IAAI,CAACiB,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,EAChCjB,IAAI,CAACkB,aAAa,EAClBlB,IAAI,CAACmB,OAAO,CAACnB,IAAI,CAACoB,gBAAgB,EAAE;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC,EACtDrB,IAAI,CAACmB,OAAO,CAACnB,IAAI,CAACsB,iBAAiB,EAAE;MAAED,OAAO,EAAE;IAAK,CAAC,CAAC,EACvD;MACEf,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,GAAG;MACVgB,GAAG,EAAE;IACP,CAAC,EACD;MACEjB,SAAS,EAAE,QAAQ;MACnBkB,QAAQ,EAAE,CACR;QACEjB,KAAK,EAAE,GAAG;QACVgB,GAAG,EAAE,MAAM;QACXF,OAAO,EAAE;MACX,CAAC;IAEL,CAAC;EAEL,CAAC;AACH;AAEAI,MAAM,CAACC,OAAO,GAAG3B,MAAM"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}