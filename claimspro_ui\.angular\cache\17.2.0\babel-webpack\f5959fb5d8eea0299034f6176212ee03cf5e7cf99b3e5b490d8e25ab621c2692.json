{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/slider\";\nexport class BasicSliderComponent {\n  constructor() {}\n  ngOnInit() {}\n  static #_ = this.ɵfac = function BasicSliderComponent_Factory(t) {\n    return new (t || BasicSliderComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: BasicSliderComponent,\n    selectors: [[\"app-basic-slider\"]],\n    decls: 1,\n    vars: 0,\n    template: function BasicSliderComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"mat-slider\");\n      }\n    },\n    dependencies: [i1.MatSlider],\n    styles: [\"\\n\\nmat-slider[_ngcontent-%COMP%] {\\n  width: 300px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hc3NldHMvZXhhbXBsZXMvbWF0ZXJpYWwvYmFzaWMtc2xpZGVyL2Jhc2ljLXNsaWRlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSw2QkFBQTtBQUNBO0VBQ0ksWUFBQTtBQUNKIiwic291cmNlc0NvbnRlbnQiOlsiLyoqIE5vIENTUyBmb3IgdGhpcyBleGFtcGxlICovXG5tYXQtc2xpZGVyIHtcbiAgICB3aWR0aDogMzAwcHg7XG4gIH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["BasicSliderComponent", "constructor", "ngOnInit", "_", "_2", "selectors", "decls", "vars", "template", "BasicSliderComponent_Template", "rf", "ctx", "i0", "ɵɵelement"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\assets\\examples\\material\\basic-slider\\basic-slider.component.ts", "C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\assets\\examples\\material\\basic-slider\\basic-slider.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-basic-slider',\n  templateUrl: './basic-slider.component.html',\n  styleUrls: ['./basic-slider.component.scss']\n})\nexport class BasicSliderComponent implements OnInit {\n\n  constructor() { }\n\n  ngOnInit() {\n  }\n\n}\n", "<mat-slider></mat-slider>\n"], "mappings": ";;AAOA,OAAM,MAAOA,oBAAoB;EAE/BC,YAAA,GAAgB;EAEhBC,QAAQA,CAAA,GACR;EAAC,QAAAC,CAAA,G;qBALUH,oBAAoB;EAAA;EAAA,QAAAI,EAAA,G;UAApBJ,oBAAoB;IAAAK,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCPjCE,EAAA,CAAAC,SAAA,iBAAyB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}