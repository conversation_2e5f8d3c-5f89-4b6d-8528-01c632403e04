{"ast": null, "code": "/** Used to match `RegExp` flags from their coerced string values. */\nvar reFlags = /\\w*$/;\n\n/**\n * Creates a clone of `regexp`.\n *\n * @private\n * @param {Object} regexp The regexp to clone.\n * @returns {Object} Returns the cloned regexp.\n */\nfunction cloneRegExp(regexp) {\n  var result = new regexp.constructor(regexp.source, reFlags.exec(regexp));\n  result.lastIndex = regexp.lastIndex;\n  return result;\n}\nexport default cloneRegExp;", "map": {"version": 3, "names": ["reFlags", "cloneRegExp", "regexp", "result", "constructor", "source", "exec", "lastIndex"], "sources": ["C:/OneDrive/Srikant/SWIP/ClaimsPro/claimspro200-src/claimspro_ui/node_modules/lodash-es/_cloneRegExp.js"], "sourcesContent": ["/** Used to match `RegExp` flags from their coerced string values. */\nvar reFlags = /\\w*$/;\n\n/**\n * Creates a clone of `regexp`.\n *\n * @private\n * @param {Object} regexp The regexp to clone.\n * @returns {Object} Returns the cloned regexp.\n */\nfunction cloneRegExp(regexp) {\n  var result = new regexp.constructor(regexp.source, reFlags.exec(regexp));\n  result.lastIndex = regexp.lastIndex;\n  return result;\n}\n\nexport default cloneRegExp;\n"], "mappings": "AAAA;AACA,IAAIA,OAAO,GAAG,MAAM;;AAEpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,MAAM,EAAE;EAC3B,IAAIC,MAAM,GAAG,IAAID,MAAM,CAACE,WAAW,CAACF,MAAM,CAACG,MAAM,EAAEL,OAAO,CAACM,IAAI,CAACJ,MAAM,CAAC,CAAC;EACxEC,MAAM,CAACI,SAAS,GAAGL,MAAM,CAACK,SAAS;EACnC,OAAOJ,MAAM;AACf;AAEA,eAAeF,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}