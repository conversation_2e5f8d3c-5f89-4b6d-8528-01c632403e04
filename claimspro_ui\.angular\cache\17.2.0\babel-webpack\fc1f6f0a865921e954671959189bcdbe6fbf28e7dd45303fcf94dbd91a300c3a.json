{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"./shared/services/route-parts.service\";\nimport * as i4 from \"./shared/services/ui-lib-icon.service\";\nimport * as i5 from \"./shared/services/layout.service\";\nexport class AppComponent {\n  constructor(title, router, activeRoute, routePartsService, iconService, layoutService) {\n    this.title = title;\n    this.router = router;\n    this.activeRoute = activeRoute;\n    this.routePartsService = routePartsService;\n    this.iconService = iconService;\n    this.layoutService = layoutService;\n    this.appTitle = 'AsClaimPro';\n    this.pageTitle = '';\n    iconService.init();\n  }\n  ngOnInit() {\n    this.changePageTitle();\n    // this.themeService.applyMatTheme(this.layoutService.layoutConf.matTheme);\n  }\n  ngAfterViewInit() {}\n  changePageTitle() {\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(routeChange => {\n      const routeParts = this.routePartsService.generateRouteParts(this.activeRoute.snapshot);\n      if (!routeParts.length) {\n        return this.title.setTitle(this.appTitle);\n      }\n      // Extract title from parts;\n      this.pageTitle = routeParts.reverse().map(part => part.title).reduce((partA, partI) => {\n        return `${partA} > ${partI}`;\n      });\n      this.pageTitle += ` | ${this.appTitle}`;\n      this.title.setTitle(this.pageTitle);\n    });\n  }\n  static #_ = this.ɵfac = function AppComponent_Factory(t) {\n    return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.Title), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.RoutePartsService), i0.ɵɵdirectiveInject(i4.UILibIconService), i0.ɵɵdirectiveInject(i5.LayoutService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppComponent,\n    selectors: [[\"app-root\"]],\n    decls: 1,\n    vars: 0,\n    template: function AppComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"router-outlet\");\n      }\n    },\n    dependencies: [i2.RouterOutlet],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["NavigationEnd", "filter", "AppComponent", "constructor", "title", "router", "activeRoute", "routePartsService", "iconService", "layoutService", "appTitle", "pageTitle", "init", "ngOnInit", "changePageTitle", "ngAfterViewInit", "events", "pipe", "event", "subscribe", "routeChange", "routeParts", "generateRouteParts", "snapshot", "length", "setTitle", "reverse", "map", "part", "reduce", "partA", "partI", "_", "i0", "ɵɵdirectiveInject", "i1", "Title", "i2", "Router", "ActivatedRoute", "i3", "RoutePartsService", "i4", "UILibIconService", "i5", "LayoutService", "_2", "selectors", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelement"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\app.component.ts", "C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit, AfterViewInit, Renderer2 } from '@angular/core';\nimport { Title } from '@angular/platform-browser';\nimport { Router, NavigationEnd, ActivatedRoute, ActivatedRouteSnapshot } from '@angular/router';\n\nimport { RoutePartsService } from './shared/services/route-parts.service';\n\nimport { filter } from 'rxjs/operators';\nimport { UILibIconService } from './shared/services/ui-lib-icon.service';\nimport { ThemeService } from './shared/services/theme.service';\nimport { LayoutService } from './shared/services/layout.service';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css']\n})\nexport class AppComponent implements OnInit, AfterViewInit {\n  appTitle = 'AsClaimPro';\n  pageTitle = '';\n\n  constructor(\n    public title: Title,\n    private router: Router,\n    private activeRoute: ActivatedRoute,\n    private routePartsService: RoutePartsService,\n    private iconService: UILibIconService,\n    private layoutService: LayoutService\n  ) {\n    iconService.init()\n  }\n\n  ngOnInit() {\n    this.changePageTitle();\n    // this.themeService.applyMatTheme(this.layoutService.layoutConf.matTheme);\n  }\n\n  ngAfterViewInit() {\n  }\n\n  changePageTitle() {\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe((routeChange) => {\n      const routeParts = this.routePartsService.generateRouteParts(this.activeRoute.snapshot);\n      if (!routeParts.length) {\n        return this.title.setTitle(this.appTitle);\n      }\n      // Extract title from parts;\n      this.pageTitle = routeParts\n                      .reverse()\n                      .map((part) => part.title )\n                      .reduce((partA, partI) => {return `${partA} > ${partI}`});\n      this.pageTitle += ` | ${this.appTitle}`;\n      this.title.setTitle(this.pageTitle);\n    });\n  }\n}\n", "<router-outlet></router-outlet>\n"], "mappings": "AAEA,SAAiBA,aAAa,QAAgD,iBAAiB;AAI/F,SAASC,MAAM,QAAQ,gBAAgB;;;;;;;AAUvC,OAAM,MAAOC,YAAY;EAIvBC,YACSC,KAAY,EACXC,MAAc,EACdC,WAA2B,EAC3BC,iBAAoC,EACpCC,WAA6B,EAC7BC,aAA4B;IAL7B,KAAAL,KAAK,GAALA,KAAK;IACJ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IATvB,KAAAC,QAAQ,GAAG,YAAY;IACvB,KAAAC,SAAS,GAAG,EAAE;IAUZH,WAAW,CAACI,IAAI,EAAE;EACpB;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;IACtB;EACF;EAEAC,eAAeA,CAAA,GACf;EAEAD,eAAeA,CAAA;IACb,IAAI,CAACT,MAAM,CAACW,MAAM,CAACC,IAAI,CAAChB,MAAM,CAACiB,KAAK,IAAIA,KAAK,YAAYlB,aAAa,CAAC,CAAC,CAACmB,SAAS,CAAEC,WAAW,IAAI;MACjG,MAAMC,UAAU,GAAG,IAAI,CAACd,iBAAiB,CAACe,kBAAkB,CAAC,IAAI,CAAChB,WAAW,CAACiB,QAAQ,CAAC;MACvF,IAAI,CAACF,UAAU,CAACG,MAAM,EAAE;QACtB,OAAO,IAAI,CAACpB,KAAK,CAACqB,QAAQ,CAAC,IAAI,CAACf,QAAQ,CAAC;MAC3C;MACA;MACA,IAAI,CAACC,SAAS,GAAGU,UAAU,CACVK,OAAO,EAAE,CACTC,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACxB,KAAK,CAAE,CAC1ByB,MAAM,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAI;QAAE,OAAO,GAAGD,KAAK,MAAMC,KAAK,EAAE;MAAA,CAAC,CAAC;MACzE,IAAI,CAACpB,SAAS,IAAI,MAAM,IAAI,CAACD,QAAQ,EAAE;MACvC,IAAI,CAACN,KAAK,CAACqB,QAAQ,CAAC,IAAI,CAACd,SAAS,CAAC;IACrC,CAAC,CAAC;EACJ;EAAC,QAAAqB,CAAA,G;qBArCU9B,YAAY,EAAA+B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,KAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAAN,EAAA,CAAAC,iBAAA,CAAAM,EAAA,CAAAC,iBAAA,GAAAR,EAAA,CAAAC,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAAV,EAAA,CAAAC,iBAAA,CAAAU,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAZ5C,YAAY;IAAA6C,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QChBzBnB,EAAA,CAAAqB,SAAA,oBAA+B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}