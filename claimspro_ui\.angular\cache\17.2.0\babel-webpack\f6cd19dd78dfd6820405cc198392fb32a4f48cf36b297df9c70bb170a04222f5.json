{"ast": null, "code": "import getTimezoneOffsetInMilliseconds from \"../_lib/getTimezoneOffsetInMilliseconds/index.js\";\nimport startOfISOWeek from \"../startOfISOWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nvar MILLISECONDS_IN_WEEK = 604800000;\n\n/**\n * @name differenceInCalendarISOWeeks\n * @category ISO Week Helpers\n * @summary Get the number of calendar ISO weeks between the given dates.\n *\n * @description\n * Get the number of calendar ISO weeks between the given dates.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of calendar ISO weeks\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many calendar ISO weeks are between 6 July 2014 and 21 July 2014?\n * const result = differenceInCalendarISOWeeks(\n *   new Date(2014, 6, 21),\n *   new Date(2014, 6, 6)\n * )\n * //=> 3\n */\nexport default function differenceInCalendarISOWeeks(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var startOfISOWeekLeft = startOfISOWeek(dirtyDateLeft);\n  var startOfISOWeekRight = startOfISOWeek(dirtyDateRight);\n  var timestampLeft = startOfISOWeekLeft.getTime() - getTimezoneOffsetInMilliseconds(startOfISOWeekLeft);\n  var timestampRight = startOfISOWeekRight.getTime() - getTimezoneOffsetInMilliseconds(startOfISOWeekRight);\n\n  // Round the number of days to the nearest integer\n  // because the number of milliseconds in a week is not constant\n  // (e.g. it's different in the week of the daylight saving time clock shift)\n  return Math.round((timestampLeft - timestampRight) / MILLISECONDS_IN_WEEK);\n}", "map": {"version": 3, "names": ["getTimezoneOffsetInMilliseconds", "startOfISOWeek", "requiredArgs", "MILLISECONDS_IN_WEEK", "differenceInCalendarISOWeeks", "dirtyDateLeft", "dirtyDateRight", "arguments", "startOfISOWeekLeft", "startOfISOWeekRight", "timestampLeft", "getTime", "timestampRight", "Math", "round"], "sources": ["C:/OneDrive/Srikant/SWIP/ClaimsPro/claimspro200-src/claimspro_ui/node_modules/date-fns/esm/differenceInCalendarISOWeeks/index.js"], "sourcesContent": ["import getTimezoneOffsetInMilliseconds from \"../_lib/getTimezoneOffsetInMilliseconds/index.js\";\nimport startOfISOWeek from \"../startOfISOWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nvar MILLISECONDS_IN_WEEK = 604800000;\n\n/**\n * @name differenceInCalendarISOWeeks\n * @category ISO Week Helpers\n * @summary Get the number of calendar ISO weeks between the given dates.\n *\n * @description\n * Get the number of calendar ISO weeks between the given dates.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of calendar ISO weeks\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many calendar ISO weeks are between 6 July 2014 and 21 July 2014?\n * const result = differenceInCalendarISOWeeks(\n *   new Date(2014, 6, 21),\n *   new Date(2014, 6, 6)\n * )\n * //=> 3\n */\nexport default function differenceInCalendarISOWeeks(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var startOfISOWeekLeft = startOfISOWeek(dirtyDateLeft);\n  var startOfISOWeekRight = startOfISOWeek(dirtyDateRight);\n  var timestampLeft = startOfISOWeekLeft.getTime() - getTimezoneOffsetInMilliseconds(startOfISOWeekLeft);\n  var timestampRight = startOfISOWeekRight.getTime() - getTimezoneOffsetInMilliseconds(startOfISOWeekRight);\n\n  // Round the number of days to the nearest integer\n  // because the number of milliseconds in a week is not constant\n  // (e.g. it's different in the week of the daylight saving time clock shift)\n  return Math.round((timestampLeft - timestampRight) / MILLISECONDS_IN_WEEK);\n}"], "mappings": "AAAA,OAAOA,+BAA+B,MAAM,kDAAkD;AAC9F,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,YAAY,MAAM,+BAA+B;AACxD,IAAIC,oBAAoB,GAAG,SAAS;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,4BAA4BA,CAACC,aAAa,EAAEC,cAAc,EAAE;EAClFJ,YAAY,CAAC,CAAC,EAAEK,SAAS,CAAC;EAC1B,IAAIC,kBAAkB,GAAGP,cAAc,CAACI,aAAa,CAAC;EACtD,IAAII,mBAAmB,GAAGR,cAAc,CAACK,cAAc,CAAC;EACxD,IAAII,aAAa,GAAGF,kBAAkB,CAACG,OAAO,CAAC,CAAC,GAAGX,+BAA+B,CAACQ,kBAAkB,CAAC;EACtG,IAAII,cAAc,GAAGH,mBAAmB,CAACE,OAAO,CAAC,CAAC,GAAGX,+BAA+B,CAACS,mBAAmB,CAAC;;EAEzG;EACA;EACA;EACA,OAAOI,IAAI,CAACC,KAAK,CAAC,CAACJ,aAAa,GAAGE,cAAc,IAAIT,oBAAoB,CAAC;AAC5E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}