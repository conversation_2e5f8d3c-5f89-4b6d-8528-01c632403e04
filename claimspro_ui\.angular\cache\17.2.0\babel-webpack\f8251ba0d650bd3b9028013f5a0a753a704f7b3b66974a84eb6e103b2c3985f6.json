{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport differenceInCalendarISOWeekYears from \"../differenceInCalendarISOWeekYears/index.js\";\nimport compareAsc from \"../compareAsc/index.js\";\nimport subISOWeekYears from \"../subISOWeekYears/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name differenceInISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the number of full ISO week-numbering years between the given dates.\n *\n * @description\n * Get the number of full ISO week-numbering years between the given dates.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of full ISO week-numbering years\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many full ISO week-numbering years are between 1 January 2010 and 1 January 2012?\n * const result = differenceInISOWeekYears(\n *   new Date(2012, 0, 1),\n *   new Date(2010, 0, 1)\n * )\n * //=> 1\n */\nexport default function differenceInISOWeekYears(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyDateLeft);\n  var dateRight = toDate(dirtyDateRight);\n  var sign = compareAsc(dateLeft, dateRight);\n  var difference = Math.abs(differenceInCalendarISOWeekYears(dateLeft, dateRight));\n  dateLeft = subISOWeekYears(dateLeft, sign * difference);\n\n  // Math.abs(diff in full ISO years - diff in calendar ISO years) === 1\n  // if last calendar ISO year is not full\n  // If so, result must be decreased by 1 in absolute value\n  var isLastISOWeekYearNotFull = Number(compareAsc(dateLeft, dateRight) === -sign);\n  var result = sign * (difference - isLastISOWeekYearNotFull);\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}", "map": {"version": 3, "names": ["toDate", "differenceInCalendarISOWeekYears", "compareAsc", "subISOWeekYears", "requiredArgs", "differenceInISOWeekYears", "dirtyDateLeft", "dirtyDateRight", "arguments", "dateLeft", "dateRight", "sign", "difference", "Math", "abs", "isLastISOWeekYearNotFull", "Number", "result"], "sources": ["C:/OneDrive/Srikant/SWIP/ClaimsPro/claimspro200-src/claimspro_ui/node_modules/date-fns/esm/differenceInISOWeekYears/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport differenceInCalendarISOWeekYears from \"../differenceInCalendarISOWeekYears/index.js\";\nimport compareAsc from \"../compareAsc/index.js\";\nimport subISOWeekYears from \"../subISOWeekYears/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name differenceInISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the number of full ISO week-numbering years between the given dates.\n *\n * @description\n * Get the number of full ISO week-numbering years between the given dates.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of full ISO week-numbering years\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many full ISO week-numbering years are between 1 January 2010 and 1 January 2012?\n * const result = differenceInISOWeekYears(\n *   new Date(2012, 0, 1),\n *   new Date(2010, 0, 1)\n * )\n * //=> 1\n */\nexport default function differenceInISOWeekYears(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyDateLeft);\n  var dateRight = toDate(dirtyDateRight);\n  var sign = compareAsc(dateLeft, dateRight);\n  var difference = Math.abs(differenceInCalendarISOWeekYears(dateLeft, dateRight));\n  dateLeft = subISOWeekYears(dateLeft, sign * difference);\n\n  // Math.abs(diff in full ISO years - diff in calendar ISO years) === 1\n  // if last calendar ISO year is not full\n  // If so, result must be decreased by 1 in absolute value\n  var isLastISOWeekYearNotFull = Number(compareAsc(dateLeft, dateRight) === -sign);\n  var result = sign * (difference - isLastISOWeekYearNotFull);\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,gCAAgC,MAAM,8CAA8C;AAC3F,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,wBAAwBA,CAACC,aAAa,EAAEC,cAAc,EAAE;EAC9EH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,QAAQ,GAAGT,MAAM,CAACM,aAAa,CAAC;EACpC,IAAII,SAAS,GAAGV,MAAM,CAACO,cAAc,CAAC;EACtC,IAAII,IAAI,GAAGT,UAAU,CAACO,QAAQ,EAAEC,SAAS,CAAC;EAC1C,IAAIE,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACb,gCAAgC,CAACQ,QAAQ,EAAEC,SAAS,CAAC,CAAC;EAChFD,QAAQ,GAAGN,eAAe,CAACM,QAAQ,EAAEE,IAAI,GAAGC,UAAU,CAAC;;EAEvD;EACA;EACA;EACA,IAAIG,wBAAwB,GAAGC,MAAM,CAACd,UAAU,CAACO,QAAQ,EAAEC,SAAS,CAAC,KAAK,CAACC,IAAI,CAAC;EAChF,IAAIM,MAAM,GAAGN,IAAI,IAAIC,UAAU,GAAGG,wBAAwB,CAAC;EAC3D;EACA,OAAOE,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM;AAClC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}