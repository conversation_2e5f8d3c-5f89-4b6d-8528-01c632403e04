{"ast": null, "code": "import { MatSidenav } from '@angular/material/sidenav';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/cdk/layout\";\nimport * as i2 from \"./chat.service\";\nimport * as i3 from \"@angular/material/sidenav\";\nimport * as i4 from \"@angular/material/card\";\nimport * as i5 from \"./chat-left-sidenav/chat-left-sidenav.component\";\nimport * as i6 from \"./chat-contents/chat-contents.component\";\nexport class AppChatsComponent {\n  constructor(breakpointObserver, chatService) {\n    this.breakpointObserver = breakpointObserver;\n    this.chatService = chatService;\n    this.isSidenavOpen = true;\n    this.activeChatUser = {\n      name: '<PERSON><PERSON>rg Spartak',\n      photo: 'assets/images/face-2.jpg',\n      isOnline: true,\n      lastMsg: 'Hello!'\n    };\n    // console.log(chatService.chats)\n    this.user = chatService.user;\n  }\n  ngOnInit() {\n    this.chatSideBarInit();\n  }\n  ngOnDestroy() {\n    if (this.screenSizeWatcher) {\n      this.screenSizeWatcher.unsubscribe();\n    }\n  }\n  changeActiveUser(user) {\n    this.activeChatUser = user;\n  }\n  updateSidenav() {\n    var self = this;\n    setTimeout(() => {\n      self.isSidenavOpen = !self.isMobile;\n      self.sideNav.mode = self.isMobile ? 'over' : 'side';\n    });\n  }\n  chatSideBarInit() {\n    this.isMobile = this.breakpointObserver.isMatched('(max-width: 959px)');\n    this.updateSidenav();\n    this.screenSizeWatcher = this.breakpointObserver.observe('(max-width: 959px)').subscribe(result => {\n      this.isMobile = result.matches;\n      this.updateSidenav();\n    });\n  }\n  static #_ = this.ɵfac = function AppChatsComponent_Factory(t) {\n    return new (t || AppChatsComponent)(i0.ɵɵdirectiveInject(i1.BreakpointObserver), i0.ɵɵdirectiveInject(i2.ChatService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppChatsComponent,\n    selectors: [[\"app-chats\"]],\n    viewQuery: function AppChatsComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatSidenav, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sideNav = _t.first);\n      }\n    },\n    decls: 6,\n    vars: 2,\n    consts: [[1, \"p-0\"], [1, \"chat-container\"], [\"mode\", \"side\", 1, \"chat-sidenav\", 3, \"opened\"], [1, \"chats-wrap\"], [3, \"matSidenav\"]],\n    template: function AppChatsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"mat-card\", 0)(1, \"mat-sidenav-container\", 1)(2, \"mat-sidenav\", 2);\n        i0.ɵɵelement(3, \"app-chat-left-sidenav\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 3);\n        i0.ɵɵelement(5, \"app-chat-contents\", 4);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"opened\", ctx.isSidenavOpen);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"matSidenav\", ctx.sideNav);\n      }\n    },\n    dependencies: [i3.MatSidenav, i3.MatSidenavContainer, i4.MatCard, i5.ChatLeftSidenavComponent, i6.ChatContentsComponent],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"],\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "AppChatsComponent", "constructor", "breakpointObserver", "chatService", "isSidenavOpen", "activeChatUser", "name", "photo", "isOnline", "lastMsg", "user", "ngOnInit", "chatSideBarInit", "ngOnDestroy", "screenSizeWatcher", "unsubscribe", "changeActiveUser", "updateSidenav", "self", "setTimeout", "isMobile", "sideNav", "mode", "isMatched", "observe", "subscribe", "result", "matches", "_", "i0", "ɵɵdirectiveInject", "i1", "BreakpointObserver", "i2", "ChatService", "_2", "selectors", "viewQuery", "AppChatsComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\views\\app-chats\\app-chats.component.ts", "C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\views\\app-chats\\app-chats.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, OnDestroy, ChangeDetectionStrategy } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { BreakpointObserver } from '@angular/cdk/layout';\n\nimport { MatSidenav } from '@angular/material/sidenav';\nimport { ChatService } from './chat.service';\n\n@Component({\n  selector: 'app-chats',\n  templateUrl: './app-chats.component.html',\n  styleUrls: ['./app-chats.component.css'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})\nexport class AppChatsComponent implements OnInit, OnDestroy {\n  isMobile;\n  screenSizeWatcher: Subscription;\n  isSidenavOpen: Boolean = true;\n  @ViewChild(MatSidenav) public sideNav: MatSidenav;\n\n  activeChatUser = {\n    name: 'Gevorg Spartak',\n    photo: 'assets/images/face-2.jpg',\n    isOnline: true,\n    lastMsg: 'Hello!'\n  };\n  user;\n\n\n  constructor(\n    private breakpointObserver: BreakpointObserver,\n    public chatService: ChatService\n  ) {\n    // console.log(chatService.chats)\n    this.user = chatService.user\n  }\n\n  ngOnInit() {\n    this.chatSideBarInit();\n  }\n  ngOnDestroy() {\n    if (this.screenSizeWatcher) {\n      this.screenSizeWatcher.unsubscribe();\n    }\n  }\n  changeActiveUser(user) {\n    this.activeChatUser = user;\n  }\n  updateSidenav() {\n    var self = this;\n    setTimeout(() => {\n      self.isSidenavOpen = !self.isMobile;\n      self.sideNav.mode = self.isMobile ? 'over' : 'side';\n    });\n  }\n  chatSideBarInit() {\n    this.isMobile = this.breakpointObserver.isMatched('(max-width: 959px)');\n    this.updateSidenav();\n\n    this.screenSizeWatcher = this.breakpointObserver.observe('(max-width: 959px)').subscribe(result => {\n      this.isMobile = result.matches;\n      this.updateSidenav();\n    });\n  }\n}\n", "<mat-card class=\"p-0\">\n  <mat-sidenav-container class=\"chat-container\">\n    <!-- Left sidebar -->\n    <mat-sidenav class=\"chat-sidenav\" [opened]=\"isSidenavOpen\" mode=\"side\">\n      <app-chat-left-sidenav></app-chat-left-sidenav>\n    </mat-sidenav>\n\n    <!-- Right side -->\n    <div class=\"chats-wrap\">\n      <!-- Right side topbar -->\n      <app-chat-contents [matSidenav]=\"sideNav\"></app-chat-contents>\n\n    </div>\n  </mat-sidenav-container>\n</mat-card>\n"], "mappings": "AAIA,SAASA,UAAU,QAAQ,2BAA2B;;;;;;;;AAStD,OAAM,MAAOC,iBAAiB;EAe5BC,YACUC,kBAAsC,EACvCC,WAAwB;IADvB,KAAAD,kBAAkB,GAAlBA,kBAAkB;IACnB,KAAAC,WAAW,GAAXA,WAAW;IAdpB,KAAAC,aAAa,GAAY,IAAI;IAG7B,KAAAC,cAAc,GAAG;MACfC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE,0BAA0B;MACjCC,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE;KACV;IAQC;IACA,IAAI,CAACC,IAAI,GAAGP,WAAW,CAACO,IAAI;EAC9B;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;EACxB;EACAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACC,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAACC,WAAW,EAAE;IACtC;EACF;EACAC,gBAAgBA,CAACN,IAAI;IACnB,IAAI,CAACL,cAAc,GAAGK,IAAI;EAC5B;EACAO,aAAaA,CAAA;IACX,IAAIC,IAAI,GAAG,IAAI;IACfC,UAAU,CAAC,MAAK;MACdD,IAAI,CAACd,aAAa,GAAG,CAACc,IAAI,CAACE,QAAQ;MACnCF,IAAI,CAACG,OAAO,CAACC,IAAI,GAAGJ,IAAI,CAACE,QAAQ,GAAG,MAAM,GAAG,MAAM;IACrD,CAAC,CAAC;EACJ;EACAR,eAAeA,CAAA;IACb,IAAI,CAACQ,QAAQ,GAAG,IAAI,CAAClB,kBAAkB,CAACqB,SAAS,CAAC,oBAAoB,CAAC;IACvE,IAAI,CAACN,aAAa,EAAE;IAEpB,IAAI,CAACH,iBAAiB,GAAG,IAAI,CAACZ,kBAAkB,CAACsB,OAAO,CAAC,oBAAoB,CAAC,CAACC,SAAS,CAACC,MAAM,IAAG;MAChG,IAAI,CAACN,QAAQ,GAAGM,MAAM,CAACC,OAAO;MAC9B,IAAI,CAACV,aAAa,EAAE;IACtB,CAAC,CAAC;EACJ;EAAC,QAAAW,CAAA,G;qBAjDU5B,iBAAiB,EAAA6B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAjBnC,iBAAiB;IAAAoC,SAAA;IAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBAIjBxC,UAAU;;;;;;;;;;;;QCjBvB8B,EAAA,CAAAY,cAAA,kBAAsB;QAIhBZ,EAAA,CAAAa,SAAA,4BAA+C;QACjDb,EAAA,CAAAc,YAAA,EAAc;QAGdd,EAAA,CAAAY,cAAA,aAAwB;QAEtBZ,EAAA,CAAAa,SAAA,2BAA8D;QAEhEb,EAAA,CAAAc,YAAA,EAAM;;;QAT4Bd,EAAA,CAAAe,SAAA,GAAwB;QAAxBf,EAAA,CAAAgB,UAAA,WAAAL,GAAA,CAAApC,aAAA,CAAwB;QAOrCyB,EAAA,CAAAe,SAAA,GAAsB;QAAtBf,EAAA,CAAAgB,UAAA,eAAAL,GAAA,CAAAnB,OAAA,CAAsB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}