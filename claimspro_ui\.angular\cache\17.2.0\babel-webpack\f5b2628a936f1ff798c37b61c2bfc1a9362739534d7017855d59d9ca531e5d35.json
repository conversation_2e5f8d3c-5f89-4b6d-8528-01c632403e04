{"ast": null, "code": "const normalWeightRegexp = /font-weight:\\s*normal/;\nconst blockTagNames = ['P', 'OL', 'UL'];\nconst isBlockElement = element => {\n  return element && blockTagNames.includes(element.tagName);\n};\nconst normalizeEmptyLines = doc => {\n  Array.from(doc.querySelectorAll('br')).filter(br => isBlockElement(br.previousElementSibling) && isBlockElement(br.nextElementSibling)).forEach(br => {\n    br.parentNode?.removeChild(br);\n  });\n};\nconst normalizeFontWeight = doc => {\n  Array.from(doc.querySelectorAll('b[style*=\"font-weight\"]')).filter(node => node.getAttribute('style')?.match(normalWeightRegexp)).forEach(node => {\n    const fragment = doc.createDocumentFragment();\n    fragment.append(...node.childNodes);\n    node.parentNode?.replaceChild(fragment, node);\n  });\n};\nexport default function normalize(doc) {\n  if (doc.querySelector('[id^=\"docs-internal-guid-\"]')) {\n    normalizeFontWeight(doc);\n    normalizeEmptyLines(doc);\n  }\n}", "map": {"version": 3, "names": ["normalWeightRegexp", "blockTagNames", "isBlockElement", "element", "includes", "tagName", "normalizeEmptyLines", "doc", "Array", "from", "querySelectorAll", "filter", "br", "previousElementSibling", "nextElement<PERSON><PERSON>ling", "for<PERSON>ach", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "normalizeFontWeight", "node", "getAttribute", "match", "fragment", "createDocumentFragment", "append", "childNodes", "<PERSON><PERSON><PERSON><PERSON>", "normalize", "querySelector"], "sources": ["C:/OneDrive/Srikant/SWIP/ClaimsPro/claimspro200-src/claimspro_ui/node_modules/quill/modules/normalizeExternalHTML/normalizers/googleDocs.js"], "sourcesContent": ["const normalWeightRegexp = /font-weight:\\s*normal/;\nconst blockTagNames = ['P', 'OL', 'UL'];\nconst isBlockElement = element => {\n  return element && blockTagNames.includes(element.tagName);\n};\nconst normalizeEmptyLines = doc => {\n  Array.from(doc.querySelectorAll('br')).filter(br => isBlockElement(br.previousElementSibling) && isBlockElement(br.nextElementSibling)).forEach(br => {\n    br.parentNode?.removeChild(br);\n  });\n};\nconst normalizeFontWeight = doc => {\n  Array.from(doc.querySelectorAll('b[style*=\"font-weight\"]')).filter(node => node.getAttribute('style')?.match(normalWeightRegexp)).forEach(node => {\n    const fragment = doc.createDocumentFragment();\n    fragment.append(...node.childNodes);\n    node.parentNode?.replaceChild(fragment, node);\n  });\n};\nexport default function normalize(doc) {\n  if (doc.querySelector('[id^=\"docs-internal-guid-\"]')) {\n    normalizeFontWeight(doc);\n    normalizeEmptyLines(doc);\n  }\n}\n"], "mappings": "AAAA,MAAMA,kBAAkB,GAAG,uBAAuB;AAClD,MAAMC,aAAa,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;AACvC,MAAMC,cAAc,GAAGC,OAAO,IAAI;EAChC,OAAOA,OAAO,IAAIF,aAAa,CAACG,QAAQ,CAACD,OAAO,CAACE,OAAO,CAAC;AAC3D,CAAC;AACD,MAAMC,mBAAmB,GAAGC,GAAG,IAAI;EACjCC,KAAK,CAACC,IAAI,CAACF,GAAG,CAACG,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAACC,MAAM,CAACC,EAAE,IAAIV,cAAc,CAACU,EAAE,CAACC,sBAAsB,CAAC,IAAIX,cAAc,CAACU,EAAE,CAACE,kBAAkB,CAAC,CAAC,CAACC,OAAO,CAACH,EAAE,IAAI;IACpJA,EAAE,CAACI,UAAU,EAAEC,WAAW,CAACL,EAAE,CAAC;EAChC,CAAC,CAAC;AACJ,CAAC;AACD,MAAMM,mBAAmB,GAAGX,GAAG,IAAI;EACjCC,KAAK,CAACC,IAAI,CAACF,GAAG,CAACG,gBAAgB,CAAC,yBAAyB,CAAC,CAAC,CAACC,MAAM,CAACQ,IAAI,IAAIA,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,EAAEC,KAAK,CAACrB,kBAAkB,CAAC,CAAC,CAACe,OAAO,CAACI,IAAI,IAAI;IAChJ,MAAMG,QAAQ,GAAGf,GAAG,CAACgB,sBAAsB,CAAC,CAAC;IAC7CD,QAAQ,CAACE,MAAM,CAAC,GAAGL,IAAI,CAACM,UAAU,CAAC;IACnCN,IAAI,CAACH,UAAU,EAAEU,YAAY,CAACJ,QAAQ,EAAEH,IAAI,CAAC;EAC/C,CAAC,CAAC;AACJ,CAAC;AACD,eAAe,SAASQ,SAASA,CAACpB,GAAG,EAAE;EACrC,IAAIA,GAAG,CAACqB,aAAa,CAAC,6BAA6B,CAAC,EAAE;IACpDV,mBAAmB,CAACX,GAAG,CAAC;IACxBD,mBAAmB,CAACC,GAAG,CAAC;EAC1B;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}