{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class RoutePartsService {\n  constructor(router) {\n    this.router = router;\n  }\n  ngOnInit() {}\n  generateRouteParts(snapshot) {\n    var routeParts = [];\n    if (snapshot) {\n      if (snapshot.firstChild) {\n        routeParts = routeParts.concat(this.generateRouteParts(snapshot.firstChild));\n      }\n      if (snapshot.data['title'] && snapshot.url.length) {\n        // console.log(snapshot.data['title'], snapshot.url)\n        routeParts.push({\n          title: snapshot.data['title'],\n          breadcrumb: snapshot.data['breadcrumb'],\n          url: snapshot.url[0].path,\n          urlSegments: snapshot.url,\n          params: snapshot.params\n        });\n      }\n    }\n    return routeParts;\n  }\n  static #_ = this.ɵfac = function RoutePartsService_Factory(t) {\n    return new (t || RoutePartsService)(i0.ɵɵinject(i1.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: RoutePartsService,\n    factory: RoutePartsService.ɵfac\n  });\n}", "map": {"version": 3, "names": ["RoutePartsService", "constructor", "router", "ngOnInit", "generateRouteParts", "snapshot", "routeParts", "<PERSON><PERSON><PERSON><PERSON>", "concat", "data", "url", "length", "push", "title", "breadcrumb", "path", "urlSegments", "params", "_", "i0", "ɵɵinject", "i1", "Router", "_2", "factory", "ɵfac"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\shared\\services\\route-parts.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Router, ActivatedRoute, NavigationEnd, ActivatedRouteSnapshot, Params, PRIMARY_OUTLET } from \"@angular/router\";\n\ninterface IRoutePart {\n  title: string,\n  breadcrumb: string,\n  params?: Params,\n  url: string,\n  urlSegments: any[]\n}\n\n@Injectable()\nexport class RoutePartsService {\n  public routeParts: IRoutePart[];\n  constructor(private router: Router) {}\n\n  ngOnInit() {\n  }\n  generateRouteParts(snapshot: ActivatedRouteSnapshot): IRoutePart[] {\n    var routeParts = <IRoutePart[]>[];\n    if (snapshot) {\n      if (snapshot.firstChild) {\n        routeParts = routeParts.concat(this.generateRouteParts(snapshot.firstChild));\n      }\n      if (snapshot.data['title'] && snapshot.url.length) {\n        // console.log(snapshot.data['title'], snapshot.url)\n        routeParts.push({\n          title: snapshot.data['title'], \n          breadcrumb: snapshot.data['breadcrumb'], \n          url: snapshot.url[0].path,\n          urlSegments: snapshot.url,\n          params: snapshot.params\n        });\n      }\n    }\n    return routeParts;\n  }\n}"], "mappings": ";;AAYA,OAAM,MAAOA,iBAAiB;EAE5BC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;EAAW;EAErCC,QAAQA,CAAA,GACR;EACAC,kBAAkBA,CAACC,QAAgC;IACjD,IAAIC,UAAU,GAAiB,EAAE;IACjC,IAAID,QAAQ,EAAE;MACZ,IAAIA,QAAQ,CAACE,UAAU,EAAE;QACvBD,UAAU,GAAGA,UAAU,CAACE,MAAM,CAAC,IAAI,CAACJ,kBAAkB,CAACC,QAAQ,CAACE,UAAU,CAAC,CAAC;MAC9E;MACA,IAAIF,QAAQ,CAACI,IAAI,CAAC,OAAO,CAAC,IAAIJ,QAAQ,CAACK,GAAG,CAACC,MAAM,EAAE;QACjD;QACAL,UAAU,CAACM,IAAI,CAAC;UACdC,KAAK,EAAER,QAAQ,CAACI,IAAI,CAAC,OAAO,CAAC;UAC7BK,UAAU,EAAET,QAAQ,CAACI,IAAI,CAAC,YAAY,CAAC;UACvCC,GAAG,EAAEL,QAAQ,CAACK,GAAG,CAAC,CAAC,CAAC,CAACK,IAAI;UACzBC,WAAW,EAAEX,QAAQ,CAACK,GAAG;UACzBO,MAAM,EAAEZ,QAAQ,CAACY;SAClB,CAAC;MACJ;IACF;IACA,OAAOX,UAAU;EACnB;EAAC,QAAAY,CAAA,G;qBAxBUlB,iBAAiB,EAAAmB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAjBvB,iBAAiB;IAAAwB,OAAA,EAAjBxB,iBAAiB,CAAAyB;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}