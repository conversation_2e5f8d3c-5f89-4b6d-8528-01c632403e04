{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatListModule } from '@angular/material/list';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { SharedModule } from '../../shared/shared.module';\nimport { Icd9CodesTableComponent } from './icd9Codes-table/icd9Codes-table.component';\nimport { Icd9CodesRoutes } from './icd9Codes.routing';\nimport { Icd9CodesService } from './icd9Codes.service';\nimport { Icd9CodesTablePopupComponent } from './icd9Codes-table/icd9Codes-table-popup/icd9Codes-table-popup.component';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class Icd9CodesModule {\n  static #_ = this.ɵfac = function Icd9CodesModule_Factory(t) {\n    return new (t || Icd9CodesModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: Icd9CodesModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [Icd9CodesService],\n    imports: [CommonModule, ReactiveFormsModule, MatInputModule, MatIconModule, MatCardModule, MatMenuModule, MatButtonModule, MatChipsModule, MatListModule, MatPaginatorModule, MatTooltipModule, MatTableModule, MatDialogModule, MatSnackBarModule, MatSlideToggleModule, TranslateModule, SharedModule, RouterModule.forChild(Icd9CodesRoutes)]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(Icd9CodesModule, {\n    declarations: [Icd9CodesTableComponent, Icd9CodesTablePopupComponent],\n    imports: [CommonModule, ReactiveFormsModule, MatInputModule, MatIconModule, MatCardModule, MatMenuModule, MatButtonModule, MatChipsModule, MatListModule, MatPaginatorModule, MatTooltipModule, MatTableModule, MatDialogModule, MatSnackBarModule, MatSlideToggleModule, TranslateModule, SharedModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "ReactiveFormsModule", "MatButtonModule", "MatCardModule", "MatChipsModule", "MatDialogModule", "MatIconModule", "MatInputModule", "MatListModule", "MatMenuModule", "MatSlideToggleModule", "MatSnackBarModule", "MatTooltipModule", "SharedModule", "Icd9CodesTableComponent", "Icd9CodesRoutes", "Icd9CodesService", "Icd9CodesTablePopupComponent", "TranslateModule", "MatTableModule", "MatPaginatorModule", "Icd9CodesModule", "_", "_2", "_3", "imports", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "i1"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\views\\icd9Codes\\icd9Codes.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterModule } from '@angular/router';\r\nimport { ReactiveFormsModule } from '@angular/forms';\r\nimport { MatButtonModule as MatButtonModule } from '@angular/material/button';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatChipsModule as MatChipsModule } from '@angular/material/chips';\r\nimport { MatDialogModule as MatDialogModule } from '@angular/material/dialog';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatInputModule as MatInputModule } from '@angular/material/input';\r\nimport { MatListModule } from '@angular/material/list';\r\nimport { MatMenuModule as MatMenuModule } from '@angular/material/menu';\r\nimport { MatSlideToggleModule as MatSlideToggleModule } from '@angular/material/slide-toggle';\r\nimport { MatSnackBarModule as MatSnackBarModule } from '@angular/material/snack-bar';\r\nimport { MatTooltipModule as MatTooltipModule } from '@angular/material/tooltip';\r\nimport { SharedModule } from '../../shared/shared.module';\r\nimport { Icd9CodesTableComponent } from './icd9Codes-table/icd9Codes-table.component';\r\n\r\nimport { Icd9CodesRoutes } from './icd9Codes.routing';\r\nimport { Icd9CodesService } from './icd9Codes.service';\r\nimport { Icd9CodesTablePopupComponent } from './icd9Codes-table/icd9Codes-table-popup/icd9Codes-table-popup.component'\r\nimport { TranslateModule } from '@ngx-translate/core';\r\nimport { MatTableModule as MatTableModule } from '@angular/material/table';\r\nimport { MatPaginatorModule as MatPaginatorModule } from '@angular/material/paginator';\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    MatInputModule,\r\n    MatIconModule,\r\n    MatCardModule,\r\n    MatMenuModule,\r\n    MatButtonModule,\r\n    MatChipsModule,\r\n    MatListModule,\r\n    MatPaginatorModule,\r\n    MatTooltipModule,\r\n    MatTableModule,\r\n    MatDialogModule,\r\n    MatSnackBarModule,\r\n    MatSlideToggleModule,\r\n    TranslateModule,\r\n    SharedModule,\r\n    RouterModule.forChild(Icd9CodesRoutes)\r\n  ],\r\n  declarations: [Icd9CodesTableComponent, Icd9CodesTablePopupComponent],\r\n  providers: [Icd9CodesService]\r\n})\r\nexport class Icd9CodesModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAkC,QAAQ,0BAA0B;AAC7E,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAgC,QAAQ,yBAAyB;AAC1E,SAASC,eAAkC,QAAQ,0BAA0B;AAC7E,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAgC,QAAQ,yBAAyB;AAC1E,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAA8B,QAAQ,wBAAwB;AACvE,SAASC,oBAA4C,QAAQ,gCAAgC;AAC7F,SAASC,iBAAsC,QAAQ,6BAA6B;AACpF,SAASC,gBAAoC,QAAQ,2BAA2B;AAChF,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,uBAAuB,QAAQ,6CAA6C;AAErF,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,4BAA4B,QAAQ,yEAAyE;AACtH,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,cAAgC,QAAQ,yBAAyB;AAC1E,SAASC,kBAAwC,QAAQ,6BAA6B;;;AA0BtF,OAAM,MAAOC,eAAe;EAAA,QAAAC,CAAA,G;qBAAfD,eAAe;EAAA;EAAA,QAAAE,EAAA,G;UAAfF;EAAe;EAAA,QAAAG,EAAA,G;eAFf,CAACR,gBAAgB,CAAC;IAAAS,OAAA,GApB3B1B,YAAY,EACZE,mBAAmB,EACnBM,cAAc,EACdD,aAAa,EACbH,aAAa,EACbM,aAAa,EACbP,eAAe,EACfE,cAAc,EACdI,aAAa,EACbY,kBAAkB,EAClBR,gBAAgB,EAChBO,cAAc,EACdd,eAAe,EACfM,iBAAiB,EACjBD,oBAAoB,EACpBQ,eAAe,EACfL,YAAY,EACZb,YAAY,CAAC0B,QAAQ,CAACX,eAAe,CAAC;EAAA;;;2EAK7BM,eAAe;IAAAM,YAAA,GAHXb,uBAAuB,EAAEG,4BAA4B;IAAAQ,OAAA,GAnBlE1B,YAAY,EACZE,mBAAmB,EACnBM,cAAc,EACdD,aAAa,EACbH,aAAa,EACbM,aAAa,EACbP,eAAe,EACfE,cAAc,EACdI,aAAa,EACbY,kBAAkB,EAClBR,gBAAgB,EAChBO,cAAc,EACdd,eAAe,EACfM,iBAAiB,EACjBD,oBAAoB,EACpBQ,eAAe,EACfL,YAAY,EAAAe,EAAA,CAAA5B,YAAA;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}