{"ast": null, "code": "import addISOWeekYears from \"../addISOWeekYears/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name subISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Subtract the specified number of ISO week-numbering years from the given date.\n *\n * @description\n * Subtract the specified number of ISO week-numbering years from the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of ISO week-numbering years to be subtracted. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the ISO week-numbering years subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Subtract 5 ISO week-numbering years from 1 September 2014:\n * const result = subISOWeekYears(new Date(2014, 8, 1), 5)\n * //=> Mon Aug 31 2009 00:00:00\n */\nexport default function subISOWeekYears(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addISOWeekYears(dirtyDate, -amount);\n}", "map": {"version": 3, "names": ["addISOWeekYears", "requiredArgs", "toInteger", "subISOWeekYears", "dirtyDate", "dirtyAmount", "arguments", "amount"], "sources": ["C:/OneDrive/Srikant/SWIP/ClaimsPro/claimspro200-src/claimspro_ui/node_modules/date-fns/esm/subISOWeekYears/index.js"], "sourcesContent": ["import addISOWeekYears from \"../addISOWeekYears/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name subISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Subtract the specified number of ISO week-numbering years from the given date.\n *\n * @description\n * Subtract the specified number of ISO week-numbering years from the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of ISO week-numbering years to be subtracted. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the ISO week-numbering years subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Subtract 5 ISO week-numbering years from 1 September 2014:\n * const result = subISOWeekYears(new Date(2014, 8, 1), 5)\n * //=> Mon Aug 31 2009 00:00:00\n */\nexport default function subISOWeekYears(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addISOWeekYears(dirtyDate, -amount);\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,6BAA6B;AACzD,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,SAAS,MAAM,4BAA4B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,eAAeA,CAACC,SAAS,EAAEC,WAAW,EAAE;EAC9DJ,YAAY,CAAC,CAAC,EAAEK,SAAS,CAAC;EAC1B,IAAIC,MAAM,GAAGL,SAAS,CAACG,WAAW,CAAC;EACnC,OAAOL,eAAe,CAACI,SAAS,EAAE,CAACG,MAAM,CAAC;AAC5C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}