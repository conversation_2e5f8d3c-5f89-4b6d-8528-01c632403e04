{"ast": null, "code": "import { ProvidersTablePopupComponent } from './providers-table-popup/providers-table-popup.component';\nimport { egretAnimations } from \"../../../shared/animations/egret-animations\";\nimport { MatTableDataSource } from '@angular/material/table';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"../providers.service\";\nimport * as i4 from \"../../../shared/services/app-confirm/app-confirm.service\";\nimport * as i5 from \"../../../shared/services/app-loader/app-loader.service\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/chips\";\nimport * as i10 from \"@angular/material/paginator\";\nimport * as i11 from \"@angular/material/table\";\nimport * as i12 from \"@ngx-translate/core\";\nfunction ProvidersTableComponent_mat_header_cell_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 21);\n    i0.ɵɵtext(1, \" ID \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProvidersTableComponent_mat_cell_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r14 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", row_r14.providerId, \" \");\n  }\n}\nfunction ProvidersTableComponent_mat_header_cell_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 21);\n    i0.ɵɵtext(1, \" Last Name \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProvidersTableComponent_mat_cell_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r15 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", row_r15.lastName, \" \");\n  }\n}\nfunction ProvidersTableComponent_mat_header_cell_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 21);\n    i0.ɵɵtext(1, \" First Name \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProvidersTableComponent_mat_cell_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r16 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", row_r16.firstName, \" \");\n  }\n}\nfunction ProvidersTableComponent_mat_header_cell_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 21);\n    i0.ɵɵtext(1, \" NPI \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProvidersTableComponent_mat_cell_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r17 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", row_r17.npi, \" \");\n  }\n}\nfunction ProvidersTableComponent_mat_header_cell_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 21);\n    i0.ɵɵtext(1, \" Status \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProvidersTableComponent_mat_cell_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 26)(1, \"mat-chip\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r18 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"color\", row_r18.color);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"color\", \"primary\")(\"selected\", row_r18.active);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", row_r18.active ? \"Active\" : \"Inactive\", \" \");\n  }\n}\nfunction ProvidersTableComponent_mat_header_cell_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 21);\n    i0.ɵɵtext(1, \" Action \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProvidersTableComponent_mat_cell_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-cell\")(1, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function ProvidersTableComponent_mat_cell_25_Template_button_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r21);\n      const row_r19 = restoredCtx.$implicit;\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.openPopUp(row_r19));\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function ProvidersTableComponent_mat_cell_25_Template_button_click_4_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r21);\n      const row_r19 = restoredCtx.$implicit;\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.delete(row_r19));\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"delete\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProvidersTableComponent_mat_header_row_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-header-row\");\n  }\n}\nfunction ProvidersTableComponent_mat_row_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-row\");\n  }\n}\nconst _c0 = () => ({\n  y: \"50px\",\n  delay: \"300ms\"\n});\nconst _c1 = a1 => ({\n  value: \"*\",\n  params: a1\n});\nconst _c2 = () => [10, 25, 100];\nexport class ProvidersTableComponent {\n  constructor(dialog, snack, crudService, confirmService, loader) {\n    this.dialog = dialog;\n    this.snack = snack;\n    this.crudService = crudService;\n    this.confirmService = confirmService;\n    this.loader = loader;\n  }\n  ngOnInit() {\n    this.displayedColumns = this.getDisplayedColumns();\n    this.getAll();\n  }\n  ngAfterViewInit() {\n    this.dataSource.paginator = this.paginator;\n    this.dataSource.sort = this.sort;\n  }\n  ngOnDestroy() {\n    if (this.getItemSub) {\n      this.getItemSub.unsubscribe();\n    }\n  }\n  getDisplayedColumns() {\n    return ['providerId', 'lastName', 'firstName', 'active', 'npi', 'actions'];\n  }\n  getAll() {\n    this.getItemSub = this.crudService.getAll().subscribe(data => {\n      this.dataSource = new MatTableDataSource(data);\n    });\n  }\n  openPopUp(data = {}, isNew) {\n    let title = isNew ? 'Add new Provider' : 'Update Provider';\n    let dialogRef = this.dialog.open(ProvidersTablePopupComponent, {\n      width: '720px',\n      disableClose: true,\n      data: {\n        title: title,\n        payload: data\n      }\n    });\n    dialogRef.afterClosed().subscribe(res => {\n      if (!res) {\n        // If user press cancel\n        return;\n      }\n      if (isNew) {\n        this.loader.open('Adding new Provider');\n        this.crudService.create(res).subscribe(data => {\n          this.dataSource = data;\n          this.loader.close();\n          this.snack.open('Provider Added!', 'OK', {\n            duration: 4000\n          });\n        });\n      } else {\n        this.loader.open('Updating Provider');\n        this.crudService.update(data._id, res).subscribe(data => {\n          this.dataSource = data;\n          this.loader.close();\n          this.snack.open('Provider Updated!', 'OK', {\n            duration: 4000\n          });\n        });\n      }\n    });\n  }\n  deleteItem(row) {\n    this.confirmService.confirm({\n      message: `Delete ${row.name}?`\n    }).subscribe(res => {\n      if (res) {\n        this.loader.open('Deleting Provider');\n        this.crudService.delete(row).subscribe(data => {\n          this.dataSource = data;\n          this.loader.close();\n          this.snack.open('Provider deleted!', 'OK', {\n            duration: 4000\n          });\n        });\n      }\n    });\n  }\n  static #_ = this.ɵfac = function ProvidersTableComponent_Factory(t) {\n    return new (t || ProvidersTableComponent)(i0.ɵɵdirectiveInject(i1.MatDialog), i0.ɵɵdirectiveInject(i2.MatSnackBar), i0.ɵɵdirectiveInject(i3.ProviderService), i0.ɵɵdirectiveInject(i4.AppConfirmService), i0.ɵɵdirectiveInject(i5.AppLoaderService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProvidersTableComponent,\n    selectors: [[\"app-providers-table\"]],\n    viewQuery: function ProvidersTableComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatPaginator, 5);\n        i0.ɵɵviewQuery(MatSort, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n      }\n    },\n    decls: 29,\n    vars: 16,\n    consts: [[1, \"m-3\", \"flex\", \"flex-col\", \"md:flex-row\", \"justify-between\"], [1, \"md:w-[100px]\", \"lg:w-1/6\", \"xl:w-2/6\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"m-2\", 3, \"click\"], [1, \"p-0\"], [\"matSort\", \"\", 3, \"dataSource\"], [\"matColumnDef\", \"providerId\"], [\"mat-sort-header\", \"\", 4, \"matHeaderCellDef\"], [\"data-label\", \"providerId\", 4, \"matCellDef\"], [\"matColumnDef\", \"lastName\"], [\"data-label\", \"lastName\", 4, \"matCellDef\"], [\"matColumnDef\", \"firstName\"], [\"data-label\", \"firstName\", 4, \"matCellDef\"], [\"matColumnDef\", \"npi\"], [\"data-label\", \"npi\", 4, \"matCellDef\"], [\"matColumnDef\", \"active\"], [\"data-label\", \"color\", 3, \"color\", 4, \"matCellDef\"], [\"matColumnDef\", \"actions\"], [4, \"matCellDef\"], [4, \"matHeaderRowDef\"], [4, \"matRowDef\", \"matRowDefColumns\"], [3, \"pageSizeOptions\"], [\"mat-sort-header\", \"\"], [\"data-label\", \"providerId\"], [\"data-label\", \"lastName\"], [\"data-label\", \"firstName\"], [\"data-label\", \"npi\"], [\"data-label\", \"color\"], [\"mat-sm-chip\", \"\", 3, \"color\", \"selected\"], [\"mat-icon-button\", \"\", \"mat-sm-button\", \"\", \"color\", \"primary\", 1, \"mr-4\", \"rtl:ml-4\", 3, \"click\"], [\"mat-icon-button\", \"\", \"mat-sm-button\", \"\", \"color\", \"warn\", 3, \"click\"]],\n    template: function ProvidersTableComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelement(1, \"div\", 1);\n        i0.ɵɵelementStart(2, \"button\", 2);\n        i0.ɵɵlistener(\"click\", function ProvidersTableComponent_Template_button_click_2_listener() {\n          return ctx.openPopUp({}, true);\n        });\n        i0.ɵɵtext(3);\n        i0.ɵɵpipe(4, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(5, \"mat-card\", 3)(6, \"mat-card-content\", 3)(7, \"mat-table\", 4);\n        i0.ɵɵelementContainerStart(8, 5);\n        i0.ɵɵtemplate(9, ProvidersTableComponent_mat_header_cell_9_Template, 2, 0, \"mat-header-cell\", 6)(10, ProvidersTableComponent_mat_cell_10_Template, 2, 1, \"mat-cell\", 7);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(11, 8);\n        i0.ɵɵtemplate(12, ProvidersTableComponent_mat_header_cell_12_Template, 2, 0, \"mat-header-cell\", 6)(13, ProvidersTableComponent_mat_cell_13_Template, 2, 1, \"mat-cell\", 9);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(14, 10);\n        i0.ɵɵtemplate(15, ProvidersTableComponent_mat_header_cell_15_Template, 2, 0, \"mat-header-cell\", 6)(16, ProvidersTableComponent_mat_cell_16_Template, 2, 1, \"mat-cell\", 11);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(17, 12);\n        i0.ɵɵtemplate(18, ProvidersTableComponent_mat_header_cell_18_Template, 2, 0, \"mat-header-cell\", 6)(19, ProvidersTableComponent_mat_cell_19_Template, 2, 1, \"mat-cell\", 13);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(20, 14);\n        i0.ɵɵtemplate(21, ProvidersTableComponent_mat_header_cell_21_Template, 2, 0, \"mat-header-cell\", 6)(22, ProvidersTableComponent_mat_cell_22_Template, 3, 5, \"mat-cell\", 15);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(23, 16);\n        i0.ɵɵtemplate(24, ProvidersTableComponent_mat_header_cell_24_Template, 2, 0, \"mat-header-cell\", 6)(25, ProvidersTableComponent_mat_cell_25_Template, 7, 0, \"mat-cell\", 17);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵtemplate(26, ProvidersTableComponent_mat_header_row_26_Template, 1, 0, \"mat-header-row\", 18)(27, ProvidersTableComponent_mat_row_27_Template, 1, 0, \"mat-row\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(28, \"mat-paginator\", 20);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"@animate\", i0.ɵɵpureFunction1(10, _c1, i0.ɵɵpureFunction0(9, _c0)));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(4, 7, \"ADD\"), \" Provider\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"@animate\", i0.ɵɵpureFunction1(13, _c1, i0.ɵɵpureFunction0(12, _c0)));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"dataSource\", ctx.dataSource);\n        i0.ɵɵadvance(19);\n        i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"pageSizeOptions\", i0.ɵɵpureFunction0(15, _c2));\n      }\n    },\n    dependencies: [i6.MatIcon, i7.MatCard, i7.MatCardContent, i8.MatButton, i8.MatIconButton, i9.MatChip, i10.MatPaginator, i11.MatTable, i11.MatHeaderCellDef, i11.MatHeaderRowDef, i11.MatColumnDef, i11.MatCellDef, i11.MatRowDef, i11.MatHeaderCell, i11.MatCell, i11.MatHeaderRow, i11.MatRow, i12.TranslatePipe],\n    encapsulation: 2,\n    data: {\n      animation: egretAnimations\n    }\n  });\n}", "map": {"version": 3, "names": ["ProvidersTablePopupComponent", "egretAnimations", "MatTableDataSource", "MatPaginator", "MatSort", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "row_r14", "providerId", "row_r15", "lastName", "row_r16", "firstName", "row_r17", "npi", "ɵɵstyleProp", "row_r18", "color", "ɵɵproperty", "active", "ɵɵlistener", "ProvidersTableComponent_mat_cell_25_Template_button_click_1_listener", "restoredCtx", "ɵɵrestoreView", "_r21", "row_r19", "$implicit", "ctx_r20", "ɵɵnextContext", "ɵɵresetView", "openPopUp", "ProvidersTableComponent_mat_cell_25_Template_button_click_4_listener", "ctx_r22", "delete", "ɵɵelement", "ProvidersTableComponent", "constructor", "dialog", "snack", "crudService", "confirmService", "loader", "ngOnInit", "displayedColumns", "getDisplayedColumns", "getAll", "ngAfterViewInit", "dataSource", "paginator", "sort", "ngOnDestroy", "getItemSub", "unsubscribe", "subscribe", "data", "isNew", "title", "dialogRef", "open", "width", "disableClose", "payload", "afterClosed", "res", "create", "close", "duration", "update", "_id", "deleteItem", "row", "confirm", "message", "name", "_", "ɵɵdirectiveInject", "i1", "MatDialog", "i2", "MatSnackBar", "i3", "ProviderService", "i4", "AppConfirmService", "i5", "AppLoaderService", "_2", "selectors", "viewQuery", "ProvidersTableComponent_Query", "rf", "ctx", "ProvidersTableComponent_Template_button_click_2_listener", "ɵɵelementContainerStart", "ɵɵtemplate", "ProvidersTableComponent_mat_header_cell_9_Template", "ProvidersTableComponent_mat_cell_10_Template", "ɵɵelementContainerEnd", "ProvidersTableComponent_mat_header_cell_12_Template", "ProvidersTableComponent_mat_cell_13_Template", "ProvidersTableComponent_mat_header_cell_15_Template", "ProvidersTableComponent_mat_cell_16_Template", "ProvidersTableComponent_mat_header_cell_18_Template", "ProvidersTableComponent_mat_cell_19_Template", "ProvidersTableComponent_mat_header_cell_21_Template", "ProvidersTableComponent_mat_cell_22_Template", "ProvidersTableComponent_mat_header_cell_24_Template", "ProvidersTableComponent_mat_cell_25_Template", "ProvidersTableComponent_mat_header_row_26_Template", "ProvidersTableComponent_mat_row_27_Template", "ɵɵpureFunction1", "_c1", "ɵɵpureFunction0", "_c0", "ɵɵpipeBind1", "_c2"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\views\\providers\\providers-table\\providers-table.component.ts", "C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\views\\providers\\providers-table\\providers-table.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';\r\nimport { ProviderService } from '../providers.service';\r\nimport { MatDialogRef as MatDialogRef, MatDialog as MatDialog } from '@angular/material/dialog';\r\nimport { MatSnackBar as MatSnackBar } from '@angular/material/snack-bar';\r\nimport { AppConfirmService } from '../../../shared/services/app-confirm/app-confirm.service';\r\nimport { AppLoaderService } from '../../../shared/services/app-loader/app-loader.service';\r\nimport { ProvidersTablePopupComponent } from './providers-table-popup/providers-table-popup.component';\r\nimport { Subscription } from 'rxjs';\r\nimport { egretAnimations } from \"../../../shared/animations/egret-animations\";\r\nimport { MatTableDataSource as MatTableDataSource } from '@angular/material/table';\r\nimport { MatPaginator as MatPaginator } from '@angular/material/paginator';\r\nimport { MatSort } from '@angular/material/sort';\r\n\r\n@Component({\r\n  selector: 'app-providers-table',\r\n  templateUrl: './providers-table.component.html',\r\n  animations: egretAnimations\r\n})\r\nexport class ProvidersTableComponent implements OnInit, OnDestroy {\r\n  @ViewChild(MatPaginator) paginator: MatPaginator;\r\n  @ViewChild(MatSort) sort: MatSort;\r\n  \r\n  public dataSource: any;\r\n  public displayedColumns: any;\r\n  public getItemSub: Subscription;\r\n  constructor(\r\n    private dialog: MatDialog,\r\n    private snack: MatSnackBar,\r\n    private crudService: ProviderService,\r\n    private confirmService: AppConfirmService,\r\n    private loader: AppLoaderService\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.displayedColumns = this.getDisplayedColumns();\r\n    this.getAll()\r\n  }\r\n  ngAfterViewInit() {\r\n    this.dataSource.paginator = this.paginator;\r\n    this.dataSource.sort = this.sort;\r\n  }\r\n  ngOnDestroy() {\r\n    if (this.getItemSub) {\r\n      this.getItemSub.unsubscribe()\r\n    }\r\n  }\r\n\r\n  getDisplayedColumns() {\r\n    return ['providerId', 'lastName', 'firstName', 'active', 'npi', 'actions'];\r\n  }\r\n\r\n  getAll() {    \r\n    this.getItemSub = this.crudService.getAll()\r\n      .subscribe(data => {\r\n        this.dataSource = new MatTableDataSource(data);\r\n      })\r\n  }\r\n\r\n  openPopUp(data: any = {}, isNew?) {\r\n    let title = isNew ? 'Add new Provider' : 'Update Provider';\r\n    let dialogRef: MatDialogRef<any> = this.dialog.open(ProvidersTablePopupComponent, {\r\n      width: '720px',\r\n      disableClose: true,\r\n      data: { title: title, payload: data }\r\n    })\r\n    dialogRef.afterClosed()\r\n      .subscribe(res => {\r\n        if(!res) {\r\n          // If user press cancel\r\n          return;\r\n        }\r\n        if (isNew) {\r\n          this.loader.open('Adding new Provider');\r\n          this.crudService.create(res)\r\n            .subscribe(data => {\r\n              this.dataSource = data;\r\n              this.loader.close();\r\n              this.snack.open('Provider Added!', 'OK', { duration: 4000 })\r\n            })\r\n        } else {\r\n          this.loader.open('Updating Provider');\r\n          this.crudService.update(data._id, res)\r\n            .subscribe(data => {\r\n              this.dataSource = data;\r\n              this.loader.close();\r\n              this.snack.open('Provider Updated!', 'OK', { duration: 4000 })\r\n            })\r\n        }\r\n      })\r\n  }\r\n  deleteItem(row) {\r\n    this.confirmService.confirm({message: `Delete ${row.name}?`})\r\n      .subscribe(res => {\r\n        if (res) {\r\n          this.loader.open('Deleting Provider');\r\n          this.crudService.delete(row)\r\n            .subscribe(data => {\r\n              this.dataSource = data;\r\n              this.loader.close();\r\n              this.snack.open('Provider deleted!', 'OK', { duration: 4000 })\r\n            })\r\n        }\r\n      })\r\n  }\r\n}", "<div class=\"m-3 flex flex-col md:flex-row justify-between \" [@animate]=\"{value:'*',params:{y:'50px',delay:'300ms'}}\">\r\n\r\n  <div class=\"md:w-[100px] lg:w-1/6 xl:w-2/6\"></div>\r\n  <button mat-raised-button class=\"m-2\" color=\"primary\" (click)=\"openPopUp({}, true)\">{{\"ADD\" | translate }}\r\n    Provider</button>\r\n</div>\r\n<mat-card class=\"p-0\" [@animate]=\"{value:'*',params:{y:'50px',delay:'300ms'}}\">\r\n  <mat-card-content class=\"p-0\">\r\n    <mat-table [dataSource]=\"dataSource\" matSort>\r\n      <ng-container matColumnDef=\"providerId\">\r\n        <mat-header-cell *matHeaderCellDef mat-sort-header> ID </mat-header-cell>\r\n        <mat-cell *matCellDef=\"let row\" data-label=\"providerId\"> {{row.providerId}} </mat-cell>\r\n      </ng-container>\r\n\r\n      <ng-container matColumnDef=\"lastName\">\r\n        <mat-header-cell *matHeaderCellDef mat-sort-header> Last Name </mat-header-cell>\r\n        <mat-cell *matCellDef=\"let row\" data-label=\"lastName\"> {{row.lastName}} </mat-cell>\r\n      </ng-container>\r\n\r\n      <ng-container matColumnDef=\"firstName\">\r\n        <mat-header-cell *matHeaderCellDef mat-sort-header> First Name </mat-header-cell>\r\n        <mat-cell *matCellDef=\"let row\" data-label=\"firstName\"> {{row.firstName}} </mat-cell>\r\n      </ng-container>\r\n\r\n      <ng-container matColumnDef=\"npi\">\r\n        <mat-header-cell *matHeaderCellDef mat-sort-header> NPI </mat-header-cell>\r\n        <mat-cell *matCellDef=\"let row\" data-label=\"npi\"> {{row.npi}} </mat-cell>\r\n      </ng-container>\r\n\r\n      <ng-container matColumnDef=\"active\">\r\n        <mat-header-cell *matHeaderCellDef mat-sort-header> Status </mat-header-cell>\r\n        <mat-cell *matCellDef=\"let row\" [style.color]=\"row.color\" data-label=\"color\">\r\n          <mat-chip mat-sm-chip [color]=\"'primary'\" [selected]=\"row.active\">{{row.active ? 'Active' : 'Inactive'}}\r\n          </mat-chip>\r\n        </mat-cell>\r\n      </ng-container>\r\n\r\n      <ng-container matColumnDef=\"actions\">\r\n        <mat-header-cell *matHeaderCellDef mat-sort-header> Action </mat-header-cell>\r\n        <mat-cell *matCellDef=\"let row\">\r\n          <button mat-icon-button mat-sm-button color=\"primary\" class=\"mr-4 rtl:ml-4\" (click)=\"openPopUp(row)\">\r\n            <mat-icon>edit</mat-icon>\r\n          </button>\r\n          <button mat-icon-button mat-sm-button color=\"warn\" (click)=\"delete(row)\">\r\n            <mat-icon>delete</mat-icon>\r\n          </button>\r\n        </mat-cell>\r\n      </ng-container>\r\n\r\n      <mat-header-row *matHeaderRowDef=\"displayedColumns\"></mat-header-row>\r\n      <mat-row *matRowDef=\"let row; columns: displayedColumns;\"></mat-row>\r\n    </mat-table>\r\n    <mat-paginator [pageSizeOptions]=\"[10, 25, 100]\"></mat-paginator>\r\n\r\n  </mat-card-content>\r\n</mat-card>"], "mappings": "AAMA,SAASA,4BAA4B,QAAQ,yDAAyD;AAEtG,SAASC,eAAe,QAAQ,6CAA6C;AAC7E,SAASC,kBAAwC,QAAQ,yBAAyB;AAClF,SAASC,YAA4B,QAAQ,6BAA6B;AAC1E,SAASC,OAAO,QAAQ,wBAAwB;;;;;;;;;;;;;;;;ICDxCC,EAAA,CAAAC,cAAA,0BAAmD;IAACD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAkB;;;;;IACzEH,EAAA,CAAAC,cAAA,mBAAwD;IAACD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IAA9BH,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAK,kBAAA,MAAAC,OAAA,CAAAC,UAAA,MAAmB;;;;;IAI5EP,EAAA,CAAAC,cAAA,0BAAmD;IAACD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAkB;;;;;IAChFH,EAAA,CAAAC,cAAA,mBAAsD;IAACD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IAA5BH,EAAA,CAAAI,SAAA,EAAiB;IAAjBJ,EAAA,CAAAK,kBAAA,MAAAG,OAAA,CAAAC,QAAA,MAAiB;;;;;IAIxET,EAAA,CAAAC,cAAA,0BAAmD;IAACD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAkB;;;;;IACjFH,EAAA,CAAAC,cAAA,mBAAuD;IAACD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IAA7BH,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAK,kBAAA,MAAAK,OAAA,CAAAC,SAAA,MAAkB;;;;;IAI1EX,EAAA,CAAAC,cAAA,0BAAmD;IAACD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAkB;;;;;IAC1EH,EAAA,CAAAC,cAAA,mBAAiD;IAACD,EAAA,CAAAE,MAAA,GAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IAAvBH,EAAA,CAAAI,SAAA,EAAY;IAAZJ,EAAA,CAAAK,kBAAA,MAAAO,OAAA,CAAAC,GAAA,MAAY;;;;;IAI9Db,EAAA,CAAAC,cAAA,0BAAmD;IAACD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAkB;;;;;IAC7EH,EAAA,CAAAC,cAAA,mBAA6E;IACTD,EAAA,CAAAE,MAAA,GAClE;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IAFmBH,EAAA,CAAAc,WAAA,UAAAC,OAAA,CAAAC,KAAA,CAAyB;IACjChB,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAiB,UAAA,oBAAmB,aAAAF,OAAA,CAAAG,MAAA;IAAyBlB,EAAA,CAAAI,SAAA,EAClE;IADkEJ,EAAA,CAAAK,kBAAA,KAAAU,OAAA,CAAAG,MAAA,8BAClE;;;;;IAKFlB,EAAA,CAAAC,cAAA,0BAAmD;IAACD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAkB;;;;;;IAC7EH,EAAA,CAAAC,cAAA,eAAgC;IAC8CD,EAAA,CAAAmB,UAAA,mBAAAC,qEAAA;MAAA,MAAAC,WAAA,GAAArB,EAAA,CAAAsB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAA1B,EAAA,CAAA2B,aAAA;MAAA,OAAS3B,EAAA,CAAA4B,WAAA,CAAAF,OAAA,CAAAG,SAAA,CAAAL,OAAA,CAAc;IAAA,EAAC;IAClGxB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE3BH,EAAA,CAAAC,cAAA,iBAAyE;IAAtBD,EAAA,CAAAmB,UAAA,mBAAAW,qEAAA;MAAA,MAAAT,WAAA,GAAArB,EAAA,CAAAsB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAM,OAAA,GAAA/B,EAAA,CAAA2B,aAAA;MAAA,OAAS3B,EAAA,CAAA4B,WAAA,CAAAG,OAAA,CAAAC,MAAA,CAAAR,OAAA,CAAW;IAAA,EAAC;IACtExB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAKjCH,EAAA,CAAAiC,SAAA,qBAAqE;;;;;IACrEjC,EAAA,CAAAiC,SAAA,cAAoE;;;;;;;;;;;;ADhC1E,OAAM,MAAOC,uBAAuB;EAOlCC,YACUC,MAAiB,EACjBC,KAAkB,EAClBC,WAA4B,EAC5BC,cAAiC,EACjCC,MAAwB;IAJxB,KAAAJ,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;EACZ;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACC,mBAAmB,EAAE;IAClD,IAAI,CAACC,MAAM,EAAE;EACf;EACAC,eAAeA,CAAA;IACb,IAAI,CAACC,UAAU,CAACC,SAAS,GAAG,IAAI,CAACA,SAAS;IAC1C,IAAI,CAACD,UAAU,CAACE,IAAI,GAAG,IAAI,CAACA,IAAI;EAClC;EACAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACC,UAAU,EAAE;MACnB,IAAI,CAACA,UAAU,CAACC,WAAW,EAAE;IAC/B;EACF;EAEAR,mBAAmBA,CAAA;IACjB,OAAO,CAAC,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC;EAC5E;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACM,UAAU,GAAG,IAAI,CAACZ,WAAW,CAACM,MAAM,EAAE,CACxCQ,SAAS,CAACC,IAAI,IAAG;MAChB,IAAI,CAACP,UAAU,GAAG,IAAIjD,kBAAkB,CAACwD,IAAI,CAAC;IAChD,CAAC,CAAC;EACN;EAEAxB,SAASA,CAACwB,IAAA,GAAY,EAAE,EAAEC,KAAM;IAC9B,IAAIC,KAAK,GAAGD,KAAK,GAAG,kBAAkB,GAAG,iBAAiB;IAC1D,IAAIE,SAAS,GAAsB,IAAI,CAACpB,MAAM,CAACqB,IAAI,CAAC9D,4BAA4B,EAAE;MAChF+D,KAAK,EAAE,OAAO;MACdC,YAAY,EAAE,IAAI;MAClBN,IAAI,EAAE;QAAEE,KAAK,EAAEA,KAAK;QAAEK,OAAO,EAAEP;MAAI;KACpC,CAAC;IACFG,SAAS,CAACK,WAAW,EAAE,CACpBT,SAAS,CAACU,GAAG,IAAG;MACf,IAAG,CAACA,GAAG,EAAE;QACP;QACA;MACF;MACA,IAAIR,KAAK,EAAE;QACT,IAAI,CAACd,MAAM,CAACiB,IAAI,CAAC,qBAAqB,CAAC;QACvC,IAAI,CAACnB,WAAW,CAACyB,MAAM,CAACD,GAAG,CAAC,CACzBV,SAAS,CAACC,IAAI,IAAG;UAChB,IAAI,CAACP,UAAU,GAAGO,IAAI;UACtB,IAAI,CAACb,MAAM,CAACwB,KAAK,EAAE;UACnB,IAAI,CAAC3B,KAAK,CAACoB,IAAI,CAAC,iBAAiB,EAAE,IAAI,EAAE;YAAEQ,QAAQ,EAAE;UAAI,CAAE,CAAC;QAC9D,CAAC,CAAC;MACN,CAAC,MAAM;QACL,IAAI,CAACzB,MAAM,CAACiB,IAAI,CAAC,mBAAmB,CAAC;QACrC,IAAI,CAACnB,WAAW,CAAC4B,MAAM,CAACb,IAAI,CAACc,GAAG,EAAEL,GAAG,CAAC,CACnCV,SAAS,CAACC,IAAI,IAAG;UAChB,IAAI,CAACP,UAAU,GAAGO,IAAI;UACtB,IAAI,CAACb,MAAM,CAACwB,KAAK,EAAE;UACnB,IAAI,CAAC3B,KAAK,CAACoB,IAAI,CAAC,mBAAmB,EAAE,IAAI,EAAE;YAAEQ,QAAQ,EAAE;UAAI,CAAE,CAAC;QAChE,CAAC,CAAC;MACN;IACF,CAAC,CAAC;EACN;EACAG,UAAUA,CAACC,GAAG;IACZ,IAAI,CAAC9B,cAAc,CAAC+B,OAAO,CAAC;MAACC,OAAO,EAAE,UAAUF,GAAG,CAACG,IAAI;IAAG,CAAC,CAAC,CAC1DpB,SAAS,CAACU,GAAG,IAAG;MACf,IAAIA,GAAG,EAAE;QACP,IAAI,CAACtB,MAAM,CAACiB,IAAI,CAAC,mBAAmB,CAAC;QACrC,IAAI,CAACnB,WAAW,CAACN,MAAM,CAACqC,GAAG,CAAC,CACzBjB,SAAS,CAACC,IAAI,IAAG;UAChB,IAAI,CAACP,UAAU,GAAGO,IAAI;UACtB,IAAI,CAACb,MAAM,CAACwB,KAAK,EAAE;UACnB,IAAI,CAAC3B,KAAK,CAACoB,IAAI,CAAC,mBAAmB,EAAE,IAAI,EAAE;YAAEQ,QAAQ,EAAE;UAAI,CAAE,CAAC;QAChE,CAAC,CAAC;MACN;IACF,CAAC,CAAC;EACN;EAAC,QAAAQ,CAAA,G;qBArFUvC,uBAAuB,EAAAlC,EAAA,CAAA0E,iBAAA,CAAAC,EAAA,CAAAC,SAAA,GAAA5E,EAAA,CAAA0E,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA9E,EAAA,CAAA0E,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAhF,EAAA,CAAA0E,iBAAA,CAAAO,EAAA,CAAAC,iBAAA,GAAAlF,EAAA,CAAA0E,iBAAA,CAAAS,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAvBnD,uBAAuB;IAAAoD,SAAA;IAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBACvB3F,YAAY;uBACZC,OAAO;;;;;;;;;;;;;QCpBpBC,EAAA,CAAAC,cAAA,aAAqH;QAEnHD,EAAA,CAAAiC,SAAA,aAAkD;QAClDjC,EAAA,CAAAC,cAAA,gBAAoF;QAA9BD,EAAA,CAAAmB,UAAA,mBAAAwE,yDAAA;UAAA,OAASD,GAAA,CAAA7D,SAAA,KAAc,IAAI,CAAC;QAAA,EAAC;QAAC7B,EAAA,CAAAE,MAAA,GAC1E;;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAErBH,EAAA,CAAAC,cAAA,kBAA+E;QAGzED,EAAA,CAAA4F,uBAAA,MAAwC;QACtC5F,EAAA,CAAA6F,UAAA,IAAAC,kDAAA,6BAAyE,KAAAC,4CAAA;QAE3E/F,EAAA,CAAAgG,qBAAA,EAAe;QAEfhG,EAAA,CAAA4F,uBAAA,OAAsC;QACpC5F,EAAA,CAAA6F,UAAA,KAAAI,mDAAA,6BAAgF,KAAAC,4CAAA;QAElFlG,EAAA,CAAAgG,qBAAA,EAAe;QAEfhG,EAAA,CAAA4F,uBAAA,QAAuC;QACrC5F,EAAA,CAAA6F,UAAA,KAAAM,mDAAA,6BAAiF,KAAAC,4CAAA;QAEnFpG,EAAA,CAAAgG,qBAAA,EAAe;QAEfhG,EAAA,CAAA4F,uBAAA,QAAiC;QAC/B5F,EAAA,CAAA6F,UAAA,KAAAQ,mDAAA,6BAA0E,KAAAC,4CAAA;QAE5EtG,EAAA,CAAAgG,qBAAA,EAAe;QAEfhG,EAAA,CAAA4F,uBAAA,QAAoC;QAClC5F,EAAA,CAAA6F,UAAA,KAAAU,mDAAA,6BAA6E,KAAAC,4CAAA;QAK/ExG,EAAA,CAAAgG,qBAAA,EAAe;QAEfhG,EAAA,CAAA4F,uBAAA,QAAqC;QACnC5F,EAAA,CAAA6F,UAAA,KAAAY,mDAAA,6BAA6E,KAAAC,4CAAA;QAS/E1G,EAAA,CAAAgG,qBAAA,EAAe;QAEfhG,EAAA,CAAA6F,UAAA,KAAAc,kDAAA,6BAAqE,KAAAC,2CAAA;QAEvE5G,EAAA,CAAAG,YAAA,EAAY;QACZH,EAAA,CAAAiC,SAAA,yBAAiE;QAEnEjC,EAAA,CAAAG,YAAA,EAAmB;;;QAtDuCH,EAAA,CAAAiB,UAAA,aAAAjB,EAAA,CAAA6G,eAAA,KAAAC,GAAA,EAAA9G,EAAA,CAAA+G,eAAA,IAAAC,GAAA,GAAwD;QAG9BhH,EAAA,CAAAI,SAAA,GAC1E;QAD0EJ,EAAA,CAAAK,kBAAA,KAAAL,EAAA,CAAAiH,WAAA,2BAC1E;QAEUjH,EAAA,CAAAI,SAAA,GAAwD;QAAxDJ,EAAA,CAAAiB,UAAA,aAAAjB,EAAA,CAAA6G,eAAA,KAAAC,GAAA,EAAA9G,EAAA,CAAA+G,eAAA,KAAAC,GAAA,GAAwD;QAE/DhH,EAAA,CAAAI,SAAA,GAAyB;QAAzBJ,EAAA,CAAAiB,UAAA,eAAAyE,GAAA,CAAA5C,UAAA,CAAyB;QAyCjB9C,EAAA,CAAAI,SAAA,IAAiC;QAAjCJ,EAAA,CAAAiB,UAAA,oBAAAyE,GAAA,CAAAhD,gBAAA,CAAiC;QACpB1C,EAAA,CAAAI,SAAA,EAA0B;QAA1BJ,EAAA,CAAAiB,UAAA,qBAAAyE,GAAA,CAAAhD,gBAAA,CAA0B;QAE3C1C,EAAA,CAAAI,SAAA,EAAiC;QAAjCJ,EAAA,CAAAiB,UAAA,oBAAAjB,EAAA,CAAA+G,eAAA,KAAAG,GAAA,EAAiC;;;;;;iBDpCtCtH;IAAe;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}