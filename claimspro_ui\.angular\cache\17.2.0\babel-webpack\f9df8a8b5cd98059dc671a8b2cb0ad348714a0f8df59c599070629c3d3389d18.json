{"ast": null, "code": "/*\nLanguage: AutoIt\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: AutoIt language definition\nCategory: scripting\n*/\n\n/** @type LanguageFn */\nfunction autoit(hljs) {\n  const KEYWORDS = 'ByRef Case Const ContinueCase ContinueLoop ' + 'Dim Do Else ElseIf EndFunc EndIf EndSelect ' + 'EndSwitch EndWith Enum Exit ExitLoop For Func ' + 'Global If In Local Next ReDim Return Select Static ' + 'Step Switch Then To Until Volatile WEnd While With';\n  const DIRECTIVES = [\"EndRegion\", \"forcedef\", \"forceref\", \"ignorefunc\", \"include\", \"include-once\", \"NoTrayIcon\", \"OnAutoItStartRegister\", \"pragma\", \"Region\", \"RequireAdmin\", \"Tidy_Off\", \"Tidy_On\", \"Tidy_Parameters\"];\n  const LITERAL = 'True False And Null Not Or Default';\n  const BUILT_IN = 'Abs ACos AdlibRegister AdlibUnRegister Asc AscW ASin Assign ATan AutoItSetOption AutoItWinGetTitle AutoItWinSetTitle Beep Binary BinaryLen BinaryMid BinaryToString BitAND BitNOT BitOR BitRotate BitShift BitXOR BlockInput Break Call CDTray Ceiling Chr ChrW ClipGet ClipPut ConsoleRead ConsoleWrite ConsoleWriteError ControlClick ControlCommand ControlDisable ControlEnable ControlFocus ControlGetFocus ControlGetHandle ControlGetPos ControlGetText ControlHide ControlListView ControlMove ControlSend ControlSetText ControlShow ControlTreeView Cos Dec DirCopy DirCreate DirGetSize DirMove DirRemove DllCall DllCallAddress DllCallbackFree DllCallbackGetPtr DllCallbackRegister DllClose DllOpen DllStructCreate DllStructGetData DllStructGetPtr DllStructGetSize DllStructSetData DriveGetDrive DriveGetFileSystem DriveGetLabel DriveGetSerial DriveGetType DriveMapAdd DriveMapDel DriveMapGet DriveSetLabel DriveSpaceFree DriveSpaceTotal DriveStatus EnvGet EnvSet EnvUpdate Eval Execute Exp FileChangeDir FileClose FileCopy FileCreateNTFSLink FileCreateShortcut FileDelete FileExists FileFindFirstFile FileFindNextFile FileFlush FileGetAttrib FileGetEncoding FileGetLongName FileGetPos FileGetShortcut FileGetShortName FileGetSize FileGetTime FileGetVersion FileInstall FileMove FileOpen FileOpenDialog FileRead FileReadLine FileReadToArray FileRecycle FileRecycleEmpty FileSaveDialog FileSelectFolder FileSetAttrib FileSetEnd FileSetPos FileSetTime FileWrite FileWriteLine Floor FtpSetProxy FuncName GUICreate GUICtrlCreateAvi GUICtrlCreateButton GUICtrlCreateCheckbox GUICtrlCreateCombo GUICtrlCreateContextMenu GUICtrlCreateDate GUICtrlCreateDummy GUICtrlCreateEdit GUICtrlCreateGraphic GUICtrlCreateGroup GUICtrlCreateIcon GUICtrlCreateInput GUICtrlCreateLabel GUICtrlCreateList GUICtrlCreateListView GUICtrlCreateListViewItem GUICtrlCreateMenu GUICtrlCreateMenuItem GUICtrlCreateMonthCal GUICtrlCreateObj GUICtrlCreatePic GUICtrlCreateProgress GUICtrlCreateRadio GUICtrlCreateSlider GUICtrlCreateTab GUICtrlCreateTabItem GUICtrlCreateTreeView GUICtrlCreateTreeViewItem GUICtrlCreateUpdown GUICtrlDelete GUICtrlGetHandle GUICtrlGetState GUICtrlRead GUICtrlRecvMsg GUICtrlRegisterListViewSort GUICtrlSendMsg GUICtrlSendToDummy GUICtrlSetBkColor GUICtrlSetColor GUICtrlSetCursor GUICtrlSetData GUICtrlSetDefBkColor GUICtrlSetDefColor GUICtrlSetFont GUICtrlSetGraphic GUICtrlSetImage GUICtrlSetLimit GUICtrlSetOnEvent GUICtrlSetPos GUICtrlSetResizing GUICtrlSetState GUICtrlSetStyle GUICtrlSetTip GUIDelete GUIGetCursorInfo GUIGetMsg GUIGetStyle GUIRegisterMsg GUISetAccelerators GUISetBkColor GUISetCoord GUISetCursor GUISetFont GUISetHelp GUISetIcon GUISetOnEvent GUISetState GUISetStyle GUIStartGroup GUISwitch Hex HotKeySet HttpSetProxy HttpSetUserAgent HWnd InetClose InetGet InetGetInfo InetGetSize InetRead IniDelete IniRead IniReadSection IniReadSectionNames IniRenameSection IniWrite IniWriteSection InputBox Int IsAdmin IsArray IsBinary IsBool IsDeclared IsDllStruct IsFloat IsFunc IsHWnd IsInt IsKeyword IsNumber IsObj IsPtr IsString Log MemGetStats Mod MouseClick MouseClickDrag MouseDown MouseGetCursor MouseGetPos MouseMove MouseUp MouseWheel MsgBox Number ObjCreate ObjCreateInterface ObjEvent ObjGet ObjName OnAutoItExitRegister OnAutoItExitUnRegister Ping PixelChecksum PixelGetColor PixelSearch ProcessClose ProcessExists ProcessGetStats ProcessList ProcessSetPriority ProcessWait ProcessWaitClose ProgressOff ProgressOn ProgressSet Ptr Random RegDelete RegEnumKey RegEnumVal RegRead RegWrite Round Run RunAs RunAsWait RunWait Send SendKeepActive SetError SetExtended ShellExecute ShellExecuteWait Shutdown Sin Sleep SoundPlay SoundSetWaveVolume SplashImageOn SplashOff SplashTextOn Sqrt SRandom StatusbarGetText StderrRead StdinWrite StdioClose StdoutRead String StringAddCR StringCompare StringFormat StringFromASCIIArray StringInStr StringIsAlNum StringIsAlpha StringIsASCII StringIsDigit StringIsFloat StringIsInt StringIsLower StringIsSpace StringIsUpper StringIsXDigit StringLeft StringLen StringLower StringMid StringRegExp StringRegExpReplace StringReplace StringReverse StringRight StringSplit StringStripCR StringStripWS StringToASCIIArray StringToBinary StringTrimLeft StringTrimRight StringUpper Tan TCPAccept TCPCloseSocket TCPConnect TCPListen TCPNameToIP TCPRecv TCPSend TCPShutdown, UDPShutdown TCPStartup, UDPStartup TimerDiff TimerInit ToolTip TrayCreateItem TrayCreateMenu TrayGetMsg TrayItemDelete TrayItemGetHandle TrayItemGetState TrayItemGetText TrayItemSetOnEvent TrayItemSetState TrayItemSetText TraySetClick TraySetIcon TraySetOnEvent TraySetPauseIcon TraySetState TraySetToolTip TrayTip UBound UDPBind UDPCloseSocket UDPOpen UDPRecv UDPSend VarGetType WinActivate WinActive WinClose WinExists WinFlash WinGetCaretPos WinGetClassList WinGetClientSize WinGetHandle WinGetPos WinGetProcess WinGetState WinGetText WinGetTitle WinKill WinList WinMenuSelectItem WinMinimizeAll WinMinimizeAllUndo WinMove WinSetOnTop WinSetState WinSetTitle WinSetTrans WinWait WinWaitActive WinWaitClose WinWaitNotActive';\n  const COMMENT = {\n    variants: [hljs.COMMENT(';', '$', {\n      relevance: 0\n    }), hljs.COMMENT('#cs', '#ce'), hljs.COMMENT('#comments-start', '#comments-end')]\n  };\n  const VARIABLE = {\n    begin: '\\\\$[A-z0-9_]+'\n  };\n  const STRING = {\n    className: 'string',\n    variants: [{\n      begin: /\"/,\n      end: /\"/,\n      contains: [{\n        begin: /\"\"/,\n        relevance: 0\n      }]\n    }, {\n      begin: /'/,\n      end: /'/,\n      contains: [{\n        begin: /''/,\n        relevance: 0\n      }]\n    }]\n  };\n  const NUMBER = {\n    variants: [hljs.BINARY_NUMBER_MODE, hljs.C_NUMBER_MODE]\n  };\n  const PREPROCESSOR = {\n    className: 'meta',\n    begin: '#',\n    end: '$',\n    keywords: {\n      keyword: DIRECTIVES\n    },\n    contains: [{\n      begin: /\\\\\\n/,\n      relevance: 0\n    }, {\n      beginKeywords: 'include',\n      keywords: {\n        keyword: 'include'\n      },\n      end: '$',\n      contains: [STRING, {\n        className: 'string',\n        variants: [{\n          begin: '<',\n          end: '>'\n        }, {\n          begin: /\"/,\n          end: /\"/,\n          contains: [{\n            begin: /\"\"/,\n            relevance: 0\n          }]\n        }, {\n          begin: /'/,\n          end: /'/,\n          contains: [{\n            begin: /''/,\n            relevance: 0\n          }]\n        }]\n      }]\n    }, STRING, COMMENT]\n  };\n  const CONSTANT = {\n    className: 'symbol',\n    // begin: '@',\n    // end: '$',\n    // keywords: 'AppDataCommonDir AppDataDir AutoItExe AutoItPID AutoItVersion AutoItX64 COM_EventObj CommonFilesDir Compiled ComputerName ComSpec CPUArch CR CRLF DesktopCommonDir DesktopDepth DesktopDir DesktopHeight DesktopRefresh DesktopWidth DocumentsCommonDir error exitCode exitMethod extended FavoritesCommonDir FavoritesDir GUI_CtrlHandle GUI_CtrlId GUI_DragFile GUI_DragId GUI_DropId GUI_WinHandle HomeDrive HomePath HomeShare HotKeyPressed HOUR IPAddress1 IPAddress2 IPAddress3 IPAddress4 KBLayout LF LocalAppDataDir LogonDNSDomain LogonDomain LogonServer MDAY MIN MON MSEC MUILang MyDocumentsDir NumParams OSArch OSBuild OSLang OSServicePack OSType OSVersion ProgramFilesDir ProgramsCommonDir ProgramsDir ScriptDir ScriptFullPath ScriptLineNumber ScriptName SEC StartMenuCommonDir StartMenuDir StartupCommonDir StartupDir SW_DISABLE SW_ENABLE SW_HIDE SW_LOCK SW_MAXIMIZE SW_MINIMIZE SW_RESTORE SW_SHOW SW_SHOWDEFAULT SW_SHOWMAXIMIZED SW_SHOWMINIMIZED SW_SHOWMINNOACTIVE SW_SHOWNA SW_SHOWNOACTIVATE SW_SHOWNORMAL SW_UNLOCK SystemDir TAB TempDir TRAY_ID TrayIconFlashing TrayIconVisible UserName UserProfileDir WDAY WindowsDir WorkingDir YDAY YEAR',\n    // relevance: 5\n    begin: '@[A-z0-9_]+'\n  };\n  const FUNCTION = {\n    beginKeywords: 'Func',\n    end: '$',\n    illegal: '\\\\$|\\\\[|%',\n    contains: [hljs.inherit(hljs.UNDERSCORE_TITLE_MODE, {\n      className: \"title.function\"\n    }), {\n      className: 'params',\n      begin: '\\\\(',\n      end: '\\\\)',\n      contains: [VARIABLE, STRING, NUMBER]\n    }]\n  };\n  return {\n    name: 'AutoIt',\n    case_insensitive: true,\n    illegal: /\\/\\*/,\n    keywords: {\n      keyword: KEYWORDS,\n      built_in: BUILT_IN,\n      literal: LITERAL\n    },\n    contains: [COMMENT, VARIABLE, STRING, NUMBER, PREPROCESSOR, CONSTANT, FUNCTION]\n  };\n}\nmodule.exports = autoit;", "map": {"version": 3, "names": ["autoit", "hljs", "KEYWORDS", "DIRECTIVES", "LITERAL", "BUILT_IN", "COMMENT", "variants", "relevance", "VARIABLE", "begin", "STRING", "className", "end", "contains", "NUMBER", "BINARY_NUMBER_MODE", "C_NUMBER_MODE", "PREPROCESSOR", "keywords", "keyword", "beginKeywords", "CONSTANT", "FUNCTION", "illegal", "inherit", "UNDERSCORE_TITLE_MODE", "name", "case_insensitive", "built_in", "literal", "module", "exports"], "sources": ["C:/OneDrive/Srikant/SWIP/ClaimsPro/claimspro200-src/claimspro_ui/node_modules/highlight.js/lib/languages/autoit.js"], "sourcesContent": ["/*\nLanguage: AutoIt\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: AutoIt language definition\nCategory: scripting\n*/\n\n/** @type LanguageFn */\nfunction autoit(hljs) {\n  const KEYWORDS = 'ByRef Case Const ContinueCase ContinueLoop '\n        + 'Dim Do Else ElseIf EndFunc EndIf EndSelect '\n        + 'EndSwitch EndWith Enum Exit ExitLoop For Func '\n        + 'Global If In Local Next ReDim Return Select Static '\n        + 'Step Switch Then To Until Volatile WEnd While With';\n\n  const DIRECTIVES = [\n    \"EndRegion\",\n    \"forcedef\",\n    \"forceref\",\n    \"ignorefunc\",\n    \"include\",\n    \"include-once\",\n    \"NoTrayIcon\",\n    \"OnAutoItStartRegister\",\n    \"pragma\",\n    \"Region\",\n    \"RequireAdmin\",\n    \"Tidy_Off\",\n    \"Tidy_On\",\n    \"Tidy_Parameters\"\n  ];\n\n  const LITERAL = 'True False And Null Not Or Default';\n\n  const BUILT_IN =\n          'Abs ACos AdlibRegister AdlibUnRegister Asc AscW ASin Assign ATan AutoItSetOption AutoItWinGetTitle AutoItWinSetTitle Beep Binary BinaryLen BinaryMid BinaryToString BitAND BitNOT BitOR BitRotate BitShift BitXOR BlockInput Break Call CDTray Ceiling Chr ChrW ClipGet ClipPut ConsoleRead ConsoleWrite ConsoleWriteError ControlClick ControlCommand ControlDisable ControlEnable ControlFocus ControlGetFocus ControlGetHandle ControlGetPos ControlGetText ControlHide ControlListView ControlMove ControlSend ControlSetText ControlShow ControlTreeView Cos Dec DirCopy DirCreate DirGetSize DirMove DirRemove DllCall DllCallAddress DllCallbackFree DllCallbackGetPtr DllCallbackRegister DllClose DllOpen DllStructCreate DllStructGetData DllStructGetPtr DllStructGetSize DllStructSetData DriveGetDrive DriveGetFileSystem DriveGetLabel DriveGetSerial DriveGetType DriveMapAdd DriveMapDel DriveMapGet DriveSetLabel DriveSpaceFree DriveSpaceTotal DriveStatus EnvGet EnvSet EnvUpdate Eval Execute Exp FileChangeDir FileClose FileCopy FileCreateNTFSLink FileCreateShortcut FileDelete FileExists FileFindFirstFile FileFindNextFile FileFlush FileGetAttrib FileGetEncoding FileGetLongName FileGetPos FileGetShortcut FileGetShortName FileGetSize FileGetTime FileGetVersion FileInstall FileMove FileOpen FileOpenDialog FileRead FileReadLine FileReadToArray FileRecycle FileRecycleEmpty FileSaveDialog FileSelectFolder FileSetAttrib FileSetEnd FileSetPos FileSetTime FileWrite FileWriteLine Floor FtpSetProxy FuncName GUICreate GUICtrlCreateAvi GUICtrlCreateButton GUICtrlCreateCheckbox GUICtrlCreateCombo GUICtrlCreateContextMenu GUICtrlCreateDate GUICtrlCreateDummy GUICtrlCreateEdit GUICtrlCreateGraphic GUICtrlCreateGroup GUICtrlCreateIcon GUICtrlCreateInput GUICtrlCreateLabel GUICtrlCreateList GUICtrlCreateListView GUICtrlCreateListViewItem GUICtrlCreateMenu GUICtrlCreateMenuItem GUICtrlCreateMonthCal GUICtrlCreateObj GUICtrlCreatePic GUICtrlCreateProgress GUICtrlCreateRadio GUICtrlCreateSlider GUICtrlCreateTab GUICtrlCreateTabItem GUICtrlCreateTreeView GUICtrlCreateTreeViewItem GUICtrlCreateUpdown GUICtrlDelete GUICtrlGetHandle GUICtrlGetState GUICtrlRead GUICtrlRecvMsg GUICtrlRegisterListViewSort GUICtrlSendMsg GUICtrlSendToDummy GUICtrlSetBkColor GUICtrlSetColor GUICtrlSetCursor GUICtrlSetData GUICtrlSetDefBkColor GUICtrlSetDefColor GUICtrlSetFont GUICtrlSetGraphic GUICtrlSetImage GUICtrlSetLimit GUICtrlSetOnEvent GUICtrlSetPos GUICtrlSetResizing GUICtrlSetState GUICtrlSetStyle GUICtrlSetTip GUIDelete GUIGetCursorInfo GUIGetMsg GUIGetStyle GUIRegisterMsg GUISetAccelerators GUISetBkColor GUISetCoord GUISetCursor GUISetFont GUISetHelp GUISetIcon GUISetOnEvent GUISetState GUISetStyle GUIStartGroup GUISwitch Hex HotKeySet HttpSetProxy HttpSetUserAgent HWnd InetClose InetGet InetGetInfo InetGetSize InetRead IniDelete IniRead IniReadSection IniReadSectionNames IniRenameSection IniWrite IniWriteSection InputBox Int IsAdmin IsArray IsBinary IsBool IsDeclared IsDllStruct IsFloat IsFunc IsHWnd IsInt IsKeyword IsNumber IsObj IsPtr IsString Log MemGetStats Mod MouseClick MouseClickDrag MouseDown MouseGetCursor MouseGetPos MouseMove MouseUp MouseWheel MsgBox Number ObjCreate ObjCreateInterface ObjEvent ObjGet ObjName OnAutoItExitRegister OnAutoItExitUnRegister Ping PixelChecksum PixelGetColor PixelSearch ProcessClose ProcessExists ProcessGetStats ProcessList ProcessSetPriority ProcessWait ProcessWaitClose ProgressOff ProgressOn ProgressSet Ptr Random RegDelete RegEnumKey RegEnumVal RegRead RegWrite Round Run RunAs RunAsWait RunWait Send SendKeepActive SetError SetExtended ShellExecute ShellExecuteWait Shutdown Sin Sleep SoundPlay SoundSetWaveVolume SplashImageOn SplashOff SplashTextOn Sqrt SRandom StatusbarGetText StderrRead StdinWrite StdioClose StdoutRead String StringAddCR StringCompare StringFormat StringFromASCIIArray StringInStr StringIsAlNum StringIsAlpha StringIsASCII StringIsDigit StringIsFloat StringIsInt StringIsLower StringIsSpace StringIsUpper StringIsXDigit StringLeft StringLen StringLower StringMid StringRegExp StringRegExpReplace StringReplace StringReverse StringRight StringSplit StringStripCR StringStripWS StringToASCIIArray StringToBinary StringTrimLeft StringTrimRight StringUpper Tan TCPAccept TCPCloseSocket TCPConnect TCPListen TCPNameToIP TCPRecv TCPSend TCPShutdown, UDPShutdown TCPStartup, UDPStartup TimerDiff TimerInit ToolTip TrayCreateItem TrayCreateMenu TrayGetMsg TrayItemDelete TrayItemGetHandle TrayItemGetState TrayItemGetText TrayItemSetOnEvent TrayItemSetState TrayItemSetText TraySetClick TraySetIcon TraySetOnEvent TraySetPauseIcon TraySetState TraySetToolTip TrayTip UBound UDPBind UDPCloseSocket UDPOpen UDPRecv UDPSend VarGetType WinActivate WinActive WinClose WinExists WinFlash WinGetCaretPos WinGetClassList WinGetClientSize WinGetHandle WinGetPos WinGetProcess WinGetState WinGetText WinGetTitle WinKill WinList WinMenuSelectItem WinMinimizeAll WinMinimizeAllUndo WinMove WinSetOnTop WinSetState WinSetTitle WinSetTrans WinWait WinWaitActive WinWaitClose WinWaitNotActive';\n\n  const COMMENT = { variants: [\n    hljs.COMMENT(';', '$', { relevance: 0 }),\n    hljs.COMMENT('#cs', '#ce'),\n    hljs.COMMENT('#comments-start', '#comments-end')\n  ] };\n\n  const VARIABLE = { begin: '\\\\$[A-z0-9_]+' };\n\n  const STRING = {\n    className: 'string',\n    variants: [\n      {\n        begin: /\"/,\n        end: /\"/,\n        contains: [\n          {\n            begin: /\"\"/,\n            relevance: 0\n          }\n        ]\n      },\n      {\n        begin: /'/,\n        end: /'/,\n        contains: [\n          {\n            begin: /''/,\n            relevance: 0\n          }\n        ]\n      }\n    ]\n  };\n\n  const NUMBER = { variants: [\n    hljs.BINARY_NUMBER_MODE,\n    hljs.C_NUMBER_MODE\n  ] };\n\n  const PREPROCESSOR = {\n    className: 'meta',\n    begin: '#',\n    end: '$',\n    keywords: { keyword: DIRECTIVES },\n    contains: [\n      {\n        begin: /\\\\\\n/,\n        relevance: 0\n      },\n      {\n        beginKeywords: 'include',\n        keywords: { keyword: 'include' },\n        end: '$',\n        contains: [\n          STRING,\n          {\n            className: 'string',\n            variants: [\n              {\n                begin: '<',\n                end: '>'\n              },\n              {\n                begin: /\"/,\n                end: /\"/,\n                contains: [\n                  {\n                    begin: /\"\"/,\n                    relevance: 0\n                  }\n                ]\n              },\n              {\n                begin: /'/,\n                end: /'/,\n                contains: [\n                  {\n                    begin: /''/,\n                    relevance: 0\n                  }\n                ]\n              }\n            ]\n          }\n        ]\n      },\n      STRING,\n      COMMENT\n    ]\n  };\n\n  const CONSTANT = {\n    className: 'symbol',\n    // begin: '@',\n    // end: '$',\n    // keywords: 'AppDataCommonDir AppDataDir AutoItExe AutoItPID AutoItVersion AutoItX64 COM_EventObj CommonFilesDir Compiled ComputerName ComSpec CPUArch CR CRLF DesktopCommonDir DesktopDepth DesktopDir DesktopHeight DesktopRefresh DesktopWidth DocumentsCommonDir error exitCode exitMethod extended FavoritesCommonDir FavoritesDir GUI_CtrlHandle GUI_CtrlId GUI_DragFile GUI_DragId GUI_DropId GUI_WinHandle HomeDrive HomePath HomeShare HotKeyPressed HOUR IPAddress1 IPAddress2 IPAddress3 IPAddress4 KBLayout LF LocalAppDataDir LogonDNSDomain LogonDomain LogonServer MDAY MIN MON MSEC MUILang MyDocumentsDir NumParams OSArch OSBuild OSLang OSServicePack OSType OSVersion ProgramFilesDir ProgramsCommonDir ProgramsDir ScriptDir ScriptFullPath ScriptLineNumber ScriptName SEC StartMenuCommonDir StartMenuDir StartupCommonDir StartupDir SW_DISABLE SW_ENABLE SW_HIDE SW_LOCK SW_MAXIMIZE SW_MINIMIZE SW_RESTORE SW_SHOW SW_SHOWDEFAULT SW_SHOWMAXIMIZED SW_SHOWMINIMIZED SW_SHOWMINNOACTIVE SW_SHOWNA SW_SHOWNOACTIVATE SW_SHOWNORMAL SW_UNLOCK SystemDir TAB TempDir TRAY_ID TrayIconFlashing TrayIconVisible UserName UserProfileDir WDAY WindowsDir WorkingDir YDAY YEAR',\n    // relevance: 5\n    begin: '@[A-z0-9_]+'\n  };\n\n  const FUNCTION = {\n    beginKeywords: 'Func',\n    end: '$',\n    illegal: '\\\\$|\\\\[|%',\n    contains: [\n      hljs.inherit(hljs.UNDERSCORE_TITLE_MODE, { className: \"title.function\" }),\n      {\n        className: 'params',\n        begin: '\\\\(',\n        end: '\\\\)',\n        contains: [\n          VARIABLE,\n          STRING,\n          NUMBER\n        ]\n      }\n    ]\n  };\n\n  return {\n    name: 'AutoIt',\n    case_insensitive: true,\n    illegal: /\\/\\*/,\n    keywords: {\n      keyword: KEYWORDS,\n      built_in: BUILT_IN,\n      literal: LITERAL\n    },\n    contains: [\n      COMMENT,\n      VARIABLE,\n      STRING,\n      NUMBER,\n      PREPROCESSOR,\n      CONSTANT,\n      FUNCTION\n    ]\n  };\n}\n\nmodule.exports = autoit;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,MAAMC,QAAQ,GAAG,6CAA6C,GACtD,6CAA6C,GAC7C,gDAAgD,GAChD,qDAAqD,GACrD,oDAAoD;EAE5D,MAAMC,UAAU,GAAG,CACjB,WAAW,EACX,UAAU,EACV,UAAU,EACV,YAAY,EACZ,SAAS,EACT,cAAc,EACd,YAAY,EACZ,uBAAuB,EACvB,QAAQ,EACR,QAAQ,EACR,cAAc,EACd,UAAU,EACV,SAAS,EACT,iBAAiB,CAClB;EAED,MAAMC,OAAO,GAAG,oCAAoC;EAEpD,MAAMC,QAAQ,GACN,66JAA66J;EAEr7J,MAAMC,OAAO,GAAG;IAAEC,QAAQ,EAAE,CAC1BN,IAAI,CAACK,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE;MAAEE,SAAS,EAAE;IAAE,CAAC,CAAC,EACxCP,IAAI,CAACK,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,EAC1BL,IAAI,CAACK,OAAO,CAAC,iBAAiB,EAAE,eAAe,CAAC;EAChD,CAAC;EAEH,MAAMG,QAAQ,GAAG;IAAEC,KAAK,EAAE;EAAgB,CAAC;EAE3C,MAAMC,MAAM,GAAG;IACbC,SAAS,EAAE,QAAQ;IACnBL,QAAQ,EAAE,CACR;MACEG,KAAK,EAAE,GAAG;MACVG,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,CACR;QACEJ,KAAK,EAAE,IAAI;QACXF,SAAS,EAAE;MACb,CAAC;IAEL,CAAC,EACD;MACEE,KAAK,EAAE,GAAG;MACVG,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,CACR;QACEJ,KAAK,EAAE,IAAI;QACXF,SAAS,EAAE;MACb,CAAC;IAEL,CAAC;EAEL,CAAC;EAED,MAAMO,MAAM,GAAG;IAAER,QAAQ,EAAE,CACzBN,IAAI,CAACe,kBAAkB,EACvBf,IAAI,CAACgB,aAAa;EAClB,CAAC;EAEH,MAAMC,YAAY,GAAG;IACnBN,SAAS,EAAE,MAAM;IACjBF,KAAK,EAAE,GAAG;IACVG,GAAG,EAAE,GAAG;IACRM,QAAQ,EAAE;MAAEC,OAAO,EAAEjB;IAAW,CAAC;IACjCW,QAAQ,EAAE,CACR;MACEJ,KAAK,EAAE,MAAM;MACbF,SAAS,EAAE;IACb,CAAC,EACD;MACEa,aAAa,EAAE,SAAS;MACxBF,QAAQ,EAAE;QAAEC,OAAO,EAAE;MAAU,CAAC;MAChCP,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,CACRH,MAAM,EACN;QACEC,SAAS,EAAE,QAAQ;QACnBL,QAAQ,EAAE,CACR;UACEG,KAAK,EAAE,GAAG;UACVG,GAAG,EAAE;QACP,CAAC,EACD;UACEH,KAAK,EAAE,GAAG;UACVG,GAAG,EAAE,GAAG;UACRC,QAAQ,EAAE,CACR;YACEJ,KAAK,EAAE,IAAI;YACXF,SAAS,EAAE;UACb,CAAC;QAEL,CAAC,EACD;UACEE,KAAK,EAAE,GAAG;UACVG,GAAG,EAAE,GAAG;UACRC,QAAQ,EAAE,CACR;YACEJ,KAAK,EAAE,IAAI;YACXF,SAAS,EAAE;UACb,CAAC;QAEL,CAAC;MAEL,CAAC;IAEL,CAAC,EACDG,MAAM,EACNL,OAAO;EAEX,CAAC;EAED,MAAMgB,QAAQ,GAAG;IACfV,SAAS,EAAE,QAAQ;IACnB;IACA;IACA;IACA;IACAF,KAAK,EAAE;EACT,CAAC;EAED,MAAMa,QAAQ,GAAG;IACfF,aAAa,EAAE,MAAM;IACrBR,GAAG,EAAE,GAAG;IACRW,OAAO,EAAE,WAAW;IACpBV,QAAQ,EAAE,CACRb,IAAI,CAACwB,OAAO,CAACxB,IAAI,CAACyB,qBAAqB,EAAE;MAAEd,SAAS,EAAE;IAAiB,CAAC,CAAC,EACzE;MACEA,SAAS,EAAE,QAAQ;MACnBF,KAAK,EAAE,KAAK;MACZG,GAAG,EAAE,KAAK;MACVC,QAAQ,EAAE,CACRL,QAAQ,EACRE,MAAM,EACNI,MAAM;IAEV,CAAC;EAEL,CAAC;EAED,OAAO;IACLY,IAAI,EAAE,QAAQ;IACdC,gBAAgB,EAAE,IAAI;IACtBJ,OAAO,EAAE,MAAM;IACfL,QAAQ,EAAE;MACRC,OAAO,EAAElB,QAAQ;MACjB2B,QAAQ,EAAExB,QAAQ;MAClByB,OAAO,EAAE1B;IACX,CAAC;IACDU,QAAQ,EAAE,CACRR,OAAO,EACPG,QAAQ,EACRE,MAAM,EACNI,MAAM,EACNG,YAAY,EACZI,QAAQ,EACRC,QAAQ;EAEZ,CAAC;AACH;AAEAQ,MAAM,CAACC,OAAO,GAAGhC,MAAM"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}