{"ast": null, "code": "/*\nLanguage: Monkey\nDescription: Monkey2 is an easy to use, cross platform, games oriented programming language from Blitz Research.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://blitzresearch.itch.io/monkey2\n*/\n\nfunction monkey(hljs) {\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [{\n      begin: '[$][a-fA-F0-9]+'\n    }, hljs.NUMBER_MODE]\n  };\n  const FUNC_DEFINITION = {\n    variants: [{\n      match: [/(function|method)/, /\\s+/, hljs.UNDERSCORE_IDENT_RE]\n    }],\n    scope: {\n      1: \"keyword\",\n      3: \"title.function\"\n    }\n  };\n  const CLASS_DEFINITION = {\n    variants: [{\n      match: [/(class|interface|extends|implements)/, /\\s+/, hljs.UNDERSCORE_IDENT_RE]\n    }],\n    scope: {\n      1: \"keyword\",\n      3: \"title.class\"\n    }\n  };\n  const BUILT_INS = [\"DebugLog\", \"DebugStop\", \"Error\", \"Print\", \"ACos\", \"ACosr\", \"ASin\", \"ASinr\", \"ATan\", \"ATan2\", \"ATan2r\", \"ATanr\", \"Abs\", \"Abs\", \"Ceil\", \"Clamp\", \"Clamp\", \"Cos\", \"Cosr\", \"Exp\", \"Floor\", \"Log\", \"Max\", \"Max\", \"Min\", \"Min\", \"Pow\", \"Sgn\", \"Sgn\", \"Sin\", \"Sinr\", \"Sqrt\", \"Tan\", \"Tanr\", \"Seed\", \"PI\", \"HALFPI\", \"TWOPI\"];\n  const LITERALS = [\"true\", \"false\", \"null\"];\n  const KEYWORDS = [\"public\", \"private\", \"property\", \"continue\", \"exit\", \"extern\", \"new\", \"try\", \"catch\", \"eachin\", \"not\", \"abstract\", \"final\", \"select\", \"case\", \"default\", \"const\", \"local\", \"global\", \"field\", \"end\", \"if\", \"then\", \"else\", \"elseif\", \"endif\", \"while\", \"wend\", \"repeat\", \"until\", \"forever\", \"for\", \"to\", \"step\", \"next\", \"return\", \"module\", \"inline\", \"throw\", \"import\",\n  // not positive, but these are not literals\n  \"and\", \"or\", \"shl\", \"shr\", \"mod\"];\n  return {\n    name: 'Monkey',\n    case_insensitive: true,\n    keywords: {\n      keyword: KEYWORDS,\n      built_in: BUILT_INS,\n      literal: LITERALS\n    },\n    illegal: /\\/\\*/,\n    contains: [hljs.COMMENT('#rem', '#end'), hljs.COMMENT(\"'\", '$', {\n      relevance: 0\n    }), FUNC_DEFINITION, CLASS_DEFINITION, {\n      className: 'variable.language',\n      begin: /\\b(self|super)\\b/\n    }, {\n      className: 'meta',\n      begin: /\\s*#/,\n      end: '$',\n      keywords: {\n        keyword: 'if else elseif endif end then'\n      }\n    }, {\n      match: [/^\\s*/, /strict\\b/],\n      scope: {\n        2: \"meta\"\n      }\n    }, {\n      beginKeywords: 'alias',\n      end: '=',\n      contains: [hljs.UNDERSCORE_TITLE_MODE]\n    }, hljs.QUOTE_STRING_MODE, NUMBER]\n  };\n}\nmodule.exports = monkey;", "map": {"version": 3, "names": ["monkey", "hljs", "NUMBER", "className", "relevance", "variants", "begin", "NUMBER_MODE", "FUNC_DEFINITION", "match", "UNDERSCORE_IDENT_RE", "scope", "CLASS_DEFINITION", "BUILT_INS", "LITERALS", "KEYWORDS", "name", "case_insensitive", "keywords", "keyword", "built_in", "literal", "illegal", "contains", "COMMENT", "end", "beginKeywords", "UNDERSCORE_TITLE_MODE", "QUOTE_STRING_MODE", "module", "exports"], "sources": ["C:/OneDrive/Srikant/SWIP/ClaimsPro/claimspro200-src/claimspro_ui/node_modules/highlight.js/lib/languages/monkey.js"], "sourcesContent": ["/*\nLanguage: Monkey\nDescription: Monkey2 is an easy to use, cross platform, games oriented programming language from Blitz Research.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://blitzresearch.itch.io/monkey2\n*/\n\nfunction monkey(hljs) {\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      { begin: '[$][a-fA-F0-9]+' },\n      hljs.NUMBER_MODE\n    ]\n  };\n  const FUNC_DEFINITION = {\n    variants: [\n      { match: [\n        /(function|method)/,\n        /\\s+/,\n        hljs.UNDERSCORE_IDENT_RE,\n      ] },\n    ],\n    scope: {\n      1: \"keyword\",\n      3: \"title.function\"\n    }\n  };\n  const CLASS_DEFINITION = {\n    variants: [\n      { match: [\n        /(class|interface|extends|implements)/,\n        /\\s+/,\n        hljs.UNDERSCORE_IDENT_RE,\n      ] },\n    ],\n    scope: {\n      1: \"keyword\",\n      3: \"title.class\"\n    }\n  };\n  const BUILT_INS = [\n    \"DebugLog\",\n    \"DebugStop\",\n    \"Error\",\n    \"Print\",\n    \"ACos\",\n    \"ACosr\",\n    \"ASin\",\n    \"ASinr\",\n    \"ATan\",\n    \"ATan2\",\n    \"ATan2r\",\n    \"ATanr\",\n    \"Abs\",\n    \"Abs\",\n    \"<PERSON>il\",\n    \"Clamp\",\n    \"Clamp\",\n    \"Cos\",\n    \"Cosr\",\n    \"Exp\",\n    \"Floor\",\n    \"Log\",\n    \"Max\",\n    \"Max\",\n    \"Min\",\n    \"Min\",\n    \"Pow\",\n    \"Sgn\",\n    \"Sgn\",\n    \"Sin\",\n    \"Sinr\",\n    \"Sqrt\",\n    \"Tan\",\n    \"Tanr\",\n    \"Seed\",\n    \"PI\",\n    \"HALFPI\",\n    \"TWOPI\"\n  ];\n  const LITERALS = [\n    \"true\",\n    \"false\",\n    \"null\"\n  ];\n  const KEYWORDS = [\n    \"public\",\n    \"private\",\n    \"property\",\n    \"continue\",\n    \"exit\",\n    \"extern\",\n    \"new\",\n    \"try\",\n    \"catch\",\n    \"eachin\",\n    \"not\",\n    \"abstract\",\n    \"final\",\n    \"select\",\n    \"case\",\n    \"default\",\n    \"const\",\n    \"local\",\n    \"global\",\n    \"field\",\n    \"end\",\n    \"if\",\n    \"then\",\n    \"else\",\n    \"elseif\",\n    \"endif\",\n    \"while\",\n    \"wend\",\n    \"repeat\",\n    \"until\",\n    \"forever\",\n    \"for\",\n    \"to\",\n    \"step\",\n    \"next\",\n    \"return\",\n    \"module\",\n    \"inline\",\n    \"throw\",\n    \"import\",\n    // not positive, but these are not literals\n    \"and\",\n    \"or\",\n    \"shl\",\n    \"shr\",\n    \"mod\"\n  ];\n\n  return {\n    name: 'Monkey',\n    case_insensitive: true,\n    keywords: {\n      keyword: KEYWORDS,\n      built_in: BUILT_INS,\n      literal: LITERALS\n    },\n    illegal: /\\/\\*/,\n    contains: [\n      hljs.COMMENT('#rem', '#end'),\n      hljs.COMMENT(\n        \"'\",\n        '$',\n        { relevance: 0 }\n      ),\n      FUNC_DEFINITION,\n      CLASS_DEFINITION,\n      {\n        className: 'variable.language',\n        begin: /\\b(self|super)\\b/\n      },\n      {\n        className: 'meta',\n        begin: /\\s*#/,\n        end: '$',\n        keywords: { keyword: 'if else elseif endif end then' }\n      },\n      {\n        match: [\n          /^\\s*/,\n          /strict\\b/\n        ],\n        scope: { 2: \"meta\" }\n      },\n      {\n        beginKeywords: 'alias',\n        end: '=',\n        contains: [ hljs.UNDERSCORE_TITLE_MODE ]\n      },\n      hljs.QUOTE_STRING_MODE,\n      NUMBER\n    ]\n  };\n}\n\nmodule.exports = monkey;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,MAAMC,MAAM,GAAG;IACbC,SAAS,EAAE,QAAQ;IACnBC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CACR;MAAEC,KAAK,EAAE;IAAkB,CAAC,EAC5BL,IAAI,CAACM,WAAW;EAEpB,CAAC;EACD,MAAMC,eAAe,GAAG;IACtBH,QAAQ,EAAE,CACR;MAAEI,KAAK,EAAE,CACP,mBAAmB,EACnB,KAAK,EACLR,IAAI,CAACS,mBAAmB;IACxB,CAAC,CACJ;IACDC,KAAK,EAAE;MACL,CAAC,EAAE,SAAS;MACZ,CAAC,EAAE;IACL;EACF,CAAC;EACD,MAAMC,gBAAgB,GAAG;IACvBP,QAAQ,EAAE,CACR;MAAEI,KAAK,EAAE,CACP,sCAAsC,EACtC,KAAK,EACLR,IAAI,CAACS,mBAAmB;IACxB,CAAC,CACJ;IACDC,KAAK,EAAE;MACL,CAAC,EAAE,SAAS;MACZ,CAAC,EAAE;IACL;EACF,CAAC;EACD,MAAME,SAAS,GAAG,CAChB,UAAU,EACV,WAAW,EACX,OAAO,EACP,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,QAAQ,EACR,OAAO,EACP,KAAK,EACL,KAAK,EACL,MAAM,EACN,OAAO,EACP,OAAO,EACP,KAAK,EACL,MAAM,EACN,KAAK,EACL,OAAO,EACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,IAAI,EACJ,QAAQ,EACR,OAAO,CACR;EACD,MAAMC,QAAQ,GAAG,CACf,MAAM,EACN,OAAO,EACP,MAAM,CACP;EACD,MAAMC,QAAQ,GAAG,CACf,QAAQ,EACR,SAAS,EACT,UAAU,EACV,UAAU,EACV,MAAM,EACN,QAAQ,EACR,KAAK,EACL,KAAK,EACL,OAAO,EACP,QAAQ,EACR,KAAK,EACL,UAAU,EACV,OAAO,EACP,QAAQ,EACR,MAAM,EACN,SAAS,EACT,OAAO,EACP,OAAO,EACP,QAAQ,EACR,OAAO,EACP,KAAK,EACL,IAAI,EACJ,MAAM,EACN,MAAM,EACN,QAAQ,EACR,OAAO,EACP,OAAO,EACP,MAAM,EACN,QAAQ,EACR,OAAO,EACP,SAAS,EACT,KAAK,EACL,IAAI,EACJ,MAAM,EACN,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,QAAQ;EACR;EACA,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAED,OAAO;IACLC,IAAI,EAAE,QAAQ;IACdC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE;MACRC,OAAO,EAAEJ,QAAQ;MACjBK,QAAQ,EAAEP,SAAS;MACnBQ,OAAO,EAAEP;IACX,CAAC;IACDQ,OAAO,EAAE,MAAM;IACfC,QAAQ,EAAE,CACRtB,IAAI,CAACuB,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,EAC5BvB,IAAI,CAACuB,OAAO,CACV,GAAG,EACH,GAAG,EACH;MAAEpB,SAAS,EAAE;IAAE,CACjB,CAAC,EACDI,eAAe,EACfI,gBAAgB,EAChB;MACET,SAAS,EAAE,mBAAmB;MAC9BG,KAAK,EAAE;IACT,CAAC,EACD;MACEH,SAAS,EAAE,MAAM;MACjBG,KAAK,EAAE,MAAM;MACbmB,GAAG,EAAE,GAAG;MACRP,QAAQ,EAAE;QAAEC,OAAO,EAAE;MAAgC;IACvD,CAAC,EACD;MACEV,KAAK,EAAE,CACL,MAAM,EACN,UAAU,CACX;MACDE,KAAK,EAAE;QAAE,CAAC,EAAE;MAAO;IACrB,CAAC,EACD;MACEe,aAAa,EAAE,OAAO;MACtBD,GAAG,EAAE,GAAG;MACRF,QAAQ,EAAE,CAAEtB,IAAI,CAAC0B,qBAAqB;IACxC,CAAC,EACD1B,IAAI,CAAC2B,iBAAiB,EACtB1B,MAAM;EAEV,CAAC;AACH;AAEA2B,MAAM,CAACC,OAAO,GAAG9B,MAAM"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}