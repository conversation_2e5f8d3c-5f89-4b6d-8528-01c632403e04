{"ast": null, "code": "import { UsersTableComponent } from './users-table/users-table.component';\nexport const UsersRoutes = [{\n  path: 'users-table',\n  component: UsersTableComponent,\n  data: {\n    title: 'Table',\n    breadcrumb: 'Table'\n  }\n}];", "map": {"version": 3, "names": ["UsersTableComponent", "UsersRoutes", "path", "component", "data", "title", "breadcrumb"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\views\\users\\users.routing.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\r\nimport { UsersTableComponent } from './users-table/users-table.component';\r\n\r\nexport const UsersRoutes: Routes = [\r\n  { \r\n    path: 'users-table', \r\n    component: UsersTableComponent, \r\n    data: { title: 'Table', breadcrumb: 'Table' } \r\n  }\r\n];"], "mappings": "AACA,SAASA,mBAAmB,QAAQ,qCAAqC;AAEzE,OAAO,MAAMC,WAAW,GAAW,CACjC;EACEC,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEH,mBAAmB;EAC9BI,IAAI,EAAE;IAAEC,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE;EAAO;CAC5C,CACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}