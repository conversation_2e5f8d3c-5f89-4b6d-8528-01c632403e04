{"ast": null, "code": "const MODES = hljs => {\n  return {\n    IMPORTANT: {\n      scope: 'meta',\n      begin: '!important'\n    },\n    BLOCK_COMMENT: hljs.C_BLOCK_COMMENT_MODE,\n    HEXCOLOR: {\n      scope: 'number',\n      begin: /#(([0-9a-fA-F]{3,4})|(([0-9a-fA-F]{2}){3,4}))\\b/\n    },\n    FUNCTION_DISPATCH: {\n      className: \"built_in\",\n      begin: /[\\w-]+(?=\\()/\n    },\n    ATTRIBUTE_SELECTOR_MODE: {\n      scope: 'selector-attr',\n      begin: /\\[/,\n      end: /\\]/,\n      illegal: '$',\n      contains: [hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE]\n    },\n    CSS_NUMBER_MODE: {\n      scope: 'number',\n      begin: hljs.NUMBER_RE + '(' + '%|em|ex|ch|rem' + '|vw|vh|vmin|vmax' + '|cm|mm|in|pt|pc|px' + '|deg|grad|rad|turn' + '|s|ms' + '|Hz|kHz' + '|dpi|dpcm|dppx' + ')?',\n      relevance: 0\n    },\n    CSS_VARIABLE: {\n      className: \"attr\",\n      begin: /--[A-Za-z][A-Za-z0-9_-]*/\n    }\n  };\n};\nconst TAGS = ['a', 'abbr', 'address', 'article', 'aside', 'audio', 'b', 'blockquote', 'body', 'button', 'canvas', 'caption', 'cite', 'code', 'dd', 'del', 'details', 'dfn', 'div', 'dl', 'dt', 'em', 'fieldset', 'figcaption', 'figure', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'header', 'hgroup', 'html', 'i', 'iframe', 'img', 'input', 'ins', 'kbd', 'label', 'legend', 'li', 'main', 'mark', 'menu', 'nav', 'object', 'ol', 'p', 'q', 'quote', 'samp', 'section', 'span', 'strong', 'summary', 'sup', 'table', 'tbody', 'td', 'textarea', 'tfoot', 'th', 'thead', 'time', 'tr', 'ul', 'var', 'video'];\nconst MEDIA_FEATURES = ['any-hover', 'any-pointer', 'aspect-ratio', 'color', 'color-gamut', 'color-index', 'device-aspect-ratio', 'device-height', 'device-width', 'display-mode', 'forced-colors', 'grid', 'height', 'hover', 'inverted-colors', 'monochrome', 'orientation', 'overflow-block', 'overflow-inline', 'pointer', 'prefers-color-scheme', 'prefers-contrast', 'prefers-reduced-motion', 'prefers-reduced-transparency', 'resolution', 'scan', 'scripting', 'update', 'width',\n// TODO: find a better solution?\n'min-width', 'max-width', 'min-height', 'max-height'];\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-classes\nconst PSEUDO_CLASSES = ['active', 'any-link', 'blank', 'checked', 'current', 'default', 'defined', 'dir',\n// dir()\n'disabled', 'drop', 'empty', 'enabled', 'first', 'first-child', 'first-of-type', 'fullscreen', 'future', 'focus', 'focus-visible', 'focus-within', 'has',\n// has()\n'host',\n// host or host()\n'host-context',\n// host-context()\n'hover', 'indeterminate', 'in-range', 'invalid', 'is',\n// is()\n'lang',\n// lang()\n'last-child', 'last-of-type', 'left', 'link', 'local-link', 'not',\n// not()\n'nth-child',\n// nth-child()\n'nth-col',\n// nth-col()\n'nth-last-child',\n// nth-last-child()\n'nth-last-col',\n// nth-last-col()\n'nth-last-of-type',\n//nth-last-of-type()\n'nth-of-type',\n//nth-of-type()\n'only-child', 'only-of-type', 'optional', 'out-of-range', 'past', 'placeholder-shown', 'read-only', 'read-write', 'required', 'right', 'root', 'scope', 'target', 'target-within', 'user-invalid', 'valid', 'visited', 'where' // where()\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-elements\nconst PSEUDO_ELEMENTS = ['after', 'backdrop', 'before', 'cue', 'cue-region', 'first-letter', 'first-line', 'grammar-error', 'marker', 'part', 'placeholder', 'selection', 'slotted', 'spelling-error'];\nconst ATTRIBUTES = ['align-content', 'align-items', 'align-self', 'all', 'animation', 'animation-delay', 'animation-direction', 'animation-duration', 'animation-fill-mode', 'animation-iteration-count', 'animation-name', 'animation-play-state', 'animation-timing-function', 'backface-visibility', 'background', 'background-attachment', 'background-blend-mode', 'background-clip', 'background-color', 'background-image', 'background-origin', 'background-position', 'background-repeat', 'background-size', 'block-size', 'border', 'border-block', 'border-block-color', 'border-block-end', 'border-block-end-color', 'border-block-end-style', 'border-block-end-width', 'border-block-start', 'border-block-start-color', 'border-block-start-style', 'border-block-start-width', 'border-block-style', 'border-block-width', 'border-bottom', 'border-bottom-color', 'border-bottom-left-radius', 'border-bottom-right-radius', 'border-bottom-style', 'border-bottom-width', 'border-collapse', 'border-color', 'border-image', 'border-image-outset', 'border-image-repeat', 'border-image-slice', 'border-image-source', 'border-image-width', 'border-inline', 'border-inline-color', 'border-inline-end', 'border-inline-end-color', 'border-inline-end-style', 'border-inline-end-width', 'border-inline-start', 'border-inline-start-color', 'border-inline-start-style', 'border-inline-start-width', 'border-inline-style', 'border-inline-width', 'border-left', 'border-left-color', 'border-left-style', 'border-left-width', 'border-radius', 'border-right', 'border-right-color', 'border-right-style', 'border-right-width', 'border-spacing', 'border-style', 'border-top', 'border-top-color', 'border-top-left-radius', 'border-top-right-radius', 'border-top-style', 'border-top-width', 'border-width', 'bottom', 'box-decoration-break', 'box-shadow', 'box-sizing', 'break-after', 'break-before', 'break-inside', 'caption-side', 'caret-color', 'clear', 'clip', 'clip-path', 'clip-rule', 'color', 'column-count', 'column-fill', 'column-gap', 'column-rule', 'column-rule-color', 'column-rule-style', 'column-rule-width', 'column-span', 'column-width', 'columns', 'contain', 'content', 'content-visibility', 'counter-increment', 'counter-reset', 'cue', 'cue-after', 'cue-before', 'cursor', 'direction', 'display', 'empty-cells', 'filter', 'flex', 'flex-basis', 'flex-direction', 'flex-flow', 'flex-grow', 'flex-shrink', 'flex-wrap', 'float', 'flow', 'font', 'font-display', 'font-family', 'font-feature-settings', 'font-kerning', 'font-language-override', 'font-size', 'font-size-adjust', 'font-smoothing', 'font-stretch', 'font-style', 'font-synthesis', 'font-variant', 'font-variant-caps', 'font-variant-east-asian', 'font-variant-ligatures', 'font-variant-numeric', 'font-variant-position', 'font-variation-settings', 'font-weight', 'gap', 'glyph-orientation-vertical', 'grid', 'grid-area', 'grid-auto-columns', 'grid-auto-flow', 'grid-auto-rows', 'grid-column', 'grid-column-end', 'grid-column-start', 'grid-gap', 'grid-row', 'grid-row-end', 'grid-row-start', 'grid-template', 'grid-template-areas', 'grid-template-columns', 'grid-template-rows', 'hanging-punctuation', 'height', 'hyphens', 'icon', 'image-orientation', 'image-rendering', 'image-resolution', 'ime-mode', 'inline-size', 'isolation', 'justify-content', 'left', 'letter-spacing', 'line-break', 'line-height', 'list-style', 'list-style-image', 'list-style-position', 'list-style-type', 'margin', 'margin-block', 'margin-block-end', 'margin-block-start', 'margin-bottom', 'margin-inline', 'margin-inline-end', 'margin-inline-start', 'margin-left', 'margin-right', 'margin-top', 'marks', 'mask', 'mask-border', 'mask-border-mode', 'mask-border-outset', 'mask-border-repeat', 'mask-border-slice', 'mask-border-source', 'mask-border-width', 'mask-clip', 'mask-composite', 'mask-image', 'mask-mode', 'mask-origin', 'mask-position', 'mask-repeat', 'mask-size', 'mask-type', 'max-block-size', 'max-height', 'max-inline-size', 'max-width', 'min-block-size', 'min-height', 'min-inline-size', 'min-width', 'mix-blend-mode', 'nav-down', 'nav-index', 'nav-left', 'nav-right', 'nav-up', 'none', 'normal', 'object-fit', 'object-position', 'opacity', 'order', 'orphans', 'outline', 'outline-color', 'outline-offset', 'outline-style', 'outline-width', 'overflow', 'overflow-wrap', 'overflow-x', 'overflow-y', 'padding', 'padding-block', 'padding-block-end', 'padding-block-start', 'padding-bottom', 'padding-inline', 'padding-inline-end', 'padding-inline-start', 'padding-left', 'padding-right', 'padding-top', 'page-break-after', 'page-break-before', 'page-break-inside', 'pause', 'pause-after', 'pause-before', 'perspective', 'perspective-origin', 'pointer-events', 'position', 'quotes', 'resize', 'rest', 'rest-after', 'rest-before', 'right', 'row-gap', 'scroll-margin', 'scroll-margin-block', 'scroll-margin-block-end', 'scroll-margin-block-start', 'scroll-margin-bottom', 'scroll-margin-inline', 'scroll-margin-inline-end', 'scroll-margin-inline-start', 'scroll-margin-left', 'scroll-margin-right', 'scroll-margin-top', 'scroll-padding', 'scroll-padding-block', 'scroll-padding-block-end', 'scroll-padding-block-start', 'scroll-padding-bottom', 'scroll-padding-inline', 'scroll-padding-inline-end', 'scroll-padding-inline-start', 'scroll-padding-left', 'scroll-padding-right', 'scroll-padding-top', 'scroll-snap-align', 'scroll-snap-stop', 'scroll-snap-type', 'scrollbar-color', 'scrollbar-gutter', 'scrollbar-width', 'shape-image-threshold', 'shape-margin', 'shape-outside', 'speak', 'speak-as', 'src',\n// @font-face\n'tab-size', 'table-layout', 'text-align', 'text-align-all', 'text-align-last', 'text-combine-upright', 'text-decoration', 'text-decoration-color', 'text-decoration-line', 'text-decoration-style', 'text-emphasis', 'text-emphasis-color', 'text-emphasis-position', 'text-emphasis-style', 'text-indent', 'text-justify', 'text-orientation', 'text-overflow', 'text-rendering', 'text-shadow', 'text-transform', 'text-underline-position', 'top', 'transform', 'transform-box', 'transform-origin', 'transform-style', 'transition', 'transition-delay', 'transition-duration', 'transition-property', 'transition-timing-function', 'unicode-bidi', 'vertical-align', 'visibility', 'voice-balance', 'voice-duration', 'voice-family', 'voice-pitch', 'voice-range', 'voice-rate', 'voice-stress', 'voice-volume', 'white-space', 'widows', 'width', 'will-change', 'word-break', 'word-spacing', 'word-wrap', 'writing-mode', 'z-index'\n// reverse makes sure longer attributes `font-weight` are matched fully\n// instead of getting false positives on say `font`\n].reverse();\n\n// some grammars use them all as a single group\nconst PSEUDO_SELECTORS = PSEUDO_CLASSES.concat(PSEUDO_ELEMENTS);\n\n/*\nLanguage: Less\nDescription: It's CSS, with just a little more.\nAuthor:   Max Mikhailov <<EMAIL>>\nWebsite: http://lesscss.org\nCategory: common, css, web\n*/\n\n/** @type LanguageFn */\nfunction less(hljs) {\n  const modes = MODES(hljs);\n  const PSEUDO_SELECTORS$1 = PSEUDO_SELECTORS;\n  const AT_MODIFIERS = \"and or not only\";\n  const IDENT_RE = '[\\\\w-]+'; // yes, Less identifiers may begin with a digit\n  const INTERP_IDENT_RE = '(' + IDENT_RE + '|@\\\\{' + IDENT_RE + '\\\\})';\n\n  /* Generic Modes */\n\n  const RULES = [];\n  const VALUE_MODES = []; // forward def. for recursive modes\n\n  const STRING_MODE = function (c) {\n    return {\n      // Less strings are not multiline (also include '~' for more consistent coloring of \"escaped\" strings)\n      className: 'string',\n      begin: '~?' + c + '.*?' + c\n    };\n  };\n  const IDENT_MODE = function (name, begin, relevance) {\n    return {\n      className: name,\n      begin: begin,\n      relevance: relevance\n    };\n  };\n  const AT_KEYWORDS = {\n    $pattern: /[a-z-]+/,\n    keyword: AT_MODIFIERS,\n    attribute: MEDIA_FEATURES.join(\" \")\n  };\n  const PARENS_MODE = {\n    // used only to properly balance nested parens inside mixin call, def. arg list\n    begin: '\\\\(',\n    end: '\\\\)',\n    contains: VALUE_MODES,\n    keywords: AT_KEYWORDS,\n    relevance: 0\n  };\n\n  // generic Less highlighter (used almost everywhere except selectors):\n  VALUE_MODES.push(hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, STRING_MODE(\"'\"), STRING_MODE('\"'), modes.CSS_NUMBER_MODE,\n  // fixme: it does not include dot for numbers like .5em :(\n  {\n    begin: '(url|data-uri)\\\\(',\n    starts: {\n      className: 'string',\n      end: '[\\\\)\\\\n]',\n      excludeEnd: true\n    }\n  }, modes.HEXCOLOR, PARENS_MODE, IDENT_MODE('variable', '@@?' + IDENT_RE, 10), IDENT_MODE('variable', '@\\\\{' + IDENT_RE + '\\\\}'), IDENT_MODE('built_in', '~?`[^`]*?`'),\n  // inline javascript (or whatever host language) *multiline* string\n  {\n    // @media features (it’s here to not duplicate things in AT_RULE_MODE with extra PARENS_MODE overriding):\n    className: 'attribute',\n    begin: IDENT_RE + '\\\\s*:',\n    end: ':',\n    returnBegin: true,\n    excludeEnd: true\n  }, modes.IMPORTANT, {\n    beginKeywords: 'and not'\n  }, modes.FUNCTION_DISPATCH);\n  const VALUE_WITH_RULESETS = VALUE_MODES.concat({\n    begin: /\\{/,\n    end: /\\}/,\n    contains: RULES\n  });\n  const MIXIN_GUARD_MODE = {\n    beginKeywords: 'when',\n    endsWithParent: true,\n    contains: [{\n      beginKeywords: 'and not'\n    }].concat(VALUE_MODES) // using this form to override VALUE’s 'function' match\n  };\n\n  /* Rule-Level Modes */\n\n  const RULE_MODE = {\n    begin: INTERP_IDENT_RE + '\\\\s*:',\n    returnBegin: true,\n    end: /[;}]/,\n    relevance: 0,\n    contains: [{\n      begin: /-(webkit|moz|ms|o)-/\n    }, modes.CSS_VARIABLE, {\n      className: 'attribute',\n      begin: '\\\\b(' + ATTRIBUTES.join('|') + ')\\\\b',\n      end: /(?=:)/,\n      starts: {\n        endsWithParent: true,\n        illegal: '[<=$]',\n        relevance: 0,\n        contains: VALUE_MODES\n      }\n    }]\n  };\n  const AT_RULE_MODE = {\n    className: 'keyword',\n    begin: '@(import|media|charset|font-face|(-[a-z]+-)?keyframes|supports|document|namespace|page|viewport|host)\\\\b',\n    starts: {\n      end: '[;{}]',\n      keywords: AT_KEYWORDS,\n      returnEnd: true,\n      contains: VALUE_MODES,\n      relevance: 0\n    }\n  };\n\n  // variable definitions and calls\n  const VAR_RULE_MODE = {\n    className: 'variable',\n    variants: [\n    // using more strict pattern for higher relevance to increase chances of Less detection.\n    // this is *the only* Less specific statement used in most of the sources, so...\n    // (we’ll still often loose to the css-parser unless there's '//' comment,\n    // simply because 1 variable just can't beat 99 properties :)\n    {\n      begin: '@' + IDENT_RE + '\\\\s*:',\n      relevance: 15\n    }, {\n      begin: '@' + IDENT_RE\n    }],\n    starts: {\n      end: '[;}]',\n      returnEnd: true,\n      contains: VALUE_WITH_RULESETS\n    }\n  };\n  const SELECTOR_MODE = {\n    // first parse unambiguous selectors (i.e. those not starting with tag)\n    // then fall into the scary lookahead-discriminator variant.\n    // this mode also handles mixin definitions and calls\n    variants: [{\n      begin: '[\\\\.#:&\\\\[>]',\n      end: '[;{}]' // mixin calls end with ';'\n    }, {\n      begin: INTERP_IDENT_RE,\n      end: /\\{/\n    }],\n    returnBegin: true,\n    returnEnd: true,\n    illegal: '[<=\\'$\"]',\n    relevance: 0,\n    contains: [hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, MIXIN_GUARD_MODE, IDENT_MODE('keyword', 'all\\\\b'), IDENT_MODE('variable', '@\\\\{' + IDENT_RE + '\\\\}'),\n    // otherwise it’s identified as tag\n\n    {\n      begin: '\\\\b(' + TAGS.join('|') + ')\\\\b',\n      className: 'selector-tag'\n    }, modes.CSS_NUMBER_MODE, IDENT_MODE('selector-tag', INTERP_IDENT_RE, 0), IDENT_MODE('selector-id', '#' + INTERP_IDENT_RE), IDENT_MODE('selector-class', '\\\\.' + INTERP_IDENT_RE, 0), IDENT_MODE('selector-tag', '&', 0), modes.ATTRIBUTE_SELECTOR_MODE, {\n      className: 'selector-pseudo',\n      begin: ':(' + PSEUDO_CLASSES.join('|') + ')'\n    }, {\n      className: 'selector-pseudo',\n      begin: ':(:)?(' + PSEUDO_ELEMENTS.join('|') + ')'\n    }, {\n      begin: /\\(/,\n      end: /\\)/,\n      relevance: 0,\n      contains: VALUE_WITH_RULESETS\n    },\n    // argument list of parametric mixins\n    {\n      begin: '!important'\n    },\n    // eat !important after mixin call or it will be colored as tag\n    modes.FUNCTION_DISPATCH]\n  };\n  const PSEUDO_SELECTOR_MODE = {\n    begin: IDENT_RE + ':(:)?' + `(${PSEUDO_SELECTORS$1.join('|')})`,\n    returnBegin: true,\n    contains: [SELECTOR_MODE]\n  };\n  RULES.push(hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, AT_RULE_MODE, VAR_RULE_MODE, PSEUDO_SELECTOR_MODE, RULE_MODE, SELECTOR_MODE, MIXIN_GUARD_MODE, modes.FUNCTION_DISPATCH);\n  return {\n    name: 'Less',\n    case_insensitive: true,\n    illegal: '[=>\\'/<($\"]',\n    contains: RULES\n  };\n}\nmodule.exports = less;", "map": {"version": 3, "names": ["MODES", "hljs", "IMPORTANT", "scope", "begin", "BLOCK_COMMENT", "C_BLOCK_COMMENT_MODE", "HEXCOLOR", "FUNCTION_DISPATCH", "className", "ATTRIBUTE_SELECTOR_MODE", "end", "illegal", "contains", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "CSS_NUMBER_MODE", "NUMBER_RE", "relevance", "CSS_VARIABLE", "TAGS", "MEDIA_FEATURES", "PSEUDO_CLASSES", "PSEUDO_ELEMENTS", "ATTRIBUTES", "reverse", "PSEUDO_SELECTORS", "concat", "less", "modes", "PSEUDO_SELECTORS$1", "AT_MODIFIERS", "IDENT_RE", "INTERP_IDENT_RE", "RULES", "VALUE_MODES", "STRING_MODE", "c", "IDENT_MODE", "name", "AT_KEYWORDS", "$pattern", "keyword", "attribute", "join", "PARENS_MODE", "keywords", "push", "C_LINE_COMMENT_MODE", "starts", "excludeEnd", "returnBegin", "beginKeywords", "VALUE_WITH_RULESETS", "MIXIN_GUARD_MODE", "endsWithParent", "RULE_MODE", "AT_RULE_MODE", "returnEnd", "VAR_RULE_MODE", "variants", "SELECTOR_MODE", "PSEUDO_SELECTOR_MODE", "case_insensitive", "module", "exports"], "sources": ["C:/OneDrive/Srikant/SWIP/ClaimsPro/claimspro200-src/claimspro_ui/node_modules/highlight.js/lib/languages/less.js"], "sourcesContent": ["const MODES = (hljs) => {\n  return {\n    IMPORTANT: {\n      scope: 'meta',\n      begin: '!important'\n    },\n    BLOCK_COMMENT: hljs.C_BLOCK_COMMENT_MODE,\n    HEXCOLOR: {\n      scope: 'number',\n      begin: /#(([0-9a-fA-F]{3,4})|(([0-9a-fA-F]{2}){3,4}))\\b/\n    },\n    FUNCTION_DISPATCH: {\n      className: \"built_in\",\n      begin: /[\\w-]+(?=\\()/\n    },\n    ATTRIBUTE_SELECTOR_MODE: {\n      scope: 'selector-attr',\n      begin: /\\[/,\n      end: /\\]/,\n      illegal: '$',\n      contains: [\n        hljs.APOS_STRING_MODE,\n        hljs.QUOTE_STRING_MODE\n      ]\n    },\n    CSS_NUMBER_MODE: {\n      scope: 'number',\n      begin: hljs.NUMBER_RE + '(' +\n        '%|em|ex|ch|rem' +\n        '|vw|vh|vmin|vmax' +\n        '|cm|mm|in|pt|pc|px' +\n        '|deg|grad|rad|turn' +\n        '|s|ms' +\n        '|Hz|kHz' +\n        '|dpi|dpcm|dppx' +\n        ')?',\n      relevance: 0\n    },\n    CSS_VARIABLE: {\n      className: \"attr\",\n      begin: /--[A-Za-z][A-Za-z0-9_-]*/\n    }\n  };\n};\n\nconst TAGS = [\n  'a',\n  'abbr',\n  'address',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'blockquote',\n  'body',\n  'button',\n  'canvas',\n  'caption',\n  'cite',\n  'code',\n  'dd',\n  'del',\n  'details',\n  'dfn',\n  'div',\n  'dl',\n  'dt',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'header',\n  'hgroup',\n  'html',\n  'i',\n  'iframe',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'mark',\n  'menu',\n  'nav',\n  'object',\n  'ol',\n  'p',\n  'q',\n  'quote',\n  'samp',\n  'section',\n  'span',\n  'strong',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'ul',\n  'var',\n  'video'\n];\n\nconst MEDIA_FEATURES = [\n  'any-hover',\n  'any-pointer',\n  'aspect-ratio',\n  'color',\n  'color-gamut',\n  'color-index',\n  'device-aspect-ratio',\n  'device-height',\n  'device-width',\n  'display-mode',\n  'forced-colors',\n  'grid',\n  'height',\n  'hover',\n  'inverted-colors',\n  'monochrome',\n  'orientation',\n  'overflow-block',\n  'overflow-inline',\n  'pointer',\n  'prefers-color-scheme',\n  'prefers-contrast',\n  'prefers-reduced-motion',\n  'prefers-reduced-transparency',\n  'resolution',\n  'scan',\n  'scripting',\n  'update',\n  'width',\n  // TODO: find a better solution?\n  'min-width',\n  'max-width',\n  'min-height',\n  'max-height'\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-classes\nconst PSEUDO_CLASSES = [\n  'active',\n  'any-link',\n  'blank',\n  'checked',\n  'current',\n  'default',\n  'defined',\n  'dir', // dir()\n  'disabled',\n  'drop',\n  'empty',\n  'enabled',\n  'first',\n  'first-child',\n  'first-of-type',\n  'fullscreen',\n  'future',\n  'focus',\n  'focus-visible',\n  'focus-within',\n  'has', // has()\n  'host', // host or host()\n  'host-context', // host-context()\n  'hover',\n  'indeterminate',\n  'in-range',\n  'invalid',\n  'is', // is()\n  'lang', // lang()\n  'last-child',\n  'last-of-type',\n  'left',\n  'link',\n  'local-link',\n  'not', // not()\n  'nth-child', // nth-child()\n  'nth-col', // nth-col()\n  'nth-last-child', // nth-last-child()\n  'nth-last-col', // nth-last-col()\n  'nth-last-of-type', //nth-last-of-type()\n  'nth-of-type', //nth-of-type()\n  'only-child',\n  'only-of-type',\n  'optional',\n  'out-of-range',\n  'past',\n  'placeholder-shown',\n  'read-only',\n  'read-write',\n  'required',\n  'right',\n  'root',\n  'scope',\n  'target',\n  'target-within',\n  'user-invalid',\n  'valid',\n  'visited',\n  'where' // where()\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-elements\nconst PSEUDO_ELEMENTS = [\n  'after',\n  'backdrop',\n  'before',\n  'cue',\n  'cue-region',\n  'first-letter',\n  'first-line',\n  'grammar-error',\n  'marker',\n  'part',\n  'placeholder',\n  'selection',\n  'slotted',\n  'spelling-error'\n];\n\nconst ATTRIBUTES = [\n  'align-content',\n  'align-items',\n  'align-self',\n  'all',\n  'animation',\n  'animation-delay',\n  'animation-direction',\n  'animation-duration',\n  'animation-fill-mode',\n  'animation-iteration-count',\n  'animation-name',\n  'animation-play-state',\n  'animation-timing-function',\n  'backface-visibility',\n  'background',\n  'background-attachment',\n  'background-blend-mode',\n  'background-clip',\n  'background-color',\n  'background-image',\n  'background-origin',\n  'background-position',\n  'background-repeat',\n  'background-size',\n  'block-size',\n  'border',\n  'border-block',\n  'border-block-color',\n  'border-block-end',\n  'border-block-end-color',\n  'border-block-end-style',\n  'border-block-end-width',\n  'border-block-start',\n  'border-block-start-color',\n  'border-block-start-style',\n  'border-block-start-width',\n  'border-block-style',\n  'border-block-width',\n  'border-bottom',\n  'border-bottom-color',\n  'border-bottom-left-radius',\n  'border-bottom-right-radius',\n  'border-bottom-style',\n  'border-bottom-width',\n  'border-collapse',\n  'border-color',\n  'border-image',\n  'border-image-outset',\n  'border-image-repeat',\n  'border-image-slice',\n  'border-image-source',\n  'border-image-width',\n  'border-inline',\n  'border-inline-color',\n  'border-inline-end',\n  'border-inline-end-color',\n  'border-inline-end-style',\n  'border-inline-end-width',\n  'border-inline-start',\n  'border-inline-start-color',\n  'border-inline-start-style',\n  'border-inline-start-width',\n  'border-inline-style',\n  'border-inline-width',\n  'border-left',\n  'border-left-color',\n  'border-left-style',\n  'border-left-width',\n  'border-radius',\n  'border-right',\n  'border-right-color',\n  'border-right-style',\n  'border-right-width',\n  'border-spacing',\n  'border-style',\n  'border-top',\n  'border-top-color',\n  'border-top-left-radius',\n  'border-top-right-radius',\n  'border-top-style',\n  'border-top-width',\n  'border-width',\n  'bottom',\n  'box-decoration-break',\n  'box-shadow',\n  'box-sizing',\n  'break-after',\n  'break-before',\n  'break-inside',\n  'caption-side',\n  'caret-color',\n  'clear',\n  'clip',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'column-count',\n  'column-fill',\n  'column-gap',\n  'column-rule',\n  'column-rule-color',\n  'column-rule-style',\n  'column-rule-width',\n  'column-span',\n  'column-width',\n  'columns',\n  'contain',\n  'content',\n  'content-visibility',\n  'counter-increment',\n  'counter-reset',\n  'cue',\n  'cue-after',\n  'cue-before',\n  'cursor',\n  'direction',\n  'display',\n  'empty-cells',\n  'filter',\n  'flex',\n  'flex-basis',\n  'flex-direction',\n  'flex-flow',\n  'flex-grow',\n  'flex-shrink',\n  'flex-wrap',\n  'float',\n  'flow',\n  'font',\n  'font-display',\n  'font-family',\n  'font-feature-settings',\n  'font-kerning',\n  'font-language-override',\n  'font-size',\n  'font-size-adjust',\n  'font-smoothing',\n  'font-stretch',\n  'font-style',\n  'font-synthesis',\n  'font-variant',\n  'font-variant-caps',\n  'font-variant-east-asian',\n  'font-variant-ligatures',\n  'font-variant-numeric',\n  'font-variant-position',\n  'font-variation-settings',\n  'font-weight',\n  'gap',\n  'glyph-orientation-vertical',\n  'grid',\n  'grid-area',\n  'grid-auto-columns',\n  'grid-auto-flow',\n  'grid-auto-rows',\n  'grid-column',\n  'grid-column-end',\n  'grid-column-start',\n  'grid-gap',\n  'grid-row',\n  'grid-row-end',\n  'grid-row-start',\n  'grid-template',\n  'grid-template-areas',\n  'grid-template-columns',\n  'grid-template-rows',\n  'hanging-punctuation',\n  'height',\n  'hyphens',\n  'icon',\n  'image-orientation',\n  'image-rendering',\n  'image-resolution',\n  'ime-mode',\n  'inline-size',\n  'isolation',\n  'justify-content',\n  'left',\n  'letter-spacing',\n  'line-break',\n  'line-height',\n  'list-style',\n  'list-style-image',\n  'list-style-position',\n  'list-style-type',\n  'margin',\n  'margin-block',\n  'margin-block-end',\n  'margin-block-start',\n  'margin-bottom',\n  'margin-inline',\n  'margin-inline-end',\n  'margin-inline-start',\n  'margin-left',\n  'margin-right',\n  'margin-top',\n  'marks',\n  'mask',\n  'mask-border',\n  'mask-border-mode',\n  'mask-border-outset',\n  'mask-border-repeat',\n  'mask-border-slice',\n  'mask-border-source',\n  'mask-border-width',\n  'mask-clip',\n  'mask-composite',\n  'mask-image',\n  'mask-mode',\n  'mask-origin',\n  'mask-position',\n  'mask-repeat',\n  'mask-size',\n  'mask-type',\n  'max-block-size',\n  'max-height',\n  'max-inline-size',\n  'max-width',\n  'min-block-size',\n  'min-height',\n  'min-inline-size',\n  'min-width',\n  'mix-blend-mode',\n  'nav-down',\n  'nav-index',\n  'nav-left',\n  'nav-right',\n  'nav-up',\n  'none',\n  'normal',\n  'object-fit',\n  'object-position',\n  'opacity',\n  'order',\n  'orphans',\n  'outline',\n  'outline-color',\n  'outline-offset',\n  'outline-style',\n  'outline-width',\n  'overflow',\n  'overflow-wrap',\n  'overflow-x',\n  'overflow-y',\n  'padding',\n  'padding-block',\n  'padding-block-end',\n  'padding-block-start',\n  'padding-bottom',\n  'padding-inline',\n  'padding-inline-end',\n  'padding-inline-start',\n  'padding-left',\n  'padding-right',\n  'padding-top',\n  'page-break-after',\n  'page-break-before',\n  'page-break-inside',\n  'pause',\n  'pause-after',\n  'pause-before',\n  'perspective',\n  'perspective-origin',\n  'pointer-events',\n  'position',\n  'quotes',\n  'resize',\n  'rest',\n  'rest-after',\n  'rest-before',\n  'right',\n  'row-gap',\n  'scroll-margin',\n  'scroll-margin-block',\n  'scroll-margin-block-end',\n  'scroll-margin-block-start',\n  'scroll-margin-bottom',\n  'scroll-margin-inline',\n  'scroll-margin-inline-end',\n  'scroll-margin-inline-start',\n  'scroll-margin-left',\n  'scroll-margin-right',\n  'scroll-margin-top',\n  'scroll-padding',\n  'scroll-padding-block',\n  'scroll-padding-block-end',\n  'scroll-padding-block-start',\n  'scroll-padding-bottom',\n  'scroll-padding-inline',\n  'scroll-padding-inline-end',\n  'scroll-padding-inline-start',\n  'scroll-padding-left',\n  'scroll-padding-right',\n  'scroll-padding-top',\n  'scroll-snap-align',\n  'scroll-snap-stop',\n  'scroll-snap-type',\n  'scrollbar-color',\n  'scrollbar-gutter',\n  'scrollbar-width',\n  'shape-image-threshold',\n  'shape-margin',\n  'shape-outside',\n  'speak',\n  'speak-as',\n  'src', // @font-face\n  'tab-size',\n  'table-layout',\n  'text-align',\n  'text-align-all',\n  'text-align-last',\n  'text-combine-upright',\n  'text-decoration',\n  'text-decoration-color',\n  'text-decoration-line',\n  'text-decoration-style',\n  'text-emphasis',\n  'text-emphasis-color',\n  'text-emphasis-position',\n  'text-emphasis-style',\n  'text-indent',\n  'text-justify',\n  'text-orientation',\n  'text-overflow',\n  'text-rendering',\n  'text-shadow',\n  'text-transform',\n  'text-underline-position',\n  'top',\n  'transform',\n  'transform-box',\n  'transform-origin',\n  'transform-style',\n  'transition',\n  'transition-delay',\n  'transition-duration',\n  'transition-property',\n  'transition-timing-function',\n  'unicode-bidi',\n  'vertical-align',\n  'visibility',\n  'voice-balance',\n  'voice-duration',\n  'voice-family',\n  'voice-pitch',\n  'voice-range',\n  'voice-rate',\n  'voice-stress',\n  'voice-volume',\n  'white-space',\n  'widows',\n  'width',\n  'will-change',\n  'word-break',\n  'word-spacing',\n  'word-wrap',\n  'writing-mode',\n  'z-index'\n  // reverse makes sure longer attributes `font-weight` are matched fully\n  // instead of getting false positives on say `font`\n].reverse();\n\n// some grammars use them all as a single group\nconst PSEUDO_SELECTORS = PSEUDO_CLASSES.concat(PSEUDO_ELEMENTS);\n\n/*\nLanguage: Less\nDescription: It's CSS, with just a little more.\nAuthor:   Max Mikhailov <<EMAIL>>\nWebsite: http://lesscss.org\nCategory: common, css, web\n*/\n\n/** @type LanguageFn */\nfunction less(hljs) {\n  const modes = MODES(hljs);\n  const PSEUDO_SELECTORS$1 = PSEUDO_SELECTORS;\n\n  const AT_MODIFIERS = \"and or not only\";\n  const IDENT_RE = '[\\\\w-]+'; // yes, Less identifiers may begin with a digit\n  const INTERP_IDENT_RE = '(' + IDENT_RE + '|@\\\\{' + IDENT_RE + '\\\\})';\n\n  /* Generic Modes */\n\n  const RULES = []; const VALUE_MODES = []; // forward def. for recursive modes\n\n  const STRING_MODE = function(c) {\n    return {\n    // Less strings are not multiline (also include '~' for more consistent coloring of \"escaped\" strings)\n      className: 'string',\n      begin: '~?' + c + '.*?' + c\n    };\n  };\n\n  const IDENT_MODE = function(name, begin, relevance) {\n    return {\n      className: name,\n      begin: begin,\n      relevance: relevance\n    };\n  };\n\n  const AT_KEYWORDS = {\n    $pattern: /[a-z-]+/,\n    keyword: AT_MODIFIERS,\n    attribute: MEDIA_FEATURES.join(\" \")\n  };\n\n  const PARENS_MODE = {\n    // used only to properly balance nested parens inside mixin call, def. arg list\n    begin: '\\\\(',\n    end: '\\\\)',\n    contains: VALUE_MODES,\n    keywords: AT_KEYWORDS,\n    relevance: 0\n  };\n\n  // generic Less highlighter (used almost everywhere except selectors):\n  VALUE_MODES.push(\n    hljs.C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    STRING_MODE(\"'\"),\n    STRING_MODE('\"'),\n    modes.CSS_NUMBER_MODE, // fixme: it does not include dot for numbers like .5em :(\n    {\n      begin: '(url|data-uri)\\\\(',\n      starts: {\n        className: 'string',\n        end: '[\\\\)\\\\n]',\n        excludeEnd: true\n      }\n    },\n    modes.HEXCOLOR,\n    PARENS_MODE,\n    IDENT_MODE('variable', '@@?' + IDENT_RE, 10),\n    IDENT_MODE('variable', '@\\\\{' + IDENT_RE + '\\\\}'),\n    IDENT_MODE('built_in', '~?`[^`]*?`'), // inline javascript (or whatever host language) *multiline* string\n    { // @media features (it’s here to not duplicate things in AT_RULE_MODE with extra PARENS_MODE overriding):\n      className: 'attribute',\n      begin: IDENT_RE + '\\\\s*:',\n      end: ':',\n      returnBegin: true,\n      excludeEnd: true\n    },\n    modes.IMPORTANT,\n    { beginKeywords: 'and not' },\n    modes.FUNCTION_DISPATCH\n  );\n\n  const VALUE_WITH_RULESETS = VALUE_MODES.concat({\n    begin: /\\{/,\n    end: /\\}/,\n    contains: RULES\n  });\n\n  const MIXIN_GUARD_MODE = {\n    beginKeywords: 'when',\n    endsWithParent: true,\n    contains: [ { beginKeywords: 'and not' } ].concat(VALUE_MODES) // using this form to override VALUE’s 'function' match\n  };\n\n  /* Rule-Level Modes */\n\n  const RULE_MODE = {\n    begin: INTERP_IDENT_RE + '\\\\s*:',\n    returnBegin: true,\n    end: /[;}]/,\n    relevance: 0,\n    contains: [\n      { begin: /-(webkit|moz|ms|o)-/ },\n      modes.CSS_VARIABLE,\n      {\n        className: 'attribute',\n        begin: '\\\\b(' + ATTRIBUTES.join('|') + ')\\\\b',\n        end: /(?=:)/,\n        starts: {\n          endsWithParent: true,\n          illegal: '[<=$]',\n          relevance: 0,\n          contains: VALUE_MODES\n        }\n      }\n    ]\n  };\n\n  const AT_RULE_MODE = {\n    className: 'keyword',\n    begin: '@(import|media|charset|font-face|(-[a-z]+-)?keyframes|supports|document|namespace|page|viewport|host)\\\\b',\n    starts: {\n      end: '[;{}]',\n      keywords: AT_KEYWORDS,\n      returnEnd: true,\n      contains: VALUE_MODES,\n      relevance: 0\n    }\n  };\n\n  // variable definitions and calls\n  const VAR_RULE_MODE = {\n    className: 'variable',\n    variants: [\n      // using more strict pattern for higher relevance to increase chances of Less detection.\n      // this is *the only* Less specific statement used in most of the sources, so...\n      // (we’ll still often loose to the css-parser unless there's '//' comment,\n      // simply because 1 variable just can't beat 99 properties :)\n      {\n        begin: '@' + IDENT_RE + '\\\\s*:',\n        relevance: 15\n      },\n      { begin: '@' + IDENT_RE }\n    ],\n    starts: {\n      end: '[;}]',\n      returnEnd: true,\n      contains: VALUE_WITH_RULESETS\n    }\n  };\n\n  const SELECTOR_MODE = {\n    // first parse unambiguous selectors (i.e. those not starting with tag)\n    // then fall into the scary lookahead-discriminator variant.\n    // this mode also handles mixin definitions and calls\n    variants: [\n      {\n        begin: '[\\\\.#:&\\\\[>]',\n        end: '[;{}]' // mixin calls end with ';'\n      },\n      {\n        begin: INTERP_IDENT_RE,\n        end: /\\{/\n      }\n    ],\n    returnBegin: true,\n    returnEnd: true,\n    illegal: '[<=\\'$\"]',\n    relevance: 0,\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      MIXIN_GUARD_MODE,\n      IDENT_MODE('keyword', 'all\\\\b'),\n      IDENT_MODE('variable', '@\\\\{' + IDENT_RE + '\\\\}'), // otherwise it’s identified as tag\n      \n      {\n        begin: '\\\\b(' + TAGS.join('|') + ')\\\\b',\n        className: 'selector-tag'\n      },\n      modes.CSS_NUMBER_MODE,\n      IDENT_MODE('selector-tag', INTERP_IDENT_RE, 0),\n      IDENT_MODE('selector-id', '#' + INTERP_IDENT_RE),\n      IDENT_MODE('selector-class', '\\\\.' + INTERP_IDENT_RE, 0),\n      IDENT_MODE('selector-tag', '&', 0),\n      modes.ATTRIBUTE_SELECTOR_MODE,\n      {\n        className: 'selector-pseudo',\n        begin: ':(' + PSEUDO_CLASSES.join('|') + ')'\n      },\n      {\n        className: 'selector-pseudo',\n        begin: ':(:)?(' + PSEUDO_ELEMENTS.join('|') + ')'\n      },\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        relevance: 0,\n        contains: VALUE_WITH_RULESETS\n      }, // argument list of parametric mixins\n      { begin: '!important' }, // eat !important after mixin call or it will be colored as tag\n      modes.FUNCTION_DISPATCH\n    ]\n  };\n\n  const PSEUDO_SELECTOR_MODE = {\n    begin: IDENT_RE + ':(:)?' + `(${PSEUDO_SELECTORS$1.join('|')})`,\n    returnBegin: true,\n    contains: [ SELECTOR_MODE ]\n  };\n\n  RULES.push(\n    hljs.C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    AT_RULE_MODE,\n    VAR_RULE_MODE,\n    PSEUDO_SELECTOR_MODE,\n    RULE_MODE,\n    SELECTOR_MODE,\n    MIXIN_GUARD_MODE,\n    modes.FUNCTION_DISPATCH\n  );\n\n  return {\n    name: 'Less',\n    case_insensitive: true,\n    illegal: '[=>\\'/<($\"]',\n    contains: RULES\n  };\n}\n\nmodule.exports = less;\n"], "mappings": "AAAA,MAAMA,KAAK,GAAIC,IAAI,IAAK;EACtB,OAAO;IACLC,SAAS,EAAE;MACTC,KAAK,EAAE,MAAM;MACbC,KAAK,EAAE;IACT,CAAC;IACDC,aAAa,EAAEJ,IAAI,CAACK,oBAAoB;IACxCC,QAAQ,EAAE;MACRJ,KAAK,EAAE,QAAQ;MACfC,KAAK,EAAE;IACT,CAAC;IACDI,iBAAiB,EAAE;MACjBC,SAAS,EAAE,UAAU;MACrBL,KAAK,EAAE;IACT,CAAC;IACDM,uBAAuB,EAAE;MACvBP,KAAK,EAAE,eAAe;MACtBC,KAAK,EAAE,IAAI;MACXO,GAAG,EAAE,IAAI;MACTC,OAAO,EAAE,GAAG;MACZC,QAAQ,EAAE,CACRZ,IAAI,CAACa,gBAAgB,EACrBb,IAAI,CAACc,iBAAiB;IAE1B,CAAC;IACDC,eAAe,EAAE;MACfb,KAAK,EAAE,QAAQ;MACfC,KAAK,EAAEH,IAAI,CAACgB,SAAS,GAAG,GAAG,GACzB,gBAAgB,GAChB,kBAAkB,GAClB,oBAAoB,GACpB,oBAAoB,GACpB,OAAO,GACP,SAAS,GACT,gBAAgB,GAChB,IAAI;MACNC,SAAS,EAAE;IACb,CAAC;IACDC,YAAY,EAAE;MACZV,SAAS,EAAE,MAAM;MACjBL,KAAK,EAAE;IACT;EACF,CAAC;AACH,CAAC;AAED,MAAMgB,IAAI,GAAG,CACX,GAAG,EACH,MAAM,EACN,SAAS,EACT,SAAS,EACT,OAAO,EACP,OAAO,EACP,GAAG,EACH,YAAY,EACZ,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,MAAM,EACN,MAAM,EACN,IAAI,EACJ,KAAK,EACL,SAAS,EACT,KAAK,EACL,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,GAAG,EACH,QAAQ,EACR,KAAK,EACL,OAAO,EACP,KAAK,EACL,KAAK,EACL,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,GAAG,EACH,GAAG,EACH,OAAO,EACP,MAAM,EACN,SAAS,EACT,MAAM,EACN,QAAQ,EACR,SAAS,EACT,KAAK,EACL,OAAO,EACP,OAAO,EACP,IAAI,EACJ,UAAU,EACV,OAAO,EACP,IAAI,EACJ,OAAO,EACP,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,OAAO,CACR;AAED,MAAMC,cAAc,GAAG,CACrB,WAAW,EACX,aAAa,EACb,cAAc,EACd,OAAO,EACP,aAAa,EACb,aAAa,EACb,qBAAqB,EACrB,eAAe,EACf,cAAc,EACd,cAAc,EACd,eAAe,EACf,MAAM,EACN,QAAQ,EACR,OAAO,EACP,iBAAiB,EACjB,YAAY,EACZ,aAAa,EACb,gBAAgB,EAChB,iBAAiB,EACjB,SAAS,EACT,sBAAsB,EACtB,kBAAkB,EAClB,wBAAwB,EACxB,8BAA8B,EAC9B,YAAY,EACZ,MAAM,EACN,WAAW,EACX,QAAQ,EACR,OAAO;AACP;AACA,WAAW,EACX,WAAW,EACX,YAAY,EACZ,YAAY,CACb;;AAED;AACA,MAAMC,cAAc,GAAG,CACrB,QAAQ,EACR,UAAU,EACV,OAAO,EACP,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,KAAK;AAAE;AACP,UAAU,EACV,MAAM,EACN,OAAO,EACP,SAAS,EACT,OAAO,EACP,aAAa,EACb,eAAe,EACf,YAAY,EACZ,QAAQ,EACR,OAAO,EACP,eAAe,EACf,cAAc,EACd,KAAK;AAAE;AACP,MAAM;AAAE;AACR,cAAc;AAAE;AAChB,OAAO,EACP,eAAe,EACf,UAAU,EACV,SAAS,EACT,IAAI;AAAE;AACN,MAAM;AAAE;AACR,YAAY,EACZ,cAAc,EACd,MAAM,EACN,MAAM,EACN,YAAY,EACZ,KAAK;AAAE;AACP,WAAW;AAAE;AACb,SAAS;AAAE;AACX,gBAAgB;AAAE;AAClB,cAAc;AAAE;AAChB,kBAAkB;AAAE;AACpB,aAAa;AAAE;AACf,YAAY,EACZ,cAAc,EACd,UAAU,EACV,cAAc,EACd,MAAM,EACN,mBAAmB,EACnB,WAAW,EACX,YAAY,EACZ,UAAU,EACV,OAAO,EACP,MAAM,EACN,OAAO,EACP,QAAQ,EACR,eAAe,EACf,cAAc,EACd,OAAO,EACP,SAAS,EACT,OAAO,CAAC;AAAA,CACT;;AAED;AACA,MAAMC,eAAe,GAAG,CACtB,OAAO,EACP,UAAU,EACV,QAAQ,EACR,KAAK,EACL,YAAY,EACZ,cAAc,EACd,YAAY,EACZ,eAAe,EACf,QAAQ,EACR,MAAM,EACN,aAAa,EACb,WAAW,EACX,SAAS,EACT,gBAAgB,CACjB;AAED,MAAMC,UAAU,GAAG,CACjB,eAAe,EACf,aAAa,EACb,YAAY,EACZ,KAAK,EACL,WAAW,EACX,iBAAiB,EACjB,qBAAqB,EACrB,oBAAoB,EACpB,qBAAqB,EACrB,2BAA2B,EAC3B,gBAAgB,EAChB,sBAAsB,EACtB,2BAA2B,EAC3B,qBAAqB,EACrB,YAAY,EACZ,uBAAuB,EACvB,uBAAuB,EACvB,iBAAiB,EACjB,kBAAkB,EAClB,kBAAkB,EAClB,mBAAmB,EACnB,qBAAqB,EACrB,mBAAmB,EACnB,iBAAiB,EACjB,YAAY,EACZ,QAAQ,EACR,cAAc,EACd,oBAAoB,EACpB,kBAAkB,EAClB,wBAAwB,EACxB,wBAAwB,EACxB,wBAAwB,EACxB,oBAAoB,EACpB,0BAA0B,EAC1B,0BAA0B,EAC1B,0BAA0B,EAC1B,oBAAoB,EACpB,oBAAoB,EACpB,eAAe,EACf,qBAAqB,EACrB,2BAA2B,EAC3B,4BAA4B,EAC5B,qBAAqB,EACrB,qBAAqB,EACrB,iBAAiB,EACjB,cAAc,EACd,cAAc,EACd,qBAAqB,EACrB,qBAAqB,EACrB,oBAAoB,EACpB,qBAAqB,EACrB,oBAAoB,EACpB,eAAe,EACf,qBAAqB,EACrB,mBAAmB,EACnB,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,qBAAqB,EACrB,2BAA2B,EAC3B,2BAA2B,EAC3B,2BAA2B,EAC3B,qBAAqB,EACrB,qBAAqB,EACrB,aAAa,EACb,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,eAAe,EACf,cAAc,EACd,oBAAoB,EACpB,oBAAoB,EACpB,oBAAoB,EACpB,gBAAgB,EAChB,cAAc,EACd,YAAY,EACZ,kBAAkB,EAClB,wBAAwB,EACxB,yBAAyB,EACzB,kBAAkB,EAClB,kBAAkB,EAClB,cAAc,EACd,QAAQ,EACR,sBAAsB,EACtB,YAAY,EACZ,YAAY,EACZ,aAAa,EACb,cAAc,EACd,cAAc,EACd,cAAc,EACd,aAAa,EACb,OAAO,EACP,MAAM,EACN,WAAW,EACX,WAAW,EACX,OAAO,EACP,cAAc,EACd,aAAa,EACb,YAAY,EACZ,aAAa,EACb,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,aAAa,EACb,cAAc,EACd,SAAS,EACT,SAAS,EACT,SAAS,EACT,oBAAoB,EACpB,mBAAmB,EACnB,eAAe,EACf,KAAK,EACL,WAAW,EACX,YAAY,EACZ,QAAQ,EACR,WAAW,EACX,SAAS,EACT,aAAa,EACb,QAAQ,EACR,MAAM,EACN,YAAY,EACZ,gBAAgB,EAChB,WAAW,EACX,WAAW,EACX,aAAa,EACb,WAAW,EACX,OAAO,EACP,MAAM,EACN,MAAM,EACN,cAAc,EACd,aAAa,EACb,uBAAuB,EACvB,cAAc,EACd,wBAAwB,EACxB,WAAW,EACX,kBAAkB,EAClB,gBAAgB,EAChB,cAAc,EACd,YAAY,EACZ,gBAAgB,EAChB,cAAc,EACd,mBAAmB,EACnB,yBAAyB,EACzB,wBAAwB,EACxB,sBAAsB,EACtB,uBAAuB,EACvB,yBAAyB,EACzB,aAAa,EACb,KAAK,EACL,4BAA4B,EAC5B,MAAM,EACN,WAAW,EACX,mBAAmB,EACnB,gBAAgB,EAChB,gBAAgB,EAChB,aAAa,EACb,iBAAiB,EACjB,mBAAmB,EACnB,UAAU,EACV,UAAU,EACV,cAAc,EACd,gBAAgB,EAChB,eAAe,EACf,qBAAqB,EACrB,uBAAuB,EACvB,oBAAoB,EACpB,qBAAqB,EACrB,QAAQ,EACR,SAAS,EACT,MAAM,EACN,mBAAmB,EACnB,iBAAiB,EACjB,kBAAkB,EAClB,UAAU,EACV,aAAa,EACb,WAAW,EACX,iBAAiB,EACjB,MAAM,EACN,gBAAgB,EAChB,YAAY,EACZ,aAAa,EACb,YAAY,EACZ,kBAAkB,EAClB,qBAAqB,EACrB,iBAAiB,EACjB,QAAQ,EACR,cAAc,EACd,kBAAkB,EAClB,oBAAoB,EACpB,eAAe,EACf,eAAe,EACf,mBAAmB,EACnB,qBAAqB,EACrB,aAAa,EACb,cAAc,EACd,YAAY,EACZ,OAAO,EACP,MAAM,EACN,aAAa,EACb,kBAAkB,EAClB,oBAAoB,EACpB,oBAAoB,EACpB,mBAAmB,EACnB,oBAAoB,EACpB,mBAAmB,EACnB,WAAW,EACX,gBAAgB,EAChB,YAAY,EACZ,WAAW,EACX,aAAa,EACb,eAAe,EACf,aAAa,EACb,WAAW,EACX,WAAW,EACX,gBAAgB,EAChB,YAAY,EACZ,iBAAiB,EACjB,WAAW,EACX,gBAAgB,EAChB,YAAY,EACZ,iBAAiB,EACjB,WAAW,EACX,gBAAgB,EAChB,UAAU,EACV,WAAW,EACX,UAAU,EACV,WAAW,EACX,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,YAAY,EACZ,iBAAiB,EACjB,SAAS,EACT,OAAO,EACP,SAAS,EACT,SAAS,EACT,eAAe,EACf,gBAAgB,EAChB,eAAe,EACf,eAAe,EACf,UAAU,EACV,eAAe,EACf,YAAY,EACZ,YAAY,EACZ,SAAS,EACT,eAAe,EACf,mBAAmB,EACnB,qBAAqB,EACrB,gBAAgB,EAChB,gBAAgB,EAChB,oBAAoB,EACpB,sBAAsB,EACtB,cAAc,EACd,eAAe,EACf,aAAa,EACb,kBAAkB,EAClB,mBAAmB,EACnB,mBAAmB,EACnB,OAAO,EACP,aAAa,EACb,cAAc,EACd,aAAa,EACb,oBAAoB,EACpB,gBAAgB,EAChB,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,YAAY,EACZ,aAAa,EACb,OAAO,EACP,SAAS,EACT,eAAe,EACf,qBAAqB,EACrB,yBAAyB,EACzB,2BAA2B,EAC3B,sBAAsB,EACtB,sBAAsB,EACtB,0BAA0B,EAC1B,4BAA4B,EAC5B,oBAAoB,EACpB,qBAAqB,EACrB,mBAAmB,EACnB,gBAAgB,EAChB,sBAAsB,EACtB,0BAA0B,EAC1B,4BAA4B,EAC5B,uBAAuB,EACvB,uBAAuB,EACvB,2BAA2B,EAC3B,6BAA6B,EAC7B,qBAAqB,EACrB,sBAAsB,EACtB,oBAAoB,EACpB,mBAAmB,EACnB,kBAAkB,EAClB,kBAAkB,EAClB,iBAAiB,EACjB,kBAAkB,EAClB,iBAAiB,EACjB,uBAAuB,EACvB,cAAc,EACd,eAAe,EACf,OAAO,EACP,UAAU,EACV,KAAK;AAAE;AACP,UAAU,EACV,cAAc,EACd,YAAY,EACZ,gBAAgB,EAChB,iBAAiB,EACjB,sBAAsB,EACtB,iBAAiB,EACjB,uBAAuB,EACvB,sBAAsB,EACtB,uBAAuB,EACvB,eAAe,EACf,qBAAqB,EACrB,wBAAwB,EACxB,qBAAqB,EACrB,aAAa,EACb,cAAc,EACd,kBAAkB,EAClB,eAAe,EACf,gBAAgB,EAChB,aAAa,EACb,gBAAgB,EAChB,yBAAyB,EACzB,KAAK,EACL,WAAW,EACX,eAAe,EACf,kBAAkB,EAClB,iBAAiB,EACjB,YAAY,EACZ,kBAAkB,EAClB,qBAAqB,EACrB,qBAAqB,EACrB,4BAA4B,EAC5B,cAAc,EACd,gBAAgB,EAChB,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,cAAc,EACd,aAAa,EACb,aAAa,EACb,YAAY,EACZ,cAAc,EACd,cAAc,EACd,aAAa,EACb,QAAQ,EACR,OAAO,EACP,aAAa,EACb,YAAY,EACZ,cAAc,EACd,WAAW,EACX,cAAc,EACd;AACA;AACA;AAAA,CACD,CAACC,OAAO,CAAC,CAAC;;AAEX;AACA,MAAMC,gBAAgB,GAAGJ,cAAc,CAACK,MAAM,CAACJ,eAAe,CAAC;;AAE/D;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASK,IAAIA,CAAC3B,IAAI,EAAE;EAClB,MAAM4B,KAAK,GAAG7B,KAAK,CAACC,IAAI,CAAC;EACzB,MAAM6B,kBAAkB,GAAGJ,gBAAgB;EAE3C,MAAMK,YAAY,GAAG,iBAAiB;EACtC,MAAMC,QAAQ,GAAG,SAAS,CAAC,CAAC;EAC5B,MAAMC,eAAe,GAAG,GAAG,GAAGD,QAAQ,GAAG,OAAO,GAAGA,QAAQ,GAAG,MAAM;;EAEpE;;EAEA,MAAME,KAAK,GAAG,EAAE;EAAE,MAAMC,WAAW,GAAG,EAAE,CAAC,CAAC;;EAE1C,MAAMC,WAAW,GAAG,SAAAA,CAASC,CAAC,EAAE;IAC9B,OAAO;MACP;MACE5B,SAAS,EAAE,QAAQ;MACnBL,KAAK,EAAE,IAAI,GAAGiC,CAAC,GAAG,KAAK,GAAGA;IAC5B,CAAC;EACH,CAAC;EAED,MAAMC,UAAU,GAAG,SAAAA,CAASC,IAAI,EAAEnC,KAAK,EAAEc,SAAS,EAAE;IAClD,OAAO;MACLT,SAAS,EAAE8B,IAAI;MACfnC,KAAK,EAAEA,KAAK;MACZc,SAAS,EAAEA;IACb,CAAC;EACH,CAAC;EAED,MAAMsB,WAAW,GAAG;IAClBC,QAAQ,EAAE,SAAS;IACnBC,OAAO,EAAEX,YAAY;IACrBY,SAAS,EAAEtB,cAAc,CAACuB,IAAI,CAAC,GAAG;EACpC,CAAC;EAED,MAAMC,WAAW,GAAG;IAClB;IACAzC,KAAK,EAAE,KAAK;IACZO,GAAG,EAAE,KAAK;IACVE,QAAQ,EAAEsB,WAAW;IACrBW,QAAQ,EAAEN,WAAW;IACrBtB,SAAS,EAAE;EACb,CAAC;;EAED;EACAiB,WAAW,CAACY,IAAI,CACd9C,IAAI,CAAC+C,mBAAmB,EACxB/C,IAAI,CAACK,oBAAoB,EACzB8B,WAAW,CAAC,GAAG,CAAC,EAChBA,WAAW,CAAC,GAAG,CAAC,EAChBP,KAAK,CAACb,eAAe;EAAE;EACvB;IACEZ,KAAK,EAAE,mBAAmB;IAC1B6C,MAAM,EAAE;MACNxC,SAAS,EAAE,QAAQ;MACnBE,GAAG,EAAE,UAAU;MACfuC,UAAU,EAAE;IACd;EACF,CAAC,EACDrB,KAAK,CAACtB,QAAQ,EACdsC,WAAW,EACXP,UAAU,CAAC,UAAU,EAAE,KAAK,GAAGN,QAAQ,EAAE,EAAE,CAAC,EAC5CM,UAAU,CAAC,UAAU,EAAE,MAAM,GAAGN,QAAQ,GAAG,KAAK,CAAC,EACjDM,UAAU,CAAC,UAAU,EAAE,YAAY,CAAC;EAAE;EACtC;IAAE;IACA7B,SAAS,EAAE,WAAW;IACtBL,KAAK,EAAE4B,QAAQ,GAAG,OAAO;IACzBrB,GAAG,EAAE,GAAG;IACRwC,WAAW,EAAE,IAAI;IACjBD,UAAU,EAAE;EACd,CAAC,EACDrB,KAAK,CAAC3B,SAAS,EACf;IAAEkD,aAAa,EAAE;EAAU,CAAC,EAC5BvB,KAAK,CAACrB,iBACR,CAAC;EAED,MAAM6C,mBAAmB,GAAGlB,WAAW,CAACR,MAAM,CAAC;IAC7CvB,KAAK,EAAE,IAAI;IACXO,GAAG,EAAE,IAAI;IACTE,QAAQ,EAAEqB;EACZ,CAAC,CAAC;EAEF,MAAMoB,gBAAgB,GAAG;IACvBF,aAAa,EAAE,MAAM;IACrBG,cAAc,EAAE,IAAI;IACpB1C,QAAQ,EAAE,CAAE;MAAEuC,aAAa,EAAE;IAAU,CAAC,CAAE,CAACzB,MAAM,CAACQ,WAAW,CAAC,CAAC;EACjE,CAAC;;EAED;;EAEA,MAAMqB,SAAS,GAAG;IAChBpD,KAAK,EAAE6B,eAAe,GAAG,OAAO;IAChCkB,WAAW,EAAE,IAAI;IACjBxC,GAAG,EAAE,MAAM;IACXO,SAAS,EAAE,CAAC;IACZL,QAAQ,EAAE,CACR;MAAET,KAAK,EAAE;IAAsB,CAAC,EAChCyB,KAAK,CAACV,YAAY,EAClB;MACEV,SAAS,EAAE,WAAW;MACtBL,KAAK,EAAE,MAAM,GAAGoB,UAAU,CAACoB,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM;MAC7CjC,GAAG,EAAE,OAAO;MACZsC,MAAM,EAAE;QACNM,cAAc,EAAE,IAAI;QACpB3C,OAAO,EAAE,OAAO;QAChBM,SAAS,EAAE,CAAC;QACZL,QAAQ,EAAEsB;MACZ;IACF,CAAC;EAEL,CAAC;EAED,MAAMsB,YAAY,GAAG;IACnBhD,SAAS,EAAE,SAAS;IACpBL,KAAK,EAAE,0GAA0G;IACjH6C,MAAM,EAAE;MACNtC,GAAG,EAAE,OAAO;MACZmC,QAAQ,EAAEN,WAAW;MACrBkB,SAAS,EAAE,IAAI;MACf7C,QAAQ,EAAEsB,WAAW;MACrBjB,SAAS,EAAE;IACb;EACF,CAAC;;EAED;EACA,MAAMyC,aAAa,GAAG;IACpBlD,SAAS,EAAE,UAAU;IACrBmD,QAAQ,EAAE;IACR;IACA;IACA;IACA;IACA;MACExD,KAAK,EAAE,GAAG,GAAG4B,QAAQ,GAAG,OAAO;MAC/Bd,SAAS,EAAE;IACb,CAAC,EACD;MAAEd,KAAK,EAAE,GAAG,GAAG4B;IAAS,CAAC,CAC1B;IACDiB,MAAM,EAAE;MACNtC,GAAG,EAAE,MAAM;MACX+C,SAAS,EAAE,IAAI;MACf7C,QAAQ,EAAEwC;IACZ;EACF,CAAC;EAED,MAAMQ,aAAa,GAAG;IACpB;IACA;IACA;IACAD,QAAQ,EAAE,CACR;MACExD,KAAK,EAAE,cAAc;MACrBO,GAAG,EAAE,OAAO,CAAC;IACf,CAAC,EACD;MACEP,KAAK,EAAE6B,eAAe;MACtBtB,GAAG,EAAE;IACP,CAAC,CACF;IACDwC,WAAW,EAAE,IAAI;IACjBO,SAAS,EAAE,IAAI;IACf9C,OAAO,EAAE,UAAU;IACnBM,SAAS,EAAE,CAAC;IACZL,QAAQ,EAAE,CACRZ,IAAI,CAAC+C,mBAAmB,EACxB/C,IAAI,CAACK,oBAAoB,EACzBgD,gBAAgB,EAChBhB,UAAU,CAAC,SAAS,EAAE,QAAQ,CAAC,EAC/BA,UAAU,CAAC,UAAU,EAAE,MAAM,GAAGN,QAAQ,GAAG,KAAK,CAAC;IAAE;;IAEnD;MACE5B,KAAK,EAAE,MAAM,GAAGgB,IAAI,CAACwB,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM;MACvCnC,SAAS,EAAE;IACb,CAAC,EACDoB,KAAK,CAACb,eAAe,EACrBsB,UAAU,CAAC,cAAc,EAAEL,eAAe,EAAE,CAAC,CAAC,EAC9CK,UAAU,CAAC,aAAa,EAAE,GAAG,GAAGL,eAAe,CAAC,EAChDK,UAAU,CAAC,gBAAgB,EAAE,KAAK,GAAGL,eAAe,EAAE,CAAC,CAAC,EACxDK,UAAU,CAAC,cAAc,EAAE,GAAG,EAAE,CAAC,CAAC,EAClCT,KAAK,CAACnB,uBAAuB,EAC7B;MACED,SAAS,EAAE,iBAAiB;MAC5BL,KAAK,EAAE,IAAI,GAAGkB,cAAc,CAACsB,IAAI,CAAC,GAAG,CAAC,GAAG;IAC3C,CAAC,EACD;MACEnC,SAAS,EAAE,iBAAiB;MAC5BL,KAAK,EAAE,QAAQ,GAAGmB,eAAe,CAACqB,IAAI,CAAC,GAAG,CAAC,GAAG;IAChD,CAAC,EACD;MACExC,KAAK,EAAE,IAAI;MACXO,GAAG,EAAE,IAAI;MACTO,SAAS,EAAE,CAAC;MACZL,QAAQ,EAAEwC;IACZ,CAAC;IAAE;IACH;MAAEjD,KAAK,EAAE;IAAa,CAAC;IAAE;IACzByB,KAAK,CAACrB,iBAAiB;EAE3B,CAAC;EAED,MAAMsD,oBAAoB,GAAG;IAC3B1D,KAAK,EAAE4B,QAAQ,GAAG,OAAO,GAAI,IAAGF,kBAAkB,CAACc,IAAI,CAAC,GAAG,CAAE,GAAE;IAC/DO,WAAW,EAAE,IAAI;IACjBtC,QAAQ,EAAE,CAAEgD,aAAa;EAC3B,CAAC;EAED3B,KAAK,CAACa,IAAI,CACR9C,IAAI,CAAC+C,mBAAmB,EACxB/C,IAAI,CAACK,oBAAoB,EACzBmD,YAAY,EACZE,aAAa,EACbG,oBAAoB,EACpBN,SAAS,EACTK,aAAa,EACbP,gBAAgB,EAChBzB,KAAK,CAACrB,iBACR,CAAC;EAED,OAAO;IACL+B,IAAI,EAAE,MAAM;IACZwB,gBAAgB,EAAE,IAAI;IACtBnD,OAAO,EAAE,aAAa;IACtBC,QAAQ,EAAEqB;EACZ,CAAC;AACH;AAEA8B,MAAM,CAACC,OAAO,GAAGrC,IAAI"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}