{"ast": null, "code": "import baseAssignValue from './_baseAssignValue.js';\nimport baseForOwn from './_baseForOwn.js';\nimport baseIteratee from './_baseIteratee.js';\n\n/**\n * The opposite of `_.mapValues`; this method creates an object with the\n * same values as `object` and keys generated by running each own enumerable\n * string keyed property of `object` thru `iteratee`. The iteratee is invoked\n * with three arguments: (value, key, object).\n *\n * @static\n * @memberOf _\n * @since 3.8.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns the new mapped object.\n * @see _.mapValues\n * @example\n *\n * _.mapKeys({ 'a': 1, 'b': 2 }, function(value, key) {\n *   return key + value;\n * });\n * // => { 'a1': 1, 'b2': 2 }\n */\nfunction mapKeys(object, iteratee) {\n  var result = {};\n  iteratee = baseIteratee(iteratee, 3);\n  baseForOwn(object, function (value, key, object) {\n    baseAssignValue(result, iteratee(value, key, object), value);\n  });\n  return result;\n}\nexport default mapKeys;", "map": {"version": 3, "names": ["baseAssignValue", "baseForOwn", "baseIteratee", "mapKeys", "object", "iteratee", "result", "value", "key"], "sources": ["C:/OneDrive/Srikant/SWIP/ClaimsPro/claimspro200-src/claimspro_ui/node_modules/lodash-es/mapKeys.js"], "sourcesContent": ["import baseAssignValue from './_baseAssignValue.js';\nimport baseForOwn from './_baseForOwn.js';\nimport baseIteratee from './_baseIteratee.js';\n\n/**\n * The opposite of `_.mapValues`; this method creates an object with the\n * same values as `object` and keys generated by running each own enumerable\n * string keyed property of `object` thru `iteratee`. The iteratee is invoked\n * with three arguments: (value, key, object).\n *\n * @static\n * @memberOf _\n * @since 3.8.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns the new mapped object.\n * @see _.mapValues\n * @example\n *\n * _.mapKeys({ 'a': 1, 'b': 2 }, function(value, key) {\n *   return key + value;\n * });\n * // => { 'a1': 1, 'b2': 2 }\n */\nfunction mapKeys(object, iteratee) {\n  var result = {};\n  iteratee = baseIteratee(iteratee, 3);\n\n  baseForOwn(object, function(value, key, object) {\n    baseAssignValue(result, iteratee(value, key, object), value);\n  });\n  return result;\n}\n\nexport default mapKeys;\n"], "mappings": "AAAA,OAAOA,eAAe,MAAM,uBAAuB;AACnD,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,YAAY,MAAM,oBAAoB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EACjC,IAAIC,MAAM,GAAG,CAAC,CAAC;EACfD,QAAQ,GAAGH,YAAY,CAACG,QAAQ,EAAE,CAAC,CAAC;EAEpCJ,UAAU,CAACG,MAAM,EAAE,UAASG,KAAK,EAAEC,GAAG,EAAEJ,MAAM,EAAE;IAC9CJ,eAAe,CAACM,MAAM,EAAED,QAAQ,CAACE,KAAK,EAAEC,GAAG,EAAEJ,MAAM,CAAC,EAAEG,KAAK,CAAC;EAC9D,CAAC,CAAC;EACF,OAAOD,MAAM;AACf;AAEA,eAAeH,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}