{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"app/shared/search/search.service\";\nimport * as i2 from \"../country.service\";\nimport * as i3 from \"@angular/common\";\nfunction ResultPageComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 5);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"span\", 6);\n    i0.ɵɵtext(7, \"Official Name:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.flag);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.name.common);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", item_r1.name.official, \"\");\n  }\n}\nexport class ResultPageComponent {\n  constructor(searchService, countryService) {\n    this.searchService = searchService;\n    this.countryService = countryService;\n  }\n  ngOnInit() {\n    this.searchTermSub = this.searchService.searchTerm$.subscribe(term => {\n      this.countries$ = this.countryService.getCountries(term);\n    });\n  }\n  ngOnDestroy() {\n    if (this.searchTermSub) {\n      this.searchTermSub.unsubscribe();\n    }\n  }\n  static #_ = this.ɵfac = function ResultPageComponent_Factory(t) {\n    return new (t || ResultPageComponent)(i0.ɵɵdirectiveInject(i1.SearchService), i0.ɵɵdirectiveInject(i2.CountryService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ResultPageComponent,\n    selectors: [[\"app-result-page\"]],\n    decls: 7,\n    vars: 6,\n    consts: [[1, \"m-4\"], [1, \"mt-0\"], [1, \"mat-box-shadow\", \"m-4\"], [\"class\", \"flex p-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"p-4\"], [1, \"mr-32\"], [1, \"text-muted\"]],\n    template: function ResultPageComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵpipe(3, \"async\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(4, \"div\", 2);\n        i0.ɵɵtemplate(5, ResultPageComponent_div_5_Template, 9, 3, \"div\", 3);\n        i0.ɵɵpipe(6, \"async\");\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\"Search result for \\\"\", i0.ɵɵpipeBind1(3, 2, ctx.searchService.searchTerm$), \"\\\"\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(6, 4, ctx.countries$));\n      }\n    },\n    dependencies: [i3.NgForOf, i3.AsyncPipe],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "item_r1", "flag", "name", "common", "ɵɵtextInterpolate1", "official", "ResultPageComponent", "constructor", "searchService", "countryService", "ngOnInit", "searchTermSub", "searchTerm$", "subscribe", "term", "countries$", "getCountries", "ngOnDestroy", "unsubscribe", "_", "ɵɵdirectiveInject", "i1", "SearchService", "i2", "CountryService", "_2", "selectors", "decls", "vars", "consts", "template", "ResultPageComponent_Template", "rf", "ctx", "ɵɵtemplate", "ResultPageComponent_div_5_Template", "ɵɵpipeBind1", "ɵɵproperty"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\views\\search-view\\result-page\\result-page.component.ts", "C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\views\\search-view\\result-page\\result-page.component.html"], "sourcesContent": ["import { Component, <PERSON>Ini<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from \"@angular/core\";\nimport { SearchService } from \"app/shared/search/search.service\";\nimport { Observable, Subscription } from \"rxjs\";\nimport { CountryService } from \"../country.service\";\n\n@Component({\n  selector: \"app-result-page\",\n  templateUrl: \"./result-page.component.html\",\n  styleUrls: [\"./result-page.component.scss\"]\n})\nexport class ResultPageComponent implements OnInit, OnDestroy {\n  countries$: Observable<any[]>;\n  searchTermSub: Subscription;\n\n  constructor(\n    public searchService: SearchService,\n    public countryService: CountryService\n  ) {}\n\n  ngOnInit() {\n    this.searchTermSub = this.searchService.searchTerm$.subscribe(term => {\n      this.countries$ = this.countryService.getCountries(term);\n    });\n  }\n\n  ngOnDestroy() {\n    if (this.searchTermSub) {\n      this.searchTermSub.unsubscribe();\n    }\n  }\n  \n}\n", "<div class=\"m-4\">\n  <h2 class=\"mt-0\">Search result for \"{{ searchService.searchTerm$ | async }}\"</h2>\n</div>\n\n<div class=\"mat-box-shadow m-4\">\n  <div *ngFor=\"let item of countries$ | async\" class=\"flex p-4\">\n    <div class=\"mr-32\">{{item.flag}}</div>\n    <div class=\"mr-32\">{{item.name.common}}</div>\n    <div><span class=\"text-muted\">Official Name:</span> {{item.name.official}}</div>\n  </div>\n\n</div>"], "mappings": ";;;;;;ICKEA,EAAA,CAAAC,cAAA,aAA8D;IACzCD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACtCH,EAAA,CAAAC,cAAA,aAAmB;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC7CH,EAAA,CAAAC,cAAA,UAAK;IAAyBD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAF7DH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAC,IAAA,CAAa;IACbP,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAE,IAAA,CAAAC,MAAA,CAAoB;IACaT,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAU,kBAAA,MAAAJ,OAAA,CAAAE,IAAA,CAAAG,QAAA,KAAsB;;;ADE9E,OAAM,MAAOC,mBAAmB;EAI9BC,YACSC,aAA4B,EAC5BC,cAA8B;IAD9B,KAAAD,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;EACpB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,GAAG,IAAI,CAACH,aAAa,CAACI,WAAW,CAACC,SAAS,CAACC,IAAI,IAAG;MACnE,IAAI,CAACC,UAAU,GAAG,IAAI,CAACN,cAAc,CAACO,YAAY,CAACF,IAAI,CAAC;IAC1D,CAAC,CAAC;EACJ;EAEAG,WAAWA,CAAA;IACT,IAAI,IAAI,CAACN,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACO,WAAW,EAAE;IAClC;EACF;EAAC,QAAAC,CAAA,G;qBAnBUb,mBAAmB,EAAAZ,EAAA,CAAA0B,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA5B,EAAA,CAAA0B,iBAAA,CAAAG,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAnBnB,mBAAmB;IAAAoB,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCVhCtC,EAAA,CAAAC,cAAA,aAAiB;QACED,EAAA,CAAAE,MAAA,GAA2D;;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAGnFH,EAAA,CAAAC,cAAA,aAAgC;QAC9BD,EAAA,CAAAwC,UAAA,IAAAC,kCAAA,iBAIM;;QAERzC,EAAA,CAAAG,YAAA,EAAM;;;QAVaH,EAAA,CAAAI,SAAA,GAA2D;QAA3DJ,EAAA,CAAAU,kBAAA,yBAAAV,EAAA,CAAA0C,WAAA,OAAAH,GAAA,CAAAzB,aAAA,CAAAI,WAAA,QAA2D;QAItDlB,EAAA,CAAAI,SAAA,GAAqB;QAArBJ,EAAA,CAAA2C,UAAA,YAAA3C,EAAA,CAAA0C,WAAA,OAAAH,GAAA,CAAAlB,UAAA,EAAqB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}