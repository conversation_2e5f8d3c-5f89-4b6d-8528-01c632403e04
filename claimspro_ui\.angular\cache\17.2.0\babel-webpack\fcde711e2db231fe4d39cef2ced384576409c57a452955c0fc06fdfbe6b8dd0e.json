{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/button\";\nexport class FooterComponent {\n  constructor() {}\n  ngOnInit() {}\n  static #_ = this.ɵfac = function FooterComponent_Factory(t) {\n    return new (t || FooterComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FooterComponent,\n    selectors: [[\"app-footer\"]],\n    decls: 8,\n    vars: 0,\n    consts: [[1, \"main-footer\"], [1, \"container-dynamic\", \"flex\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"href\", \"https://www.asclaimpro.com\"], [1, \"m-auto\"], [\"href\", \"https://swipartners.com\"]],\n    template: function FooterComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"footer\", 0)(1, \"div\", 1)(2, \"a\", 2);\n        i0.ɵɵtext(3, \"Subscribe to ASClaimPro\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(4, \"span\", 3);\n        i0.ɵɵtext(5, \" Design & Developed by \\u00A0\");\n        i0.ɵɵelementStart(6, \"a\", 4);\n        i0.ɵɵtext(7, \" SoftwareIntegrationPartners,LLC\");\n        i0.ɵɵelementEnd()()();\n      }\n    },\n    dependencies: [i1.MatAnchor],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["FooterComponent", "constructor", "ngOnInit", "_", "_2", "selectors", "decls", "vars", "consts", "template", "FooterComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\shared\\components\\footer\\footer.component.ts", "C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\shared\\components\\footer\\footer.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-footer',\n  templateUrl: './footer.component.html',\n  styleUrls: ['./footer.component.scss']\n})\nexport class FooterComponent implements OnInit {\n\n  constructor() { }\n\n  ngOnInit() {\n  }\n\n}\n", "<footer class=\"main-footer\">\r\n  <div class=\"container-dynamic flex\">\r\n    <a\r\n      mat-raised-button\r\n      color=\"accent\"\r\n      href=\"https://www.asclaimpro.com\"\r\n      >Subscribe to ASClaimPro</a\r\n    >\r\n    <span class=\"m-auto\"></span>\r\n    Design & Developed by &nbsp;<a href=\"https://swipartners.com\"> SoftwareIntegrationPartners,LLC</a>\r\n  </div>\r\n</footer>\r\n"], "mappings": ";;AAOA,OAAM,MAAOA,eAAe;EAE1BC,YAAA,GAAgB;EAEhBC,QAAQA,CAAA,GACR;EAAC,QAAAC,CAAA,G;qBALUH,eAAe;EAAA;EAAA,QAAAI,EAAA,G;UAAfJ,eAAe;IAAAK,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCP5BE,EAAA,CAAAC,cAAA,gBAA4B;QAMrBD,EAAA,CAAAE,MAAA,8BAAuB;QAAAF,EAAA,CAAAG,YAAA,EACzB;QACDH,EAAA,CAAAI,SAAA,cAA4B;QAC5BJ,EAAA,CAAAE,MAAA,oCAA4B;QAAAF,EAAA,CAAAC,cAAA,WAAkC;QAACD,EAAA,CAAAE,MAAA,uCAA+B;QAAAF,EAAA,CAAAG,YAAA,EAAI"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}