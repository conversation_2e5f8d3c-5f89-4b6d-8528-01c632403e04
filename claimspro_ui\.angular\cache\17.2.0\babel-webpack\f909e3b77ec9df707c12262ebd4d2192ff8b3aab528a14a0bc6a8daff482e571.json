{"ast": null, "code": "/*\nLanguage: MoonScript\nAuthor: <PERSON> <<EMAIL>>\nDescription: MoonScript is a programming language that transcompiles to Lua.\nOrigin: coffeescript.js\nWebsite: http://moonscript.org/\nCategory: scripting\n*/\n\nfunction moonscript(hljs) {\n  const KEYWORDS = {\n    keyword:\n    // Moonscript keywords\n    'if then not for in while do return else elseif break continue switch and or ' + 'unless when class extends super local import export from using',\n    literal: 'true false nil',\n    built_in: '_G _VERSION assert collectgarbage dofile error getfenv getmetatable ipairs load ' + 'loadfile loadstring module next pairs pcall print rawequal rawget rawset require ' + 'select setfenv setmetatable tonumber tostring type unpack xpcall coroutine debug ' + 'io math os package string table'\n  };\n  const JS_IDENT_RE = '[A-Za-z$_][0-9A-Za-z$_]*';\n  const SUBST = {\n    className: 'subst',\n    begin: /#\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS\n  };\n  const EXPRESSIONS = [hljs.inherit(hljs.C_NUMBER_MODE, {\n    starts: {\n      end: '(\\\\s*/)?',\n      relevance: 0\n    }\n  }),\n  // a number tries to eat the following slash to prevent treating it as a regexp\n  {\n    className: 'string',\n    variants: [{\n      begin: /'/,\n      end: /'/,\n      contains: [hljs.BACKSLASH_ESCAPE]\n    }, {\n      begin: /\"/,\n      end: /\"/,\n      contains: [hljs.BACKSLASH_ESCAPE, SUBST]\n    }]\n  }, {\n    className: 'built_in',\n    begin: '@__' + hljs.IDENT_RE\n  }, {\n    begin: '@' + hljs.IDENT_RE // relevance booster on par with CoffeeScript\n  }, {\n    begin: hljs.IDENT_RE + '\\\\\\\\' + hljs.IDENT_RE // inst\\method\n  }];\n  SUBST.contains = EXPRESSIONS;\n  const TITLE = hljs.inherit(hljs.TITLE_MODE, {\n    begin: JS_IDENT_RE\n  });\n  const POSSIBLE_PARAMS_RE = '(\\\\(.*\\\\)\\\\s*)?\\\\B[-=]>';\n  const PARAMS = {\n    className: 'params',\n    begin: '\\\\([^\\\\(]',\n    returnBegin: true,\n    /* We need another contained nameless mode to not have every nested\n    pair of parens to be called \"params\" */\n    contains: [{\n      begin: /\\(/,\n      end: /\\)/,\n      keywords: KEYWORDS,\n      contains: ['self'].concat(EXPRESSIONS)\n    }]\n  };\n  return {\n    name: 'MoonScript',\n    aliases: ['moon'],\n    keywords: KEYWORDS,\n    illegal: /\\/\\*/,\n    contains: EXPRESSIONS.concat([hljs.COMMENT('--', '$'), {\n      className: 'function',\n      // function: -> =>\n      begin: '^\\\\s*' + JS_IDENT_RE + '\\\\s*=\\\\s*' + POSSIBLE_PARAMS_RE,\n      end: '[-=]>',\n      returnBegin: true,\n      contains: [TITLE, PARAMS]\n    }, {\n      begin: /[\\(,:=]\\s*/,\n      // anonymous function start\n      relevance: 0,\n      contains: [{\n        className: 'function',\n        begin: POSSIBLE_PARAMS_RE,\n        end: '[-=]>',\n        returnBegin: true,\n        contains: [PARAMS]\n      }]\n    }, {\n      className: 'class',\n      beginKeywords: 'class',\n      end: '$',\n      illegal: /[:=\"\\[\\]]/,\n      contains: [{\n        beginKeywords: 'extends',\n        endsWithParent: true,\n        illegal: /[:=\"\\[\\]]/,\n        contains: [TITLE]\n      }, TITLE]\n    }, {\n      className: 'name',\n      // table\n      begin: JS_IDENT_RE + ':',\n      end: ':',\n      returnBegin: true,\n      returnEnd: true,\n      relevance: 0\n    }])\n  };\n}\nmodule.exports = moonscript;", "map": {"version": 3, "names": ["moonscript", "hljs", "KEYWORDS", "keyword", "literal", "built_in", "JS_IDENT_RE", "SUBST", "className", "begin", "end", "keywords", "EXPRESSIONS", "inherit", "C_NUMBER_MODE", "starts", "relevance", "variants", "contains", "BACKSLASH_ESCAPE", "IDENT_RE", "TITLE", "TITLE_MODE", "POSSIBLE_PARAMS_RE", "PARAMS", "returnBegin", "concat", "name", "aliases", "illegal", "COMMENT", "beginKeywords", "endsWithParent", "returnEnd", "module", "exports"], "sources": ["C:/OneDrive/Srikant/SWIP/ClaimsPro/claimspro200-src/claimspro_ui/node_modules/highlight.js/lib/languages/moonscript.js"], "sourcesContent": ["/*\nLanguage: MoonScript\nAuthor: <PERSON> <<EMAIL>>\nDescription: MoonScript is a programming language that transcompiles to Lua.\nOrigin: coffeescript.js\nWebsite: http://moonscript.org/\nCategory: scripting\n*/\n\nfunction moonscript(hljs) {\n  const KEYWORDS = {\n    keyword:\n      // Moonscript keywords\n      'if then not for in while do return else elseif break continue switch and or '\n      + 'unless when class extends super local import export from using',\n    literal:\n      'true false nil',\n    built_in:\n      '_G _VERSION assert collectgarbage dofile error getfenv getmetatable ipairs load '\n      + 'loadfile loadstring module next pairs pcall print rawequal rawget rawset require '\n      + 'select setfenv setmetatable tonumber tostring type unpack xpcall coroutine debug '\n      + 'io math os package string table'\n  };\n  const JS_IDENT_RE = '[A-Za-z$_][0-9A-Za-z$_]*';\n  const SUBST = {\n    className: 'subst',\n    begin: /#\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS\n  };\n  const EXPRESSIONS = [\n    hljs.inherit(hljs.C_NUMBER_MODE,\n      { starts: {\n        end: '(\\\\s*/)?',\n        relevance: 0\n      } }), // a number tries to eat the following slash to prevent treating it as a regexp\n    {\n      className: 'string',\n      variants: [\n        {\n          begin: /'/,\n          end: /'/,\n          contains: [ hljs.BACKSLASH_ESCAPE ]\n        },\n        {\n          begin: /\"/,\n          end: /\"/,\n          contains: [\n            hljs.BACKSLASH_ESCAPE,\n            SUBST\n          ]\n        }\n      ]\n    },\n    {\n      className: 'built_in',\n      begin: '@__' + hljs.IDENT_RE\n    },\n    { begin: '@' + hljs.IDENT_RE // relevance booster on par with CoffeeScript\n    },\n    { begin: hljs.IDENT_RE + '\\\\\\\\' + hljs.IDENT_RE // inst\\method\n    }\n  ];\n  SUBST.contains = EXPRESSIONS;\n\n  const TITLE = hljs.inherit(hljs.TITLE_MODE, { begin: JS_IDENT_RE });\n  const POSSIBLE_PARAMS_RE = '(\\\\(.*\\\\)\\\\s*)?\\\\B[-=]>';\n  const PARAMS = {\n    className: 'params',\n    begin: '\\\\([^\\\\(]',\n    returnBegin: true,\n    /* We need another contained nameless mode to not have every nested\n    pair of parens to be called \"params\" */\n    contains: [\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: KEYWORDS,\n        contains: [ 'self' ].concat(EXPRESSIONS)\n      }\n    ]\n  };\n\n  return {\n    name: 'MoonScript',\n    aliases: [ 'moon' ],\n    keywords: KEYWORDS,\n    illegal: /\\/\\*/,\n    contains: EXPRESSIONS.concat([\n      hljs.COMMENT('--', '$'),\n      {\n        className: 'function', // function: -> =>\n        begin: '^\\\\s*' + JS_IDENT_RE + '\\\\s*=\\\\s*' + POSSIBLE_PARAMS_RE,\n        end: '[-=]>',\n        returnBegin: true,\n        contains: [\n          TITLE,\n          PARAMS\n        ]\n      },\n      {\n        begin: /[\\(,:=]\\s*/, // anonymous function start\n        relevance: 0,\n        contains: [\n          {\n            className: 'function',\n            begin: POSSIBLE_PARAMS_RE,\n            end: '[-=]>',\n            returnBegin: true,\n            contains: [ PARAMS ]\n          }\n        ]\n      },\n      {\n        className: 'class',\n        beginKeywords: 'class',\n        end: '$',\n        illegal: /[:=\"\\[\\]]/,\n        contains: [\n          {\n            beginKeywords: 'extends',\n            endsWithParent: true,\n            illegal: /[:=\"\\[\\]]/,\n            contains: [ TITLE ]\n          },\n          TITLE\n        ]\n      },\n      {\n        className: 'name', // table\n        begin: JS_IDENT_RE + ':',\n        end: ':',\n        returnBegin: true,\n        returnEnd: true,\n        relevance: 0\n      }\n    ])\n  };\n}\n\nmodule.exports = moonscript;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,UAAUA,CAACC,IAAI,EAAE;EACxB,MAAMC,QAAQ,GAAG;IACfC,OAAO;IACL;IACA,8EAA8E,GAC5E,gEAAgE;IACpEC,OAAO,EACL,gBAAgB;IAClBC,QAAQ,EACN,kFAAkF,GAChF,mFAAmF,GACnF,mFAAmF,GACnF;EACN,CAAC;EACD,MAAMC,WAAW,GAAG,0BAA0B;EAC9C,MAAMC,KAAK,GAAG;IACZC,SAAS,EAAE,OAAO;IAClBC,KAAK,EAAE,KAAK;IACZC,GAAG,EAAE,IAAI;IACTC,QAAQ,EAAET;EACZ,CAAC;EACD,MAAMU,WAAW,GAAG,CAClBX,IAAI,CAACY,OAAO,CAACZ,IAAI,CAACa,aAAa,EAC7B;IAAEC,MAAM,EAAE;MACRL,GAAG,EAAE,UAAU;MACfM,SAAS,EAAE;IACb;EAAE,CAAC,CAAC;EAAE;EACR;IACER,SAAS,EAAE,QAAQ;IACnBS,QAAQ,EAAE,CACR;MACER,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE,GAAG;MACRQ,QAAQ,EAAE,CAAEjB,IAAI,CAACkB,gBAAgB;IACnC,CAAC,EACD;MACEV,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE,GAAG;MACRQ,QAAQ,EAAE,CACRjB,IAAI,CAACkB,gBAAgB,EACrBZ,KAAK;IAET,CAAC;EAEL,CAAC,EACD;IACEC,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE,KAAK,GAAGR,IAAI,CAACmB;EACtB,CAAC,EACD;IAAEX,KAAK,EAAE,GAAG,GAAGR,IAAI,CAACmB,QAAQ,CAAC;EAC7B,CAAC,EACD;IAAEX,KAAK,EAAER,IAAI,CAACmB,QAAQ,GAAG,MAAM,GAAGnB,IAAI,CAACmB,QAAQ,CAAC;EAChD,CAAC,CACF;EACDb,KAAK,CAACW,QAAQ,GAAGN,WAAW;EAE5B,MAAMS,KAAK,GAAGpB,IAAI,CAACY,OAAO,CAACZ,IAAI,CAACqB,UAAU,EAAE;IAAEb,KAAK,EAAEH;EAAY,CAAC,CAAC;EACnE,MAAMiB,kBAAkB,GAAG,yBAAyB;EACpD,MAAMC,MAAM,GAAG;IACbhB,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,WAAW;IAClBgB,WAAW,EAAE,IAAI;IACjB;AACJ;IACIP,QAAQ,EAAE,CACR;MACET,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,IAAI;MACTC,QAAQ,EAAET,QAAQ;MAClBgB,QAAQ,EAAE,CAAE,MAAM,CAAE,CAACQ,MAAM,CAACd,WAAW;IACzC,CAAC;EAEL,CAAC;EAED,OAAO;IACLe,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,CAAE,MAAM,CAAE;IACnBjB,QAAQ,EAAET,QAAQ;IAClB2B,OAAO,EAAE,MAAM;IACfX,QAAQ,EAAEN,WAAW,CAACc,MAAM,CAAC,CAC3BzB,IAAI,CAAC6B,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,EACvB;MACEtB,SAAS,EAAE,UAAU;MAAE;MACvBC,KAAK,EAAE,OAAO,GAAGH,WAAW,GAAG,WAAW,GAAGiB,kBAAkB;MAC/Db,GAAG,EAAE,OAAO;MACZe,WAAW,EAAE,IAAI;MACjBP,QAAQ,EAAE,CACRG,KAAK,EACLG,MAAM;IAEV,CAAC,EACD;MACEf,KAAK,EAAE,YAAY;MAAE;MACrBO,SAAS,EAAE,CAAC;MACZE,QAAQ,EAAE,CACR;QACEV,SAAS,EAAE,UAAU;QACrBC,KAAK,EAAEc,kBAAkB;QACzBb,GAAG,EAAE,OAAO;QACZe,WAAW,EAAE,IAAI;QACjBP,QAAQ,EAAE,CAAEM,MAAM;MACpB,CAAC;IAEL,CAAC,EACD;MACEhB,SAAS,EAAE,OAAO;MAClBuB,aAAa,EAAE,OAAO;MACtBrB,GAAG,EAAE,GAAG;MACRmB,OAAO,EAAE,WAAW;MACpBX,QAAQ,EAAE,CACR;QACEa,aAAa,EAAE,SAAS;QACxBC,cAAc,EAAE,IAAI;QACpBH,OAAO,EAAE,WAAW;QACpBX,QAAQ,EAAE,CAAEG,KAAK;MACnB,CAAC,EACDA,KAAK;IAET,CAAC,EACD;MACEb,SAAS,EAAE,MAAM;MAAE;MACnBC,KAAK,EAAEH,WAAW,GAAG,GAAG;MACxBI,GAAG,EAAE,GAAG;MACRe,WAAW,EAAE,IAAI;MACjBQ,SAAS,EAAE,IAAI;MACfjB,SAAS,EAAE;IACb,CAAC,CACF;EACH,CAAC;AACH;AAEAkB,MAAM,CAACC,OAAO,GAAGnC,UAAU"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}