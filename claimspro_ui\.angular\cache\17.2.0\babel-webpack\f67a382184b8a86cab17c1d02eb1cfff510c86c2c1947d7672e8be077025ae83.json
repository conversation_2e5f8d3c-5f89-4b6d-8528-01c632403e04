{"ast": null, "code": "import { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/material/input\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/slide-toggle\";\nimport * as i7 from \"@ngx-translate/core\";\nexport class NgxTablePopupComponent {\n  constructor(data, dialogRef, fb) {\n    this.data = data;\n    this.dialogRef = dialogRef;\n    this.fb = fb;\n  }\n  ngOnInit() {\n    this.buildItemForm(this.data.payload);\n  }\n  buildItemForm(item) {\n    this.itemForm = this.fb.group({\n      name: [item.name || '', Validators.required],\n      age: [item.age || ''],\n      email: [item.email || ''],\n      company: [item.company || ''],\n      phone: [item.phone || ''],\n      address: [item.address || ''],\n      balance: [item.balance || ''],\n      isActive: [item.isActive || false]\n    });\n  }\n  submit() {\n    this.dialogRef.close(this.itemForm.value);\n  }\n  static #_ = this.ɵfac = function NgxTablePopupComponent_Factory(t) {\n    return new (t || NgxTablePopupComponent)(i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(i2.UntypedFormBuilder));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NgxTablePopupComponent,\n    selectors: [[\"app-ngx-table-popup\"]],\n    decls: 36,\n    vars: 14,\n    consts: [[1, \"p-4\"], [1, \"text-lg\", \"mb-4\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"flex\", \"flex-wrap\", \"flex-col\", \"md:flex-row\"], [1, \"w-full\", \"md:w-1/2\", \"pr-16\"], [1, \"full-width\"], [\"matInput\", \"\", \"name\", \"username\", \"placeholder\", \"Name\", 3, \"formControl\"], [\"matInput\", \"\", \"type\", \"number\", \"name\", \"age\", \"placeholder\", \"Age\", 3, \"formControl\"], [\"matInput\", \"\", \"type\", \"email\", \"name\", \"email\", \"placeholder\", \"Email\", 3, \"formControl\"], [\"matInput\", \"\", \"name\", \"company\", \"placeholder\", \"Company\", 3, \"formControl\"], [\"matInput\", \"\", \"name\", \"phone\", \"placeholder\", \"Phone\", 3, \"formControl\"], [\"matInput\", \"\", \"name\", \"address\", \"placeholder\", \"address\", 3, \"formControl\"], [\"matInput\", \"\", \"name\", \"balance\", \"placeholder\", \"Balance\", 3, \"formControl\"], [1, \"w-full\", \"md:w-1/2\", \"!pt-4\", \"pr-16\"], [3, \"formControl\"], [1, \"flex\", \"w-full\", \"mt-4\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"disabled\"], [1, \"flex-grow\"], [\"mat-button\", \"\", \"color\", \"warn\", \"type\", \"button\", 3, \"click\"]],\n    template: function NgxTablePopupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"form\", 2);\n        i0.ɵɵlistener(\"ngSubmit\", function NgxTablePopupComponent_Template_form_ngSubmit_3_listener() {\n          return ctx.submit();\n        });\n        i0.ɵɵelementStart(4, \"div\", 3)(5, \"div\", 4)(6, \"mat-form-field\", 5);\n        i0.ɵɵelement(7, \"input\", 6);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"div\", 4)(9, \"mat-form-field\", 5);\n        i0.ɵɵelement(10, \"input\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 4)(12, \"mat-form-field\", 5);\n        i0.ɵɵelement(13, \"input\", 8);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"div\", 4)(15, \"mat-form-field\", 5);\n        i0.ɵɵelement(16, \"input\", 9);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"div\", 4)(18, \"mat-form-field\", 5);\n        i0.ɵɵelement(19, \"input\", 10);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(20, \"div\", 4)(21, \"mat-form-field\", 5);\n        i0.ɵɵelement(22, \"input\", 11);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(23, \"div\", 4)(24, \"mat-form-field\", 5);\n        i0.ɵɵelement(25, \"input\", 12);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(26, \"div\", 13)(27, \"mat-slide-toggle\", 14);\n        i0.ɵɵtext(28, \"Active Customer\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(29, \"div\", 15)(30, \"button\", 16);\n        i0.ɵɵtext(31);\n        i0.ɵɵpipe(32, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(33, \"span\", 17);\n        i0.ɵɵelementStart(34, \"button\", 18);\n        i0.ɵɵlistener(\"click\", function NgxTablePopupComponent_Template_button_click_34_listener() {\n          return ctx.dialogRef.close(false);\n        });\n        i0.ɵɵtext(35, \"Cancel\");\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.data.title);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"formGroup\", ctx.itemForm);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"formControl\", ctx.itemForm.controls[\"name\"]);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"formControl\", ctx.itemForm.controls[\"age\"]);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"formControl\", ctx.itemForm.controls[\"email\"]);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"formControl\", ctx.itemForm.controls[\"company\"]);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"formControl\", ctx.itemForm.controls[\"phone\"]);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"formControl\", ctx.itemForm.controls[\"address\"]);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"formControl\", ctx.itemForm.controls[\"balance\"]);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"formControl\", ctx.itemForm.controls[\"isActive\"]);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"disabled\", ctx.itemForm.invalid);\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(32, 12, \"SAVE\"));\n      }\n    },\n    dependencies: [i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NumberValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormControlDirective, i2.FormGroupDirective, i3.MatInput, i4.MatFormField, i5.MatButton, i6.MatSlideToggle, i7.TranslatePipe],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["MAT_DIALOG_DATA", "Validators", "NgxTablePopupComponent", "constructor", "data", "dialogRef", "fb", "ngOnInit", "buildItemForm", "payload", "item", "itemForm", "group", "name", "required", "age", "email", "company", "phone", "address", "balance", "isActive", "submit", "close", "value", "_", "i0", "ɵɵdirectiveInject", "i1", "MatDialogRef", "i2", "UntypedFormBuilder", "_2", "selectors", "decls", "vars", "consts", "template", "NgxTablePopupComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "NgxTablePopupComponent_Template_form_ngSubmit_3_listener", "ɵɵelement", "NgxTablePopupComponent_Template_button_click_34_listener", "ɵɵadvance", "ɵɵtextInterpolate", "title", "ɵɵproperty", "controls", "invalid", "ɵɵpipeBind1"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\views\\cruds\\crud-ngx-table\\ngx-table-popup\\ngx-table-popup.component.ts", "C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\views\\cruds\\crud-ngx-table\\ngx-table-popup\\ngx-table-popup.component.html"], "sourcesContent": ["import { Component, OnInit, Inject } from '@angular/core';\nimport { MatDialogRef as MatDialogRef, MAT_DIALOG_DATA as MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { UntypedFormBuilder, Validators, UntypedFormGroup } from '@angular/forms';\n\n@Component({\n  selector: 'app-ngx-table-popup',\n  templateUrl: './ngx-table-popup.component.html'\n})\nexport class NgxTablePopupComponent implements OnInit {\n  public itemForm: UntypedFormGroup;\n  constructor(\n    @Inject(MAT_DIALOG_DATA) public data: any,\n    public dialogRef: MatDialogRef<NgxTablePopupComponent>,\n    private fb: UntypedFormBuilder,\n  ) { }\n\n  ngOnInit() {\n    this.buildItemForm(this.data.payload)\n  }\n  buildItemForm(item) {\n    this.itemForm = this.fb.group({\n      name: [item.name || '', Validators.required],\n      age: [item.age || ''],\n      email: [item.email || ''],\n      company: [item.company || ''],\n      phone: [item.phone || ''],\n      address: [item.address || ''],\n      balance: [item.balance || ''],\n      isActive: [item.isActive || false]\n    })\n  }\n\n  submit() {\n    this.dialogRef.close(this.itemForm.value)\n  }\n}\n", "<div class=\"p-4\">\n  <h1 class=\"text-lg mb-4\">{{data.title}}</h1>\n  \n  <form [formGroup]=\"itemForm\" (ngSubmit)=\"submit()\">\n    <div class=\"flex flex-wrap flex-col md:flex-row\">\n      <div class=\"w-full md:w-1/2 pr-16\">\n        <mat-form-field class=\"full-width\">\n          <input matInput name=\"username\" [formControl]=\"itemForm.controls['name']\" placeholder=\"Name\">\n        </mat-form-field>\n      </div>\n\n      <div class=\"w-full md:w-1/2 pr-16\">\n        <mat-form-field class=\"full-width\">\n          <input matInput type=\"number\" name=\"age\" [formControl]=\"itemForm.controls['age']\" placeholder=\"Age\">\n        </mat-form-field>\n      </div>\n\n      <div class=\"w-full md:w-1/2 pr-16\">\n        <mat-form-field class=\"full-width\">\n          <input matInput type=\"email\" name=\"email\" [formControl]=\"itemForm.controls['email']\" placeholder=\"Email\">\n        </mat-form-field>\n      </div>\n\n      <div class=\"w-full md:w-1/2 pr-16\">\n        <mat-form-field class=\"full-width\">\n          <input matInput name=\"company\" [formControl]=\"itemForm.controls['company']\" placeholder=\"Company\">\n        </mat-form-field>\n      </div>\n      <div class=\"w-full md:w-1/2 pr-16\">\n        <mat-form-field class=\"full-width\">\n          <input matInput name=\"phone\" [formControl]=\"itemForm.controls['phone']\" placeholder=\"Phone\">\n        </mat-form-field>\n      </div>\n      <div class=\"w-full md:w-1/2 pr-16\">\n        <mat-form-field class=\"full-width\">\n          <input matInput name=\"address\" [formControl]=\"itemForm.controls['address']\" placeholder=\"address\">\n        </mat-form-field>\n      </div>\n      <div class=\"w-full md:w-1/2 pr-16\">\n        <mat-form-field class=\"full-width\">\n          <input matInput name=\"balance\" [formControl]=\"itemForm.controls['balance']\" placeholder=\"Balance\">\n        </mat-form-field>\n      </div>\n      <div class=\"w-full md:w-1/2 !pt-4 pr-16\">\n        <mat-slide-toggle [formControl]=\"itemForm.controls['isActive']\">Active Customer</mat-slide-toggle>\n      </div>\n\n      <div class=\"flex w-full mt-4\">\n        <button mat-raised-button color=\"primary\" [disabled]=\"itemForm.invalid\">{{\"SAVE\" | translate }}</button>\n        <span class=\"flex-grow\"></span>\n        <button mat-button color=\"warn\" type=\"button\" (click)=\"dialogRef.close(false)\">Cancel</button>\n      </div>\n    </div>\n  </form>\n</div>"], "mappings": "AACA,SAAuCA,eAAkC,QAAQ,0BAA0B;AAC3G,SAA6BC,UAAU,QAA0B,gBAAgB;;;;;;;;;AAMjF,OAAM,MAAOC,sBAAsB;EAEjCC,YACkCC,IAAS,EAClCC,SAA+C,EAC9CC,EAAsB;IAFE,KAAAF,IAAI,GAAJA,IAAI;IAC7B,KAAAC,SAAS,GAATA,SAAS;IACR,KAAAC,EAAE,GAAFA,EAAE;EACR;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,CAAC,IAAI,CAACJ,IAAI,CAACK,OAAO,CAAC;EACvC;EACAD,aAAaA,CAACE,IAAI;IAChB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACL,EAAE,CAACM,KAAK,CAAC;MAC5BC,IAAI,EAAE,CAACH,IAAI,CAACG,IAAI,IAAI,EAAE,EAAEZ,UAAU,CAACa,QAAQ,CAAC;MAC5CC,GAAG,EAAE,CAACL,IAAI,CAACK,GAAG,IAAI,EAAE,CAAC;MACrBC,KAAK,EAAE,CAACN,IAAI,CAACM,KAAK,IAAI,EAAE,CAAC;MACzBC,OAAO,EAAE,CAACP,IAAI,CAACO,OAAO,IAAI,EAAE,CAAC;MAC7BC,KAAK,EAAE,CAACR,IAAI,CAACQ,KAAK,IAAI,EAAE,CAAC;MACzBC,OAAO,EAAE,CAACT,IAAI,CAACS,OAAO,IAAI,EAAE,CAAC;MAC7BC,OAAO,EAAE,CAACV,IAAI,CAACU,OAAO,IAAI,EAAE,CAAC;MAC7BC,QAAQ,EAAE,CAACX,IAAI,CAACW,QAAQ,IAAI,KAAK;KAClC,CAAC;EACJ;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACjB,SAAS,CAACkB,KAAK,CAAC,IAAI,CAACZ,QAAQ,CAACa,KAAK,CAAC;EAC3C;EAAC,QAAAC,CAAA,G;qBA1BUvB,sBAAsB,EAAAwB,EAAA,CAAAC,iBAAA,CAGvB3B,eAAe,GAAA0B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,kBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAHd9B,sBAAsB;IAAA+B,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCRnCb,EAAA,CAAAe,cAAA,aAAiB;QACUf,EAAA,CAAAgB,MAAA,GAAc;QAAAhB,EAAA,CAAAiB,YAAA,EAAK;QAE5CjB,EAAA,CAAAe,cAAA,cAAmD;QAAtBf,EAAA,CAAAkB,UAAA,sBAAAC,yDAAA;UAAA,OAAYL,GAAA,CAAAlB,MAAA,EAAQ;QAAA,EAAC;QAChDI,EAAA,CAAAe,cAAA,aAAiD;QAG3Cf,EAAA,CAAAoB,SAAA,eAA6F;QAC/FpB,EAAA,CAAAiB,YAAA,EAAiB;QAGnBjB,EAAA,CAAAe,cAAA,aAAmC;QAE/Bf,EAAA,CAAAoB,SAAA,gBAAoG;QACtGpB,EAAA,CAAAiB,YAAA,EAAiB;QAGnBjB,EAAA,CAAAe,cAAA,cAAmC;QAE/Bf,EAAA,CAAAoB,SAAA,gBAAyG;QAC3GpB,EAAA,CAAAiB,YAAA,EAAiB;QAGnBjB,EAAA,CAAAe,cAAA,cAAmC;QAE/Bf,EAAA,CAAAoB,SAAA,gBAAkG;QACpGpB,EAAA,CAAAiB,YAAA,EAAiB;QAEnBjB,EAAA,CAAAe,cAAA,cAAmC;QAE/Bf,EAAA,CAAAoB,SAAA,iBAA4F;QAC9FpB,EAAA,CAAAiB,YAAA,EAAiB;QAEnBjB,EAAA,CAAAe,cAAA,cAAmC;QAE/Bf,EAAA,CAAAoB,SAAA,iBAAkG;QACpGpB,EAAA,CAAAiB,YAAA,EAAiB;QAEnBjB,EAAA,CAAAe,cAAA,cAAmC;QAE/Bf,EAAA,CAAAoB,SAAA,iBAAkG;QACpGpB,EAAA,CAAAiB,YAAA,EAAiB;QAEnBjB,EAAA,CAAAe,cAAA,eAAyC;QACyBf,EAAA,CAAAgB,MAAA,uBAAe;QAAAhB,EAAA,CAAAiB,YAAA,EAAmB;QAGpGjB,EAAA,CAAAe,cAAA,eAA8B;QAC4Cf,EAAA,CAAAgB,MAAA,IAAuB;;QAAAhB,EAAA,CAAAiB,YAAA,EAAS;QACxGjB,EAAA,CAAAoB,SAAA,gBAA+B;QAC/BpB,EAAA,CAAAe,cAAA,kBAA+E;QAAjCf,EAAA,CAAAkB,UAAA,mBAAAG,yDAAA;UAAA,OAASP,GAAA,CAAAnC,SAAA,CAAAkB,KAAA,CAAgB,KAAK,CAAC;QAAA,EAAC;QAACG,EAAA,CAAAgB,MAAA,cAAM;QAAAhB,EAAA,CAAAiB,YAAA,EAAS;;;QAjD3EjB,EAAA,CAAAsB,SAAA,GAAc;QAAdtB,EAAA,CAAAuB,iBAAA,CAAAT,GAAA,CAAApC,IAAA,CAAA8C,KAAA,CAAc;QAEjCxB,EAAA,CAAAsB,SAAA,EAAsB;QAAtBtB,EAAA,CAAAyB,UAAA,cAAAX,GAAA,CAAA7B,QAAA,CAAsB;QAIYe,EAAA,CAAAsB,SAAA,GAAyC;QAAzCtB,EAAA,CAAAyB,UAAA,gBAAAX,GAAA,CAAA7B,QAAA,CAAAyC,QAAA,SAAyC;QAMhC1B,EAAA,CAAAsB,SAAA,GAAwC;QAAxCtB,EAAA,CAAAyB,UAAA,gBAAAX,GAAA,CAAA7B,QAAA,CAAAyC,QAAA,QAAwC;QAMvC1B,EAAA,CAAAsB,SAAA,GAA0C;QAA1CtB,EAAA,CAAAyB,UAAA,gBAAAX,GAAA,CAAA7B,QAAA,CAAAyC,QAAA,UAA0C;QAMrD1B,EAAA,CAAAsB,SAAA,GAA4C;QAA5CtB,EAAA,CAAAyB,UAAA,gBAAAX,GAAA,CAAA7B,QAAA,CAAAyC,QAAA,YAA4C;QAK9C1B,EAAA,CAAAsB,SAAA,GAA0C;QAA1CtB,EAAA,CAAAyB,UAAA,gBAAAX,GAAA,CAAA7B,QAAA,CAAAyC,QAAA,UAA0C;QAKxC1B,EAAA,CAAAsB,SAAA,GAA4C;QAA5CtB,EAAA,CAAAyB,UAAA,gBAAAX,GAAA,CAAA7B,QAAA,CAAAyC,QAAA,YAA4C;QAK5C1B,EAAA,CAAAsB,SAAA,GAA4C;QAA5CtB,EAAA,CAAAyB,UAAA,gBAAAX,GAAA,CAAA7B,QAAA,CAAAyC,QAAA,YAA4C;QAI3D1B,EAAA,CAAAsB,SAAA,GAA6C;QAA7CtB,EAAA,CAAAyB,UAAA,gBAAAX,GAAA,CAAA7B,QAAA,CAAAyC,QAAA,aAA6C;QAIrB1B,EAAA,CAAAsB,SAAA,GAA6B;QAA7BtB,EAAA,CAAAyB,UAAA,aAAAX,GAAA,CAAA7B,QAAA,CAAA0C,OAAA,CAA6B;QAAC3B,EAAA,CAAAsB,SAAA,EAAuB;QAAvBtB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAA4B,WAAA,iBAAuB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}