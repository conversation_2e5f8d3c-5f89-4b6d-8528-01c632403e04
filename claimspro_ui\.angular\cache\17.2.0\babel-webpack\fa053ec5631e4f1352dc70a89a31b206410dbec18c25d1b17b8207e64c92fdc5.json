{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/sidenav\";\nexport class EgretSideNavToggleDirective {\n  constructor(\n  // private mediaObserver: MediaObserver,\n  sideNav) {\n    this.sideNav = sideNav;\n  }\n  ngOnInit() {\n    this.initSideNav();\n  }\n  ngOnDestroy() {\n    if (this.screenSizeWatcher) {\n      this.screenSizeWatcher.unsubscribe();\n    }\n  }\n  updateSidenav() {\n    var self = this;\n    setTimeout(() => {\n      self.sideNav.opened = !self.isMobile;\n      self.sideNav.mode = self.isMobile ? 'over' : 'side';\n    });\n  }\n  initSideNav() {\n    // this.isMobile = this.mediaObserver.isActive('xs') || this.mediaObserver.isActive('sm');\n    this.updateSidenav();\n    // this.screenSizeWatcher = this.mediaObserver.asObservable()\n    // .subscribe((change: MediaChange[]) => {\n    //     this.isMobile = (change[0].mqAlias == 'xs') || (change[0].mqAlias == 'sm');\n    //   this.updateSidenav();\n    // });\n  }\n  static #_ = this.ɵfac = function EgretSideNavToggleDirective_Factory(t) {\n    return new (t || EgretSideNavToggleDirective)(i0.ɵɵdirectiveInject(i1.MatSidenav, 11));\n  };\n  static #_2 = this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n    type: EgretSideNavToggleDirective,\n    selectors: [[\"\", \"EgretSideNavToggle\", \"\"]]\n  });\n}", "map": {"version": 3, "names": ["EgretSideNavToggleDirective", "constructor", "sideNav", "ngOnInit", "initSideNav", "ngOnDestroy", "screenSizeWatcher", "unsubscribe", "updateSidenav", "self", "setTimeout", "opened", "isMobile", "mode", "_", "i0", "ɵɵdirectiveInject", "i1", "<PERSON><PERSON><PERSON><PERSON>", "_2", "selectors"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\shared\\directives\\egret-side-nav-toggle.directive.ts"], "sourcesContent": ["import { Directive, Host, Self, Optional, OnDestroy, OnInit } from '@angular/core';\n// import { MediaChange, MediaObserver } from \"@angular/flex-layout\";\nimport { Subscription } from \"rxjs\";\nimport { MatSidenav } from '@angular/material/sidenav';\n\n\n@Directive({\n  selector: '[EgretSideNavToggle]'\n})\nexport class EgretSideNavToggleDirective implements OnInit, OnDestroy {\n  isMobile;\n  screenSizeWatcher: Subscription;\n  constructor(\n    // private mediaObserver: MediaObserver,\n    @Host() @Self() @Optional() public sideNav: MatSidenav\n  ) {\n  }\n\n  ngOnInit() {\n    this.initSideNav();\n  }\n\n  ngOnDestroy() {\n    if(this.screenSizeWatcher) {\n      this.screenSizeWatcher.unsubscribe()\n    }\n  }\n\n  updateSidenav() {\n    var self = this;\n    setTimeout(() => {\n      self.sideNav.opened = !self.isMobile;\n      self.sideNav.mode = self.isMobile ? 'over' : 'side';\n    })\n  }\n  initSideNav() {\n    // this.isMobile = this.mediaObserver.isActive('xs') || this.mediaObserver.isActive('sm');\n    this.updateSidenav();\n    // this.screenSizeWatcher = this.mediaObserver.asObservable()\n    // .subscribe((change: MediaChange[]) => {\n    //     this.isMobile = (change[0].mqAlias == 'xs') || (change[0].mqAlias == 'sm');\n    //   this.updateSidenav();\n    // });\n  }\n\n}\n"], "mappings": ";;AASA,OAAM,MAAOA,2BAA2B;EAGtCC;EACE;EACmCC,OAAmB;IAAnB,KAAAA,OAAO,GAAPA,OAAO;EAE5C;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,WAAWA,CAAA;IACT,IAAG,IAAI,CAACC,iBAAiB,EAAE;MACzB,IAAI,CAACA,iBAAiB,CAACC,WAAW,EAAE;IACtC;EACF;EAEAC,aAAaA,CAAA;IACX,IAAIC,IAAI,GAAG,IAAI;IACfC,UAAU,CAAC,MAAK;MACdD,IAAI,CAACP,OAAO,CAACS,MAAM,GAAG,CAACF,IAAI,CAACG,QAAQ;MACpCH,IAAI,CAACP,OAAO,CAACW,IAAI,GAAGJ,IAAI,CAACG,QAAQ,GAAG,MAAM,GAAG,MAAM;IACrD,CAAC,CAAC;EACJ;EACAR,WAAWA,CAAA;IACT;IACA,IAAI,CAACI,aAAa,EAAE;IACpB;IACA;IACA;IACA;IACA;EACF;EAAC,QAAAM,CAAA,G;qBAlCUd,2BAA2B,EAAAe,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA3BnB,2BAA2B;IAAAoB,SAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}