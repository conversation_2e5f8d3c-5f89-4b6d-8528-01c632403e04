{"ast": null, "code": "import { AppComfirmComponent } from './app-confirm.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nexport class AppConfirmService {\n  constructor(dialog) {\n    this.dialog = dialog;\n  }\n  confirm(data = {}) {\n    data.title = data.title || 'Confirm';\n    data.message = data.message || 'Are you sure?';\n    let dialogRef;\n    dialogRef = this.dialog.open(AppComfirmComponent, {\n      width: '380px',\n      disableClose: true,\n      data: {\n        title: data.title,\n        message: data.message\n      }\n    });\n    return dialogRef.afterClosed();\n  }\n  static #_ = this.ɵfac = function AppConfirmService_Factory(t) {\n    return new (t || AppConfirmService)(i0.ɵɵinject(i1.MatDialog));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AppConfirmService,\n    factory: AppConfirmService.ɵfac\n  });\n}", "map": {"version": 3, "names": ["AppComfirmComponent", "AppConfirmService", "constructor", "dialog", "confirm", "data", "title", "message", "dialogRef", "open", "width", "disableClose", "afterClosed", "_", "i0", "ɵɵinject", "i1", "MatDialog", "_2", "factory", "ɵfac"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\shared\\services\\app-confirm\\app-confirm.service.ts"], "sourcesContent": ["import { Observable } from 'rxjs';\nimport { MatDialogRef, MatDialog } from '@angular/material/dialog';\nimport { Injectable } from '@angular/core';\n\nimport { AppComfirmComponent } from './app-confirm.component';\n\ninterface confirmData {\n  title?: string,\n  message?: string\n}\n\n@Injectable()\nexport class AppConfirmService {\n\n  constructor(private dialog: MatDialog) { }\n\n  public confirm(data:confirmData = {}): Observable<boolean> {\n    data.title = data.title || 'Confirm';\n    data.message = data.message || 'Are you sure?';\n    let dialogRef: MatDialogRef<AppComfirmComponent>;\n    dialogRef = this.dialog.open(AppComfirmComponent, {\n      width: '380px',\n      disableClose: true,\n      data: {title: data.title, message: data.message}\n    });\n    return dialogRef.afterClosed();\n  }\n}"], "mappings": "AAIA,SAASA,mBAAmB,QAAQ,yBAAyB;;;AAQ7D,OAAM,MAAOC,iBAAiB;EAE5BC,YAAoBC,MAAiB;IAAjB,KAAAA,MAAM,GAANA,MAAM;EAAe;EAElCC,OAAOA,CAACC,IAAA,GAAmB,EAAE;IAClCA,IAAI,CAACC,KAAK,GAAGD,IAAI,CAACC,KAAK,IAAI,SAAS;IACpCD,IAAI,CAACE,OAAO,GAAGF,IAAI,CAACE,OAAO,IAAI,eAAe;IAC9C,IAAIC,SAA4C;IAChDA,SAAS,GAAG,IAAI,CAACL,MAAM,CAACM,IAAI,CAACT,mBAAmB,EAAE;MAChDU,KAAK,EAAE,OAAO;MACdC,YAAY,EAAE,IAAI;MAClBN,IAAI,EAAE;QAACC,KAAK,EAAED,IAAI,CAACC,KAAK;QAAEC,OAAO,EAAEF,IAAI,CAACE;MAAO;KAChD,CAAC;IACF,OAAOC,SAAS,CAACI,WAAW,EAAE;EAChC;EAAC,QAAAC,CAAA,G;qBAdUZ,iBAAiB,EAAAa,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,SAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAjBjB,iBAAiB;IAAAkB,OAAA,EAAjBlB,iBAAiB,CAAAmB;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}