{"ast": null, "code": "/*\nLanguage: Tagger Script\nAuthor: <PERSON> <<EMAIL>>\nDescription: Syntax Highlighting for the Tagger Script as used by MusicBrainz Picard.\nWebsite: https://picard.musicbrainz.org\n */\nfunction taggerscript(hljs) {\n  const NOOP = {\n    className: 'comment',\n    begin: /\\$noop\\(/,\n    end: /\\)/,\n    contains: [{\n      begin: /\\\\[()]/\n    }, {\n      begin: /\\(/,\n      end: /\\)/,\n      contains: [{\n        begin: /\\\\[()]/\n      }, 'self']\n    }],\n    relevance: 10\n  };\n  const FUNCTION = {\n    className: 'keyword',\n    begin: /\\$[_a-zA-Z0-9]+(?=\\()/\n  };\n  const VARIABLE = {\n    className: 'variable',\n    begin: /%[_a-zA-Z0-9:]+%/\n  };\n  const ESCAPE_SEQUENCE_UNICODE = {\n    className: 'symbol',\n    begin: /\\\\u[a-fA-F0-9]{4}/\n  };\n  const ESCAPE_SEQUENCE = {\n    className: 'symbol',\n    begin: /\\\\[\\\\nt$%,()]/\n  };\n  return {\n    name: 'Tagger Script',\n    contains: [NOOP, FUNCTION, VARIABLE, ESCAPE_SEQUENCE, ESCAPE_SEQUENCE_UNICODE]\n  };\n}\nmodule.exports = taggerscript;", "map": {"version": 3, "names": ["taggerscript", "hljs", "NOOP", "className", "begin", "end", "contains", "relevance", "FUNCTION", "VARIABLE", "ESCAPE_SEQUENCE_UNICODE", "ESCAPE_SEQUENCE", "name", "module", "exports"], "sources": ["C:/OneDrive/Srikant/SWIP/ClaimsPro/claimspro200-src/claimspro_ui/node_modules/highlight.js/lib/languages/taggerscript.js"], "sourcesContent": ["/*\nLanguage: Tagger Script\nAuthor: <PERSON> <<EMAIL>>\nDescription: Syntax Highlighting for the Tagger Script as used by MusicBrainz Picard.\nWebsite: https://picard.musicbrainz.org\n */\nfunction taggerscript(hljs) {\n  const NOOP = {\n    className: 'comment',\n    begin: /\\$noop\\(/,\n    end: /\\)/,\n    contains: [\n      { begin: /\\\\[()]/ },\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        contains: [\n          { begin: /\\\\[()]/ },\n          'self'\n        ]\n      }\n    ],\n    relevance: 10\n  };\n\n  const FUNCTION = {\n    className: 'keyword',\n    begin: /\\$[_a-zA-Z0-9]+(?=\\()/\n  };\n\n  const VARIABLE = {\n    className: 'variable',\n    begin: /%[_a-zA-Z0-9:]+%/\n  };\n\n  const ESCAPE_SEQUENCE_UNICODE = {\n    className: 'symbol',\n    begin: /\\\\u[a-fA-F0-9]{4}/\n  };\n\n  const ESCAPE_SEQUENCE = {\n    className: 'symbol',\n    begin: /\\\\[\\\\nt$%,()]/\n  };\n\n  return {\n    name: 'Tagger Script',\n    contains: [\n      NOOP,\n      FUNCTION,\n      VARIABLE,\n      ESCAPE_SEQUENCE,\n      ESCAPE_SEQUENCE_UNICODE\n    ]\n  };\n}\n\nmodule.exports = taggerscript;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,YAAYA,CAACC,IAAI,EAAE;EAC1B,MAAMC,IAAI,GAAG;IACXC,SAAS,EAAE,SAAS;IACpBC,KAAK,EAAE,UAAU;IACjBC,GAAG,EAAE,IAAI;IACTC,QAAQ,EAAE,CACR;MAAEF,KAAK,EAAE;IAAS,CAAC,EACnB;MACEA,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,IAAI;MACTC,QAAQ,EAAE,CACR;QAAEF,KAAK,EAAE;MAAS,CAAC,EACnB,MAAM;IAEV,CAAC,CACF;IACDG,SAAS,EAAE;EACb,CAAC;EAED,MAAMC,QAAQ,GAAG;IACfL,SAAS,EAAE,SAAS;IACpBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMK,QAAQ,GAAG;IACfN,SAAS,EAAE,UAAU;IACrBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMM,uBAAuB,GAAG;IAC9BP,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMO,eAAe,GAAG;IACtBR,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EAED,OAAO;IACLQ,IAAI,EAAE,eAAe;IACrBN,QAAQ,EAAE,CACRJ,IAAI,EACJM,QAAQ,EACRC,QAAQ,EACRE,eAAe,EACfD,uBAAuB;EAE3B,CAAC;AACH;AAEAG,MAAM,CAACC,OAAO,GAAGd,YAAY"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}