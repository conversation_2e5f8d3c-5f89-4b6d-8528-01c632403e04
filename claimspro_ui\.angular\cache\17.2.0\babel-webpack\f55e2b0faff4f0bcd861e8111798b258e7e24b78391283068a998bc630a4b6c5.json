{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Tajik [tg]\n//! author : <PERSON><PERSON>. : https://github.com/orif-jr\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var suffixes = {\n    0: '-ум',\n    1: '-ум',\n    2: '-юм',\n    3: '-юм',\n    4: '-ум',\n    5: '-ум',\n    6: '-ум',\n    7: '-ум',\n    8: '-ум',\n    9: '-ум',\n    10: '-ум',\n    12: '-ум',\n    13: '-ум',\n    20: '-ум',\n    30: '-юм',\n    40: '-ум',\n    50: '-ум',\n    60: '-ум',\n    70: '-ум',\n    80: '-ум',\n    90: '-ум',\n    100: '-ум'\n  };\n  var tg = moment.defineLocale('tg', {\n    months: {\n      format: 'январи_феврали_марти_апрели_майи_июни_июли_августи_сентябри_октябри_ноябри_декабри'.split('_'),\n      standalone: 'январ_феврал_март_апрел_май_июн_июл_август_сентябр_октябр_ноябр_декабр'.split('_')\n    },\n    monthsShort: 'янв_фев_мар_апр_май_июн_июл_авг_сен_окт_ноя_дек'.split('_'),\n    weekdays: 'якшанбе_душанбе_сешанбе_чоршанбе_панҷшанбе_ҷумъа_шанбе'.split('_'),\n    weekdaysShort: 'яшб_дшб_сшб_чшб_пшб_ҷум_шнб'.split('_'),\n    weekdaysMin: 'яш_дш_сш_чш_пш_ҷм_шб'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Имрӯз соати] LT',\n      nextDay: '[Фардо соати] LT',\n      lastDay: '[Дирӯз соати] LT',\n      nextWeek: 'dddd[и] [ҳафтаи оянда соати] LT',\n      lastWeek: 'dddd[и] [ҳафтаи гузашта соати] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'баъди %s',\n      past: '%s пеш',\n      s: 'якчанд сония',\n      m: 'як дақиқа',\n      mm: '%d дақиқа',\n      h: 'як соат',\n      hh: '%d соат',\n      d: 'як рӯз',\n      dd: '%d рӯз',\n      M: 'як моҳ',\n      MM: '%d моҳ',\n      y: 'як сол',\n      yy: '%d сол'\n    },\n    meridiemParse: /шаб|субҳ|рӯз|бегоҳ/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'шаб') {\n        return hour < 4 ? hour : hour + 12;\n      } else if (meridiem === 'субҳ') {\n        return hour;\n      } else if (meridiem === 'рӯз') {\n        return hour >= 11 ? hour : hour + 12;\n      } else if (meridiem === 'бегоҳ') {\n        return hour + 12;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'шаб';\n      } else if (hour < 11) {\n        return 'субҳ';\n      } else if (hour < 16) {\n        return 'рӯз';\n      } else if (hour < 19) {\n        return 'бегоҳ';\n      } else {\n        return 'шаб';\n      }\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}-(ум|юм)/,\n    ordinal: function (number) {\n      var a = number % 10,\n        b = number >= 100 ? 100 : null;\n      return number + (suffixes[number] || suffixes[a] || suffixes[b]);\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 1th is the first week of the year.\n    }\n  });\n  return tg;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "suffixes", "tg", "defineLocale", "months", "format", "split", "standalone", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "lastDay", "nextWeek", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "meridiemParse", "meridiemHour", "hour", "meridiem", "minute", "isLower", "dayOfMonthOrdinalParse", "ordinal", "number", "a", "b", "week", "dow", "doy"], "sources": ["C:/OneDrive/Srikant/SWIP/ClaimsPro/claimspro200-src/claimspro_ui/node_modules/moment/locale/tg.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Tajik [tg]\n//! author : <PERSON><PERSON>. : https://github.com/orif-jr\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var suffixes = {\n        0: '-ум',\n        1: '-ум',\n        2: '-юм',\n        3: '-юм',\n        4: '-ум',\n        5: '-ум',\n        6: '-ум',\n        7: '-ум',\n        8: '-ум',\n        9: '-ум',\n        10: '-ум',\n        12: '-ум',\n        13: '-ум',\n        20: '-ум',\n        30: '-юм',\n        40: '-ум',\n        50: '-ум',\n        60: '-ум',\n        70: '-ум',\n        80: '-ум',\n        90: '-ум',\n        100: '-ум',\n    };\n\n    var tg = moment.defineLocale('tg', {\n        months: {\n            format: 'январи_феврали_марти_апрели_майи_июни_июли_августи_сентябри_октябри_ноябри_декабри'.split(\n                '_'\n            ),\n            standalone:\n                'январ_феврал_март_апрел_май_июн_июл_август_сентябр_октябр_ноябр_декабр'.split(\n                    '_'\n                ),\n        },\n        monthsShort: 'янв_фев_мар_апр_май_июн_июл_авг_сен_окт_ноя_дек'.split('_'),\n        weekdays: 'якшанбе_душанбе_сешанбе_чоршанбе_панҷшанбе_ҷумъа_шанбе'.split(\n            '_'\n        ),\n        weekdaysShort: 'яшб_дшб_сшб_чшб_пшб_ҷум_шнб'.split('_'),\n        weekdaysMin: 'яш_дш_сш_чш_пш_ҷм_шб'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd, D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[Имрӯз соати] LT',\n            nextDay: '[Фардо соати] LT',\n            lastDay: '[Дирӯз соати] LT',\n            nextWeek: 'dddd[и] [ҳафтаи оянда соати] LT',\n            lastWeek: 'dddd[и] [ҳафтаи гузашта соати] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'баъди %s',\n            past: '%s пеш',\n            s: 'якчанд сония',\n            m: 'як дақиқа',\n            mm: '%d дақиқа',\n            h: 'як соат',\n            hh: '%d соат',\n            d: 'як рӯз',\n            dd: '%d рӯз',\n            M: 'як моҳ',\n            MM: '%d моҳ',\n            y: 'як сол',\n            yy: '%d сол',\n        },\n        meridiemParse: /шаб|субҳ|рӯз|бегоҳ/,\n        meridiemHour: function (hour, meridiem) {\n            if (hour === 12) {\n                hour = 0;\n            }\n            if (meridiem === 'шаб') {\n                return hour < 4 ? hour : hour + 12;\n            } else if (meridiem === 'субҳ') {\n                return hour;\n            } else if (meridiem === 'рӯз') {\n                return hour >= 11 ? hour : hour + 12;\n            } else if (meridiem === 'бегоҳ') {\n                return hour + 12;\n            }\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 4) {\n                return 'шаб';\n            } else if (hour < 11) {\n                return 'субҳ';\n            } else if (hour < 16) {\n                return 'рӯз';\n            } else if (hour < 19) {\n                return 'бегоҳ';\n            } else {\n                return 'шаб';\n            }\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}-(ум|юм)/,\n        ordinal: function (number) {\n            var a = number % 10,\n                b = number >= 100 ? 100 : null;\n            return number + (suffixes[number] || suffixes[a] || suffixes[b]);\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 1th is the first week of the year.\n        },\n    });\n\n    return tg;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,QAAQ,GAAG;IACX,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,CAAC,EAAE,KAAK;IACR,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,KAAK;IACT,GAAG,EAAE;EACT,CAAC;EAED,IAAIC,EAAE,GAAGF,MAAM,CAACG,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE;MACJC,MAAM,EAAE,oFAAoF,CAACC,KAAK,CAC9F,GACJ,CAAC;MACDC,UAAU,EACN,wEAAwE,CAACD,KAAK,CAC1E,GACJ;IACR,CAAC;IACDE,WAAW,EAAE,iDAAiD,CAACF,KAAK,CAAC,GAAG,CAAC;IACzEG,QAAQ,EAAE,wDAAwD,CAACH,KAAK,CACpE,GACJ,CAAC;IACDI,aAAa,EAAE,6BAA6B,CAACJ,KAAK,CAAC,GAAG,CAAC;IACvDK,WAAW,EAAE,sBAAsB,CAACL,KAAK,CAAC,GAAG,CAAC;IAC9CM,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,kBAAkB;MAC3BC,OAAO,EAAE,kBAAkB;MAC3BC,OAAO,EAAE,kBAAkB;MAC3BC,QAAQ,EAAE,iCAAiC;MAC3CC,QAAQ,EAAE,mCAAmC;MAC7CC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,UAAU;MAClBC,IAAI,EAAE,QAAQ;MACdC,CAAC,EAAE,cAAc;MACjBC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE;IACR,CAAC;IACDC,aAAa,EAAE,oBAAoB;IACnCC,YAAY,EAAE,SAAAA,CAAUC,IAAI,EAAEC,QAAQ,EAAE;MACpC,IAAID,IAAI,KAAK,EAAE,EAAE;QACbA,IAAI,GAAG,CAAC;MACZ;MACA,IAAIC,QAAQ,KAAK,KAAK,EAAE;QACpB,OAAOD,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAGA,IAAI,GAAG,EAAE;MACtC,CAAC,MAAM,IAAIC,QAAQ,KAAK,MAAM,EAAE;QAC5B,OAAOD,IAAI;MACf,CAAC,MAAM,IAAIC,QAAQ,KAAK,KAAK,EAAE;QAC3B,OAAOD,IAAI,IAAI,EAAE,GAAGA,IAAI,GAAGA,IAAI,GAAG,EAAE;MACxC,CAAC,MAAM,IAAIC,QAAQ,KAAK,OAAO,EAAE;QAC7B,OAAOD,IAAI,GAAG,EAAE;MACpB;IACJ,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUD,IAAI,EAAEE,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIH,IAAI,GAAG,CAAC,EAAE;QACV,OAAO,KAAK;MAChB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,MAAM;MACjB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,KAAK;MAChB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,OAAO;MAClB,CAAC,MAAM;QACH,OAAO,KAAK;MAChB;IACJ,CAAC;IACDI,sBAAsB,EAAE,iBAAiB;IACzCC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACvB,IAAIC,CAAC,GAAGD,MAAM,GAAG,EAAE;QACfE,CAAC,GAAGF,MAAM,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI;MAClC,OAAOA,MAAM,IAAI/C,QAAQ,CAAC+C,MAAM,CAAC,IAAI/C,QAAQ,CAACgD,CAAC,CAAC,IAAIhD,QAAQ,CAACiD,CAAC,CAAC,CAAC;IACpE,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOnD,EAAE;AAEb,CAAE,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}