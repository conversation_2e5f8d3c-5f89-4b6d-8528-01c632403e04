{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport makeStyleMapper from './makeStyleMapper.js';\nexport var LINE_STYLE_KEY_MAP = [['lineWidth', 'width'], ['stroke', 'color'], ['opacity'], ['shadowBlur'], ['shadowOffsetX'], ['shadowOffsetY'], ['shadowColor'], ['lineDash', 'type'], ['lineDashOffset', 'dashOffset'], ['lineCap', 'cap'], ['lineJoin', 'join'], ['miterLimit'] // Option decal is in `DecalObject` but style.decal is in `PatternObject`.\n// So do not transfer decal directly.\n];\nvar getLineStyle = makeStyleMapper(LINE_STYLE_KEY_MAP);\nvar LineStyleMixin = /** @class */\nfunction () {\n  function LineStyleMixin() {}\n  LineStyleMixin.prototype.getLineStyle = function (excludes) {\n    return getLineStyle(this, excludes);\n  };\n  return LineStyleMixin;\n}();\n;\nexport { LineStyleMixin };", "map": {"version": 3, "names": ["makeStyleMapper", "LINE_STYLE_KEY_MAP", "getLineStyle", "LineStyleMixin", "prototype", "excludes"], "sources": ["C:/OneDrive/Srikant/SWIP/ClaimsPro/claimspro200-src/claimspro_ui/node_modules/echarts/lib/model/mixin/lineStyle.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport makeStyleMapper from './makeStyleMapper.js';\nexport var LINE_STYLE_KEY_MAP = [['lineWidth', 'width'], ['stroke', 'color'], ['opacity'], ['shadowBlur'], ['shadowOffsetX'], ['shadowOffsetY'], ['shadowColor'], ['lineDash', 'type'], ['lineDashOffset', 'dashOffset'], ['lineCap', 'cap'], ['lineJoin', 'join'], ['miterLimit'] // Option decal is in `DecalObject` but style.decal is in `PatternObject`.\n// So do not transfer decal directly.\n];\nvar getLineStyle = makeStyleMapper(LINE_STYLE_KEY_MAP);\n\nvar LineStyleMixin =\n/** @class */\nfunction () {\n  function LineStyleMixin() {}\n\n  LineStyleMixin.prototype.getLineStyle = function (excludes) {\n    return getLineStyle(this, excludes);\n  };\n\n  return LineStyleMixin;\n}();\n\n;\nexport { LineStyleMixin };"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,eAAe,MAAM,sBAAsB;AAClD,OAAO,IAAIC,kBAAkB,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE,CAAC,gBAAgB,EAAE,YAAY,CAAC,EAAE,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;AACnR;AAAA,CACC;AACD,IAAIC,YAAY,GAAGF,eAAe,CAACC,kBAAkB,CAAC;AAEtD,IAAIE,cAAc,GAClB;AACA,YAAY;EACV,SAASA,cAAcA,CAAA,EAAG,CAAC;EAE3BA,cAAc,CAACC,SAAS,CAACF,YAAY,GAAG,UAAUG,QAAQ,EAAE;IAC1D,OAAOH,YAAY,CAAC,IAAI,EAAEG,QAAQ,CAAC;EACrC,CAAC;EAED,OAAOF,cAAc;AACvB,CAAC,CAAC,CAAC;AAEH;AACA,SAASA,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}