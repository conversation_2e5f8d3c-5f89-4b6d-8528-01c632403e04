{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { STEPPER_GLOBAL_OPTIONS } from '@angular/cdk/stepper';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/input\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@angular/material/stepper\";\nfunction ErrorStepperComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \"Fill out your name\");\n  }\n}\nfunction ErrorStepperComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \"Fill out your address\");\n  }\n}\nfunction ErrorStepperComponent_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \"Done\");\n  }\n}\nexport class ErrorStepperComponent {\n  constructor(_formBuilder) {\n    this._formBuilder = _formBuilder;\n  }\n  ngOnInit() {\n    this.firstFormGroup = this._formBuilder.group({\n      firstCtrl: ['', Validators.required]\n    });\n    this.secondFormGroup = this._formBuilder.group({\n      secondCtrl: ['', Validators.required]\n    });\n  }\n  static #_ = this.ɵfac = function ErrorStepperComponent_Factory(t) {\n    return new (t || ErrorStepperComponent)(i0.ɵɵdirectiveInject(i1.UntypedFormBuilder));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ErrorStepperComponent,\n    selectors: [[\"app-error-stepper\"]],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: STEPPER_GLOBAL_OPTIONS,\n      useValue: {\n        showError: true\n      }\n    }])],\n    decls: 28,\n    vars: 4,\n    consts: [[\"linear\", \"\"], [\"stepper\", \"\"], [\"errorMessage\", \"Name is required.\", 3, \"stepControl\"], [3, \"formGroup\"], [\"matStepLabel\", \"\"], [\"matInput\", \"\", \"placeholder\", \"Last name, First name\", \"formControlName\", \"firstCtrl\", \"required\", \"\"], [\"mat-button\", \"\", \"matStepperNext\", \"\"], [\"errorMessage\", \"Address is required.\", 3, \"stepControl\"], [\"matInput\", \"\", \"placeholder\", \"Address\", \"formControlName\", \"secondCtrl\", \"required\", \"\"], [\"mat-button\", \"\", \"matStepperPrevious\", \"\"], [\"mat-button\", \"\", 3, \"click\"]],\n    template: function ErrorStepperComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r4 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"mat-horizontal-stepper\", 0, 1)(2, \"mat-step\", 2)(3, \"form\", 3);\n        i0.ɵɵtemplate(4, ErrorStepperComponent_ng_template_4_Template, 1, 0, \"ng-template\", 4);\n        i0.ɵɵelementStart(5, \"mat-form-field\");\n        i0.ɵɵelement(6, \"input\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\")(8, \"button\", 6);\n        i0.ɵɵtext(9, \"Next\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(10, \"mat-step\", 7)(11, \"form\", 3);\n        i0.ɵɵtemplate(12, ErrorStepperComponent_ng_template_12_Template, 1, 0, \"ng-template\", 4);\n        i0.ɵɵelementStart(13, \"mat-form-field\");\n        i0.ɵɵelement(14, \"input\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"div\")(16, \"button\", 9);\n        i0.ɵɵtext(17, \"Back\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"button\", 6);\n        i0.ɵɵtext(19, \"Next\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(20, \"mat-step\");\n        i0.ɵɵtemplate(21, ErrorStepperComponent_ng_template_21_Template, 1, 0, \"ng-template\", 4);\n        i0.ɵɵtext(22, \" You are now done. \");\n        i0.ɵɵelementStart(23, \"div\")(24, \"button\", 9);\n        i0.ɵɵtext(25, \"Back\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"button\", 10);\n        i0.ɵɵlistener(\"click\", function ErrorStepperComponent_Template_button_click_26_listener() {\n          i0.ɵɵrestoreView(_r4);\n          const _r0 = i0.ɵɵreference(1);\n          return i0.ɵɵresetView(_r0.reset());\n        });\n        i0.ɵɵtext(27, \"Reset\");\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"stepControl\", ctx.firstFormGroup);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"formGroup\", ctx.firstFormGroup);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"stepControl\", ctx.secondFormGroup);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"formGroup\", ctx.secondFormGroup);\n      }\n    },\n    dependencies: [i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, i2.MatButton, i3.MatInput, i4.MatFormField, i5.MatStep, i5.MatStepLabel, i5.MatStepper, i5.MatStepperNext, i5.MatStepperPrevious],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "STEPPER_GLOBAL_OPTIONS", "i0", "ɵɵtext", "ErrorStepperComponent", "constructor", "_formBuilder", "ngOnInit", "firstFormGroup", "group", "firstCtrl", "required", "secondFormGroup", "secondCtrl", "_", "ɵɵdirectiveInject", "i1", "UntypedFormBuilder", "_2", "selectors", "features", "ɵɵProvidersFeature", "provide", "useValue", "showError", "decls", "vars", "consts", "template", "ErrorStepperComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtemplate", "ErrorStepperComponent_ng_template_4_Template", "ɵɵelement", "ɵɵelementEnd", "ErrorStepperComponent_ng_template_12_Template", "ErrorStepperComponent_ng_template_21_Template", "ɵɵlistener", "ErrorStepperComponent_Template_button_click_26_listener", "ɵɵrestoreView", "_r4", "_r0", "ɵɵreference", "ɵɵresetView", "reset", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\assets\\examples\\material\\error-stepper\\error-stepper.component.ts", "C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\assets\\examples\\material\\error-stepper\\error-stepper.component.html"], "sourcesContent": ["import {Component, OnInit} from '@angular/core';\nimport {UntypedFormBuilder, UntypedFormGroup, Validators} from '@angular/forms';\nimport {STEPPER_GLOBAL_OPTIONS} from '@angular/cdk/stepper';\n\n@Component({\n  selector: 'app-error-stepper',\n  templateUrl: './error-stepper.component.html',\n  styleUrls: ['./error-stepper.component.scss'],\n  providers: [{\n    provide: STEPPER_GLOBAL_OPTIONS, useValue: {showError: true}\n  }]\n})\nexport class ErrorStepperComponent implements OnInit {\n\n  firstFormGroup: UntypedFormGroup;\n  secondFormGroup: UntypedFormGroup;\n\n  constructor(private _formBuilder: UntypedFormBuilder) {}\n\n  ngOnInit() {\n    this.firstFormGroup = this._formBuilder.group({\n      firstCtrl: ['', Validators.required]\n    });\n    this.secondFormGroup = this._formBuilder.group({\n      secondCtrl: ['', Validators.required]\n    });\n  }\n\n}\n", "<mat-horizontal-stepper linear #stepper>\n  <mat-step [stepControl]=\"firstFormGroup\" errorMessage=\"Name is required.\">\n    <form [formGroup]=\"firstFormGroup\">\n      <ng-template matStepLabel>Fill out your name</ng-template>\n      <mat-form-field>\n        <input matInput placeholder=\"Last name, First name\" formControlName=\"firstCtrl\" required>\n      </mat-form-field>\n      <div>\n        <button mat-button matStepperNext>Next</button>\n      </div>\n    </form>\n  </mat-step>\n  <mat-step [stepControl]=\"secondFormGroup\" errorMessage=\"Address is required.\">\n    <form [formGroup]=\"secondFormGroup\">\n      <ng-template matStepLabel>Fill out your address</ng-template>\n      <mat-form-field>\n        <input matInput placeholder=\"Address\" formControlName=\"secondCtrl\" required>\n      </mat-form-field>\n      <div>\n        <button mat-button matStepperPrevious>Back</button>\n        <button mat-button matStepperNext>Next</button>\n      </div>\n    </form>\n  </mat-step>\n  <mat-step>\n    <ng-template matStepLabel>Done</ng-template>\n    You are now done.\n    <div>\n      <button mat-button matStepperPrevious>Back</button>\n      <button mat-button (click)=\"stepper.reset()\">Reset</button>\n    </div>\n  </mat-step>\n</mat-horizontal-stepper>\n"], "mappings": "AACA,SAA8CA,UAAU,QAAO,gBAAgB;AAC/E,SAAQC,sBAAsB,QAAO,sBAAsB;;;;;;;;;ICC3BC,EAAA,CAAAC,MAAA,yBAAkB;;;;;IAWlBD,EAAA,CAAAC,MAAA,4BAAqB;;;;;IAWvBD,EAAA,CAAAC,MAAA,WAAI;;;ADblC,OAAM,MAAOC,qBAAqB;EAKhCC,YAAoBC,YAAgC;IAAhC,KAAAA,YAAY,GAAZA,YAAY;EAAuB;EAEvDC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,GAAG,IAAI,CAACF,YAAY,CAACG,KAAK,CAAC;MAC5CC,SAAS,EAAE,CAAC,EAAE,EAAEV,UAAU,CAACW,QAAQ;KACpC,CAAC;IACF,IAAI,CAACC,eAAe,GAAG,IAAI,CAACN,YAAY,CAACG,KAAK,CAAC;MAC7CI,UAAU,EAAE,CAAC,EAAE,EAAEb,UAAU,CAACW,QAAQ;KACrC,CAAC;EACJ;EAAC,QAAAG,CAAA,G;qBAdUV,qBAAqB,EAAAF,EAAA,CAAAa,iBAAA,CAAAC,EAAA,CAAAC,kBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAArBd,qBAAqB;IAAAe,SAAA;IAAAC,QAAA,GAAAlB,EAAA,CAAAmB,kBAAA,CAJrB,CAAC;MACVC,OAAO,EAAErB,sBAAsB;MAAEsB,QAAQ,EAAE;QAACC,SAAS,EAAE;MAAI;KAC5D,CAAC;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QCVJ5B,EAAA,CAAA8B,cAAA,mCAAwC;QAGlC9B,EAAA,CAAA+B,UAAA,IAAAC,4CAAA,yBAA0D;QAC1DhC,EAAA,CAAA8B,cAAA,qBAAgB;QACd9B,EAAA,CAAAiC,SAAA,eAAyF;QAC3FjC,EAAA,CAAAkC,YAAA,EAAiB;QACjBlC,EAAA,CAAA8B,cAAA,UAAK;QAC+B9B,EAAA,CAAAC,MAAA,WAAI;QAAAD,EAAA,CAAAkC,YAAA,EAAS;QAIrDlC,EAAA,CAAA8B,cAAA,mBAA8E;QAE1E9B,EAAA,CAAA+B,UAAA,KAAAI,6CAAA,yBAA6D;QAC7DnC,EAAA,CAAA8B,cAAA,sBAAgB;QACd9B,EAAA,CAAAiC,SAAA,gBAA4E;QAC9EjC,EAAA,CAAAkC,YAAA,EAAiB;QACjBlC,EAAA,CAAA8B,cAAA,WAAK;QACmC9B,EAAA,CAAAC,MAAA,YAAI;QAAAD,EAAA,CAAAkC,YAAA,EAAS;QACnDlC,EAAA,CAAA8B,cAAA,iBAAkC;QAAA9B,EAAA,CAAAC,MAAA,YAAI;QAAAD,EAAA,CAAAkC,YAAA,EAAS;QAIrDlC,EAAA,CAAA8B,cAAA,gBAAU;QACR9B,EAAA,CAAA+B,UAAA,KAAAK,6CAAA,yBAA4C;QAC5CpC,EAAA,CAAAC,MAAA,2BACA;QAAAD,EAAA,CAAA8B,cAAA,WAAK;QACmC9B,EAAA,CAAAC,MAAA,YAAI;QAAAD,EAAA,CAAAkC,YAAA,EAAS;QACnDlC,EAAA,CAAA8B,cAAA,kBAA6C;QAA1B9B,EAAA,CAAAqC,UAAA,mBAAAC,wDAAA;UAAAtC,EAAA,CAAAuC,aAAA,CAAAC,GAAA;UAAA,MAAAC,GAAA,GAAAzC,EAAA,CAAA0C,WAAA;UAAA,OAAS1C,EAAA,CAAA2C,WAAA,CAAAF,GAAA,CAAAG,KAAA,EAAe;QAAA,EAAC;QAAC5C,EAAA,CAAAC,MAAA,aAAK;QAAAD,EAAA,CAAAkC,YAAA,EAAS;;;QA5BrDlC,EAAA,CAAA6C,SAAA,GAA8B;QAA9B7C,EAAA,CAAA8C,UAAA,gBAAAjB,GAAA,CAAAvB,cAAA,CAA8B;QAChCN,EAAA,CAAA6C,SAAA,EAA4B;QAA5B7C,EAAA,CAAA8C,UAAA,cAAAjB,GAAA,CAAAvB,cAAA,CAA4B;QAU1BN,EAAA,CAAA6C,SAAA,GAA+B;QAA/B7C,EAAA,CAAA8C,UAAA,gBAAAjB,GAAA,CAAAnB,eAAA,CAA+B;QACjCV,EAAA,CAAA6C,SAAA,EAA6B;QAA7B7C,EAAA,CAAA8C,UAAA,cAAAjB,GAAA,CAAAnB,eAAA,CAA6B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}