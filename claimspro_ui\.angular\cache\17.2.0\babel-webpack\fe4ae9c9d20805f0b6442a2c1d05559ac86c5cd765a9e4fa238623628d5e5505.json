{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RelativeTimePipe } from './relative-time.pipe';\nimport { ExcerptPipe } from \"./excerpt.pipe\";\nimport { GetValueByKeyPipe } from './get-value-by-key.pipe';\nimport * as i0 from \"@angular/core\";\nconst pipes = [RelativeTimePipe, ExcerptPipe, GetValueByKeyPipe];\nexport class SharedPipesModule {\n  static #_ = this.ɵfac = function SharedPipesModule_Factory(t) {\n    return new (t || SharedPipesModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: SharedPipesModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SharedPipesModule, {\n    declarations: [RelativeTimePipe, ExcerptPipe, GetValueByKeyPipe],\n    imports: [CommonModule],\n    exports: [RelativeTimePipe, ExcerptPipe, GetValueByKeyPipe]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RelativeTimePipe", "ExcerptPipe", "GetValueByKeyPipe", "pipes", "SharedPipesModule", "_", "_2", "_3", "declarations", "imports", "exports"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\shared\\pipes\\shared-pipes.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nimport { RelativeTimePipe } from './relative-time.pipe';\nimport { ExcerptPipe } from \"./excerpt.pipe\";\nimport { GetValueByKeyPipe } from './get-value-by-key.pipe';\n\nconst pipes = [\n  RelativeTimePipe,\n  ExcerptPipe,\n  GetValueByKeyPipe\n]\n\n@NgModule({\n  imports: [\n    CommonModule\n  ],\n  declarations: pipes,\n  exports: pipes\n})\nexport class SharedPipesModule {}"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,iBAAiB,QAAQ,yBAAyB;;AAE3D,MAAMC,KAAK,GAAG,CACZH,gBAAgB,EAChBC,WAAW,EACXC,iBAAiB,CAClB;AASD,OAAM,MAAOE,iBAAiB;EAAA,QAAAC,CAAA,G;qBAAjBD,iBAAiB;EAAA;EAAA,QAAAE,EAAA,G;UAAjBF;EAAiB;EAAA,QAAAG,EAAA,G;cAL1BR,YAAY;EAAA;;;2EAKHK,iBAAiB;IAAAI,YAAA,GAZ5BR,gBAAgB,EAChBC,WAAW,EACXC,iBAAiB;IAAAO,OAAA,GAKfV,YAAY;IAAAW,OAAA,GAPdV,gBAAgB,EAChBC,WAAW,EACXC,iBAAiB;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}