{"ast": null, "code": "import { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../../../../shared/services/utils.service\";\nimport * as i4 from \"@angular/material/input\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/slide-toggle\";\nimport * as i8 from \"@ngx-translate/core\";\nexport class PayersTablePopupComponent {\n  constructor(data, dialogRef, fb, utilsService) {\n    this.data = data;\n    this.dialogRef = dialogRef;\n    this.fb = fb;\n    this.utilsService = utilsService;\n  }\n  ngOnInit() {\n    this.buildItemForm(this.data.payload);\n    console.log(this.data.createDate);\n  }\n  buildItemForm(item) {\n    this.itemForm = this.fb.group({\n      payerId: [item.payerId || ''],\n      companyId: [item.companyId || ''],\n      groupNumber: [item.groupNumber || ''],\n      clearingHousePayerId: [item.clearingHousePayerId || ''],\n      visible: [this.convertToBoolean(item.visible)],\n      modified: [item.modified || ''],\n      sendPymntToClaimsPro: [this.convertToBoolean(item.sendPymntToClaimsPro)],\n      clearingHouseId: [item.clearingHouseId || ''],\n      networkId: [item.networkId || ''],\n      validateGroupNumber: [this.convertToBoolean(item.validateGroupNumber)],\n      active: [this.convertToBoolean(item.active)]\n    });\n  }\n  submit() {\n    this.itemForm.value.payerId = Number(this.itemForm.value.payerId);\n    this.itemForm.value.networkId = Number(this.itemForm.value.networkId);\n    this.itemForm.value.active = Number(this.itemForm.value.active);\n    this.dialogRef.close(this.itemForm.value);\n  }\n  convertToBoolean(item) {\n    return item !== 0;\n  }\n  static #_ = this.ɵfac = function PayersTablePopupComponent_Factory(t) {\n    return new (t || PayersTablePopupComponent)(i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(i2.UntypedFormBuilder), i0.ɵɵdirectiveInject(i3.UtilsService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PayersTablePopupComponent,\n    selectors: [[\"app-payers-table-popup\"]],\n    decls: 24,\n    vars: 10,\n    consts: [[1, \"p-4\"], [1, \"text-lg\", \"mb-4\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"flex\", \"flex-wrap\", \"flex-col\", \"md:flex-row\"], [1, \"w-full\", \"md:w-1/2\", \"pr-16\"], [1, \"full-width\"], [\"matInput\", \"\", \"name\", \"payerId\", \"placeholder\", \"Payer ID\", 3, \"formControl\"], [\"matInput\", \"\", \"name\", \"groupNumber\", \"placeholder\", \"Group Number\", 3, \"formControl\"], [\"matInput\", \"\", \"name\", \"networkId\", \"placeholder\", \"Network ID\", 3, \"formControl\"], [1, \"w-full\", \"md:w-1/2\", \"!pt-4\", \"pr-16\"], [3, \"formControl\"], [1, \"flex\", \"w-full\", \"mt-4\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"disabled\"], [1, \"flex-grow\"], [\"mat-button\", \"\", \"color\", \"warn\", \"type\", \"button\", 3, \"click\"]],\n    template: function PayersTablePopupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"form\", 2);\n        i0.ɵɵlistener(\"ngSubmit\", function PayersTablePopupComponent_Template_form_ngSubmit_3_listener() {\n          return ctx.submit();\n        });\n        i0.ɵɵelementStart(4, \"div\", 3)(5, \"div\", 4)(6, \"mat-form-field\", 5);\n        i0.ɵɵelement(7, \"input\", 6);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"div\", 4)(9, \"mat-form-field\", 5);\n        i0.ɵɵelement(10, \"input\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 4)(12, \"mat-form-field\", 5);\n        i0.ɵɵelement(13, \"input\", 8);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"div\", 9)(15, \"mat-slide-toggle\", 10);\n        i0.ɵɵtext(16, \"Active User\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"div\", 11)(18, \"button\", 12);\n        i0.ɵɵtext(19);\n        i0.ɵɵpipe(20, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(21, \"span\", 13);\n        i0.ɵɵelementStart(22, \"button\", 14);\n        i0.ɵɵlistener(\"click\", function PayersTablePopupComponent_Template_button_click_22_listener() {\n          return ctx.dialogRef.close(false);\n        });\n        i0.ɵɵtext(23, \"Cancel\");\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.data.title);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"formGroup\", ctx.itemForm);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"formControl\", ctx.itemForm.controls[\"payerId\"]);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"formControl\", ctx.itemForm.controls[\"groupNumber\"]);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"formControl\", ctx.itemForm.controls[\"networkId\"]);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"formControl\", ctx.itemForm.controls[\"active\"]);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"disabled\", ctx.itemForm.invalid);\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(20, 8, \"SAVE\"));\n      }\n    },\n    dependencies: [i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormControlDirective, i2.FormGroupDirective, i4.MatInput, i5.MatFormField, i6.MatButton, i7.MatSlideToggle, i8.TranslatePipe],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["MAT_DIALOG_DATA", "PayersTablePopupComponent", "constructor", "data", "dialogRef", "fb", "utilsService", "ngOnInit", "buildItemForm", "payload", "console", "log", "createDate", "item", "itemForm", "group", "payerId", "companyId", "groupNumber", "clearingHousePayerId", "visible", "convertToBoolean", "modified", "sendPymntToClaimsPro", "clearingHouseId", "networkId", "validateGroupNumber", "active", "submit", "value", "Number", "close", "_", "i0", "ɵɵdirectiveInject", "i1", "MatDialogRef", "i2", "UntypedFormBuilder", "i3", "UtilsService", "_2", "selectors", "decls", "vars", "consts", "template", "PayersTablePopupComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "PayersTablePopupComponent_Template_form_ngSubmit_3_listener", "ɵɵelement", "PayersTablePopupComponent_Template_button_click_22_listener", "ɵɵadvance", "ɵɵtextInterpolate", "title", "ɵɵproperty", "controls", "invalid", "ɵɵpipeBind1"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\views\\payers\\payers-table\\payers-table-popup\\payers-table-popup.component.ts", "C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\views\\payers\\payers-table\\payers-table-popup\\payers-table-popup.component.html"], "sourcesContent": ["import { Component, OnInit, Inject } from '@angular/core';\r\nimport { MatDialogRef as MatDialogRef, MAT_DIALOG_DATA as MAT_DIALOG_DATA } from '@angular/material/dialog';\r\nimport { UntypedFormBuilder, Validators, UntypedFormGroup } from '@angular/forms';\r\nimport { first } from 'rxjs-compat/operator/first';\r\nimport { parse } from 'date-fns';\r\nimport { UtilsService } from '../../../../shared/services/utils.service';\r\n\r\n@Component({\r\n  selector: 'app-payers-table-popup',\r\n  templateUrl: './payers-table-popup.component.html'\r\n})\r\nexport class PayersTablePopupComponent implements OnInit {\r\n  public itemForm: UntypedFormGroup;\r\n  constructor(\r\n    @Inject(MAT_DIALOG_DATA) public data: any,\r\n    public dialogRef: MatDialogRef<PayersTablePopupComponent>,\r\n    private fb: UntypedFormBuilder,\r\n    private utilsService: UtilsService\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.buildItemForm(this.data.payload)\r\n    \r\n    console.log(this.data.createDate)\r\n    \r\n  }\r\n  buildItemForm(item) {\r\n    this.itemForm = this.fb.group({\r\n      payerId: [item.payerId || ''],\r\n      companyId: [item.companyId || ''],\r\n      groupNumber: [item.groupNumber || ''],\r\n      clearingHousePayerId: [item.clearingHousePayerId || ''],\r\n      visible: [this.convertToBoolean(item.visible)],\r\n      modified: [item.modified || ''],\r\n      sendPymntToClaimsPro: [this.convertToBoolean(item.sendPymntToClaimsPro)],\r\n      clearingHouseId: [item.clearingHouseId || ''],\r\n      networkId: [item.networkId || ''],\r\n      validateGroupNumber: [this.convertToBoolean(item.validateGroupNumber)],\r\n      active: [this.convertToBoolean(item.active)],\r\n    })\r\n  }\r\n\r\n  submit() {\r\n    this.itemForm.value.payerId = Number(this.itemForm.value.payerId)\r\n    this.itemForm.value.networkId = Number(this.itemForm.value.networkId)\r\n    this.itemForm.value.active = Number(this.itemForm.value.active)\r\n    this.dialogRef.close(this.itemForm.value)\r\n  }\r\n\r\n  convertToBoolean(item){\r\n\r\n    return item !== 0;\r\n\r\n  }\r\n\r\n}\r\n", "<div class=\"p-4\">\r\n  <h1 class=\"text-lg mb-4\">{{data.title}}</h1>\r\n  \r\n  <form [formGroup]=\"itemForm\" (ngSubmit)=\"submit()\">\r\n    <div class=\"flex flex-wrap flex-col md:flex-row\">\r\n      <div class=\"w-full md:w-1/2 pr-16\">\r\n        <mat-form-field class=\"full-width\">\r\n          <input matInput name=\"payerId\" [formControl]=\"itemForm.controls['payerId']\" placeholder=\"Payer ID\">\r\n        </mat-form-field>\r\n      </div>\r\n\r\n     \r\n        <div class=\"w-full md:w-1/2 pr-16\">\r\n          <mat-form-field class=\"full-width\">\r\n            <input matInput name=\"groupNumber\" [formControl]=\"itemForm.controls['groupNumber']\" placeholder=\"Group Number\">\r\n          </mat-form-field>\r\n        </div>\r\n\r\n    \r\n      <div class=\"w-full md:w-1/2 pr-16\">\r\n        <mat-form-field class=\"full-width\">\r\n          <input matInput name=\"networkId\" [formControl]=\"itemForm.controls['networkId']\" placeholder=\"Network ID\">\r\n        </mat-form-field>\r\n      </div>\r\n\r\n      <div class=\"w-full md:w-1/2 !pt-4 pr-16\">\r\n        <mat-slide-toggle [formControl]=\"itemForm.controls['active']\">Active User</mat-slide-toggle>\r\n      </div>\r\n\r\n      <div class=\"flex w-full mt-4\">\r\n        <button mat-raised-button color=\"primary\" [disabled]=\"itemForm.invalid\">{{\"SAVE\" | translate }}</button>\r\n        <span class=\"flex-grow\"></span>\r\n        <button mat-button color=\"warn\" type=\"button\" (click)=\"dialogRef.close(false)\">Cancel</button>\r\n      </div>\r\n    </div>\r\n  </form>\r\n</div>"], "mappings": "AACA,SAAuCA,eAAkC,QAAQ,0BAA0B;;;;;;;;;;AAU3G,OAAM,MAAOC,yBAAyB;EAEpCC,YACkCC,IAAS,EAClCC,SAAkD,EACjDC,EAAsB,EACtBC,YAA0B;IAHF,KAAAH,IAAI,GAAJA,IAAI;IAC7B,KAAAC,SAAS,GAATA,SAAS;IACR,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,YAAY,GAAZA,YAAY;EAClB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,CAAC,IAAI,CAACL,IAAI,CAACM,OAAO,CAAC;IAErCC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACR,IAAI,CAACS,UAAU,CAAC;EAEnC;EACAJ,aAAaA,CAACK,IAAI;IAChB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACT,EAAE,CAACU,KAAK,CAAC;MAC5BC,OAAO,EAAE,CAACH,IAAI,CAACG,OAAO,IAAI,EAAE,CAAC;MAC7BC,SAAS,EAAE,CAACJ,IAAI,CAACI,SAAS,IAAI,EAAE,CAAC;MACjCC,WAAW,EAAE,CAACL,IAAI,CAACK,WAAW,IAAI,EAAE,CAAC;MACrCC,oBAAoB,EAAE,CAACN,IAAI,CAACM,oBAAoB,IAAI,EAAE,CAAC;MACvDC,OAAO,EAAE,CAAC,IAAI,CAACC,gBAAgB,CAACR,IAAI,CAACO,OAAO,CAAC,CAAC;MAC9CE,QAAQ,EAAE,CAACT,IAAI,CAACS,QAAQ,IAAI,EAAE,CAAC;MAC/BC,oBAAoB,EAAE,CAAC,IAAI,CAACF,gBAAgB,CAACR,IAAI,CAACU,oBAAoB,CAAC,CAAC;MACxEC,eAAe,EAAE,CAACX,IAAI,CAACW,eAAe,IAAI,EAAE,CAAC;MAC7CC,SAAS,EAAE,CAACZ,IAAI,CAACY,SAAS,IAAI,EAAE,CAAC;MACjCC,mBAAmB,EAAE,CAAC,IAAI,CAACL,gBAAgB,CAACR,IAAI,CAACa,mBAAmB,CAAC,CAAC;MACtEC,MAAM,EAAE,CAAC,IAAI,CAACN,gBAAgB,CAACR,IAAI,CAACc,MAAM,CAAC;KAC5C,CAAC;EACJ;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACd,QAAQ,CAACe,KAAK,CAACb,OAAO,GAAGc,MAAM,CAAC,IAAI,CAAChB,QAAQ,CAACe,KAAK,CAACb,OAAO,CAAC;IACjE,IAAI,CAACF,QAAQ,CAACe,KAAK,CAACJ,SAAS,GAAGK,MAAM,CAAC,IAAI,CAAChB,QAAQ,CAACe,KAAK,CAACJ,SAAS,CAAC;IACrE,IAAI,CAACX,QAAQ,CAACe,KAAK,CAACF,MAAM,GAAGG,MAAM,CAAC,IAAI,CAAChB,QAAQ,CAACe,KAAK,CAACF,MAAM,CAAC;IAC/D,IAAI,CAACvB,SAAS,CAAC2B,KAAK,CAAC,IAAI,CAACjB,QAAQ,CAACe,KAAK,CAAC;EAC3C;EAEAR,gBAAgBA,CAACR,IAAI;IAEnB,OAAOA,IAAI,KAAK,CAAC;EAEnB;EAAC,QAAAmB,CAAA,G;qBA1CU/B,yBAAyB,EAAAgC,EAAA,CAAAC,iBAAA,CAG1BlC,eAAe,GAAAiC,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,kBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;UAHdxC,yBAAyB;IAAAyC,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCXtCf,EAAA,CAAAiB,cAAA,aAAiB;QACUjB,EAAA,CAAAkB,MAAA,GAAc;QAAAlB,EAAA,CAAAmB,YAAA,EAAK;QAE5CnB,EAAA,CAAAiB,cAAA,cAAmD;QAAtBjB,EAAA,CAAAoB,UAAA,sBAAAC,4DAAA;UAAA,OAAYL,GAAA,CAAArB,MAAA,EAAQ;QAAA,EAAC;QAChDK,EAAA,CAAAiB,cAAA,aAAiD;QAG3CjB,EAAA,CAAAsB,SAAA,eAAmG;QACrGtB,EAAA,CAAAmB,YAAA,EAAiB;QAIjBnB,EAAA,CAAAiB,cAAA,aAAmC;QAE/BjB,EAAA,CAAAsB,SAAA,gBAA+G;QACjHtB,EAAA,CAAAmB,YAAA,EAAiB;QAIrBnB,EAAA,CAAAiB,cAAA,cAAmC;QAE/BjB,EAAA,CAAAsB,SAAA,gBAAyG;QAC3GtB,EAAA,CAAAmB,YAAA,EAAiB;QAGnBnB,EAAA,CAAAiB,cAAA,cAAyC;QACuBjB,EAAA,CAAAkB,MAAA,mBAAW;QAAAlB,EAAA,CAAAmB,YAAA,EAAmB;QAG9FnB,EAAA,CAAAiB,cAAA,eAA8B;QAC4CjB,EAAA,CAAAkB,MAAA,IAAuB;;QAAAlB,EAAA,CAAAmB,YAAA,EAAS;QACxGnB,EAAA,CAAAsB,SAAA,gBAA+B;QAC/BtB,EAAA,CAAAiB,cAAA,kBAA+E;QAAjCjB,EAAA,CAAAoB,UAAA,mBAAAG,4DAAA;UAAA,OAASP,GAAA,CAAA7C,SAAA,CAAA2B,KAAA,CAAgB,KAAK,CAAC;QAAA,EAAC;QAACE,EAAA,CAAAkB,MAAA,cAAM;QAAAlB,EAAA,CAAAmB,YAAA,EAAS;;;QA/B3EnB,EAAA,CAAAwB,SAAA,GAAc;QAAdxB,EAAA,CAAAyB,iBAAA,CAAAT,GAAA,CAAA9C,IAAA,CAAAwD,KAAA,CAAc;QAEjC1B,EAAA,CAAAwB,SAAA,EAAsB;QAAtBxB,EAAA,CAAA2B,UAAA,cAAAX,GAAA,CAAAnC,QAAA,CAAsB;QAIWmB,EAAA,CAAAwB,SAAA,GAA4C;QAA5CxB,EAAA,CAAA2B,UAAA,gBAAAX,GAAA,CAAAnC,QAAA,CAAA+C,QAAA,YAA4C;QAOtC5B,EAAA,CAAAwB,SAAA,GAAgD;QAAhDxB,EAAA,CAAA2B,UAAA,gBAAAX,GAAA,CAAAnC,QAAA,CAAA+C,QAAA,gBAAgD;QAOpD5B,EAAA,CAAAwB,SAAA,GAA8C;QAA9CxB,EAAA,CAAA2B,UAAA,gBAAAX,GAAA,CAAAnC,QAAA,CAAA+C,QAAA,cAA8C;QAK/D5B,EAAA,CAAAwB,SAAA,GAA2C;QAA3CxB,EAAA,CAAA2B,UAAA,gBAAAX,GAAA,CAAAnC,QAAA,CAAA+C,QAAA,WAA2C;QAInB5B,EAAA,CAAAwB,SAAA,GAA6B;QAA7BxB,EAAA,CAAA2B,UAAA,aAAAX,GAAA,CAAAnC,QAAA,CAAAgD,OAAA,CAA6B;QAAC7B,EAAA,CAAAwB,SAAA,EAAuB;QAAvBxB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAA8B,WAAA,gBAAuB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}