{"ast": null, "code": "import { CommonModule } from \"@angular/common\";\nimport { OrderRoutingModule } from \"./order-routing.module\";\nimport { OrderListComponent } from \"./order-list/order-list.component\";\nimport { OrderDetailComponent } from \"./order-detail/order-detail.component\";\nimport { OrderCostListComponent } from \"./order-cost-list/order-cost-list.component\";\nimport { SharedMaterialModule } from \"app/shared/shared-material.module\";\nimport * as i0 from \"@angular/core\";\nexport class OrderModule {\n  static #_ = this.ɵfac = function OrderModule_Factory(t) {\n    return new (t || OrderModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: OrderModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, SharedMaterialModule, OrderRoutingModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(OrderModule, {\n    declarations: [OrderListComponent, OrderDetailComponent, OrderCostListComponent],\n    imports: [CommonModule, SharedMaterialModule, OrderRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "OrderRoutingModule", "OrderListComponent", "OrderDetailComponent", "OrderCostListComponent", "SharedMaterialModule", "OrderModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\views\\order\\order.module.ts"], "sourcesContent": ["import { NgModule } from \"@angular/core\";\nimport { CommonModule } from \"@angular/common\";\n\nimport { OrderRoutingModule } from \"./order-routing.module\";\nimport { OrderListComponent } from \"./order-list/order-list.component\";\nimport { OrderDetailComponent } from \"./order-detail/order-detail.component\";\nimport { OrderCostListComponent } from \"./order-cost-list/order-cost-list.component\";\nimport { SharedMaterialModule } from \"app/shared/shared-material.module\";\n\n@NgModule({\n  declarations: [\n    OrderListComponent,\n    OrderDetailComponent,\n    OrderCostListComponent\n  ],\n  imports: [CommonModule, SharedMaterialModule, OrderRoutingModule]\n})\nexport class OrderModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,sBAAsB,QAAQ,6CAA6C;AACpF,SAASC,oBAAoB,QAAQ,mCAAmC;;AAUxE,OAAM,MAAOC,WAAW;EAAA,QAAAC,CAAA,G;qBAAXD,WAAW;EAAA;EAAA,QAAAE,EAAA,G;UAAXF;EAAW;EAAA,QAAAG,EAAA,G;cAFZT,YAAY,EAAEK,oBAAoB,EAAEJ,kBAAkB;EAAA;;;2EAErDK,WAAW;IAAAI,YAAA,GANpBR,kBAAkB,EAClBC,oBAAoB,EACpBC,sBAAsB;IAAAO,OAAA,GAEdX,YAAY,EAAEK,oBAAoB,EAAEJ,kBAAkB;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}