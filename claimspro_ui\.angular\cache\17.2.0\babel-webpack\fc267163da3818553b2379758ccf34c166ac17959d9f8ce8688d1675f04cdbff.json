{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class EgretSidenavHelperService {\n  constructor() {\n    this.sidenavList = [];\n  }\n  setSidenav(id, sidenav) {\n    this.sidenavList[id] = sidenav;\n  }\n  getSidenav(id) {\n    return this.sidenavList[id];\n  }\n  static #_ = this.ɵfac = function EgretSidenavHelperService_Factory(t) {\n    return new (t || EgretSidenavHelperService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: EgretSidenavHelperService,\n    factory: EgretSidenavHelperService.ɵfac,\n    providedIn: \"root\"\n  });\n}", "map": {"version": 3, "names": ["EgretSidenavHelperService", "constructor", "sidenavList", "<PERSON><PERSON><PERSON><PERSON>", "id", "sidenav", "getSidenav", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\shared\\directives\\egret-sidenav-helper\\egret-sidenav-helper.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\";\nimport { MatSidenav } from \"@angular/material/sidenav\";\n\n@Injectable({\n  providedIn: \"root\"\n})\nexport class EgretSidenavHelperService {\n  sidenavList: MatSidenav[];\n\n  constructor() {\n    this.sidenavList = [];\n  }\n\n  setSidenav(id, sidenav): void {\n    this.sidenavList[id] = sidenav;\n  }\n\n  getSidenav(id): any {\n    return this.sidenavList[id];\n  }\n}\n"], "mappings": ";AAMA,OAAM,MAAOA,yBAAyB;EAGpCC,YAAA;IACE,IAAI,CAACC,WAAW,GAAG,EAAE;EACvB;EAEAC,UAAUA,CAACC,EAAE,EAAEC,OAAO;IACpB,IAAI,CAACH,WAAW,CAACE,EAAE,CAAC,GAAGC,OAAO;EAChC;EAEAC,UAAUA,CAACF,EAAE;IACX,OAAO,IAAI,CAACF,WAAW,CAACE,EAAE,CAAC;EAC7B;EAAC,QAAAG,CAAA,G;qBAbUP,yBAAyB;EAAA;EAAA,QAAAQ,EAAA,G;WAAzBR,yBAAyB;IAAAS,OAAA,EAAzBT,yBAAyB,CAAAU,IAAA;IAAAC,UAAA,EAFxB;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}