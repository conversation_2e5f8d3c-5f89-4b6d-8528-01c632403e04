{"ast": null, "code": "import { UntypedFormControl } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@angular/cdk/scrolling\";\nimport * as i6 from \"@angular/material/core\";\nimport * as i7 from \"@angular/material/select\";\nimport * as i8 from \"@angular/material/tooltip\";\nfunction AutoHideTooltipComponent_mat_option_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 5);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const positionOption_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", positionOption_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", positionOption_r2, \" \");\n  }\n}\nexport class AutoHideTooltipComponent {\n  constructor() {\n    this.positionOptions = ['below', 'above', 'left', 'right'];\n    this.position = new UntypedFormControl(this.positionOptions[0]);\n  }\n  ngOnInit() {}\n  static #_ = this.ɵfac = function AutoHideTooltipComponent_Factory(t) {\n    return new (t || AutoHideTooltipComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AutoHideTooltipComponent,\n    selectors: [[\"app-auto-hide-tooltip\"]],\n    decls: 7,\n    vars: 3,\n    consts: [[\"placeholder\", \"Tooltip position\", 3, \"formControl\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"cdk-scrollable\", \"\", 1, \"example-container\"], [\"mat-raised-button\", \"\", \"matTooltip\", \"Info about the action\", \"matTooltipHideDelay\", \"100000\", \"aria-label\", \"Button that displays a tooltip that hides when scrolled out of the container\", 1, \"example-button\", 3, \"matTooltipPosition\"], [\"tooltip\", \"matTooltip\"], [3, \"value\"]],\n    template: function AutoHideTooltipComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"mat-form-field\")(1, \"mat-select\", 0);\n        i0.ɵɵtemplate(2, AutoHideTooltipComponent_mat_option_2_Template, 2, 2, \"mat-option\", 1);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(3, \"div\", 2)(4, \"button\", 3, 4);\n        i0.ɵɵtext(6, \" Action \");\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"formControl\", ctx.position);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngForOf\", ctx.positionOptions);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"matTooltipPosition\", ctx.position.value);\n      }\n    },\n    dependencies: [i1.NgForOf, i2.NgControlStatus, i2.FormControlDirective, i3.MatButton, i4.MatFormField, i5.CdkScrollable, i6.MatOption, i7.MatSelect, i8.MatTooltip],\n    styles: [\".example-button[_ngcontent-%COMP%] {\\n  display: block;\\n  margin: 80px auto 400px;\\n}\\n\\n.example-container[_ngcontent-%COMP%] {\\n  height: 200px;\\n  overflow: auto;\\n  border: 1px solid #ccc;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hc3NldHMvZXhhbXBsZXMvbWF0ZXJpYWwvYXV0by1oaWRlLXRvb2x0aXAvYXV0by1oaWRlLXRvb2x0aXAuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSxjQUFBO0VBQ0EsdUJBQUE7QUFDSjs7QUFFRTtFQUNFLGFBQUE7RUFDQSxjQUFBO0VBQ0Esc0JBQUE7QUFDSiIsInNvdXJjZXNDb250ZW50IjpbIi5leGFtcGxlLWJ1dHRvbiB7XG4gICAgZGlzcGxheTogYmxvY2s7XG4gICAgbWFyZ2luOiA4MHB4IGF1dG8gNDAwcHg7XG4gIH1cbiAgXG4gIC5leGFtcGxlLWNvbnRhaW5lciB7XG4gICAgaGVpZ2h0OiAyMDBweDtcbiAgICBvdmVyZmxvdzogYXV0bztcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjY2NjO1xuICB9XG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["UntypedFormControl", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "positionOption_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "AutoHideTooltipComponent", "constructor", "positionOptions", "position", "ngOnInit", "_", "_2", "selectors", "decls", "vars", "consts", "template", "AutoHideTooltipComponent_Template", "rf", "ctx", "ɵɵtemplate", "AutoHideTooltipComponent_mat_option_2_Template", "value"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\assets\\examples\\material\\auto-hide-tooltip\\auto-hide-tooltip.component.ts", "C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\assets\\examples\\material\\auto-hide-tooltip\\auto-hide-tooltip.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport {UntypedFormControl} from '@angular/forms';\nimport { TooltipPosition } from '@angular/material/tooltip';\n\n@Component({\n  selector: 'app-auto-hide-tooltip',\n  templateUrl: './auto-hide-tooltip.component.html',\n  styleUrls: ['./auto-hide-tooltip.component.scss']\n})\nexport class AutoHideTooltipComponent implements OnInit {\n\n  positionOptions: TooltipPosition[] = ['below', 'above', 'left', 'right'];\n  position = new UntypedFormControl(this.positionOptions[0]);\n  \n  constructor() { }\n\n  ngOnInit() {\n  }\n\n}\n", "<mat-form-field>\n  <mat-select placeholder=\"Tooltip position\" [formControl]=\"position\">\n    <mat-option *ngFor=\"let positionOption of positionOptions\" [value]=\"positionOption\">\n      {{positionOption}}\n    </mat-option>\n  </mat-select>\n</mat-form-field>\n\n<div class=\"example-container\" cdk-scrollable>\n  <button mat-raised-button #tooltip=\"matTooltip\"\n          matTooltip=\"Info about the action\"\n          [matTooltipPosition]=\"position.value\"\n          matTooltipHideDelay=\"100000\"\n          aria-label=\"Button that displays a tooltip that hides when scrolled out of the container\"\n          class=\"example-button\">\n    Action\n  </button>\n</div>\n"], "mappings": "AACA,SAAQA,kBAAkB,QAAO,gBAAgB;;;;;;;;;;;;ICC7CC,EAAA,CAAAC,cAAA,oBAAoF;IAClFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF8CH,EAAA,CAAAI,UAAA,UAAAC,iBAAA,CAAwB;IACjFL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,iBAAA,MACF;;;ADKJ,OAAM,MAAOG,wBAAwB;EAKnCC,YAAA;IAHA,KAAAC,eAAe,GAAsB,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;IACxE,KAAAC,QAAQ,GAAG,IAAIZ,kBAAkB,CAAC,IAAI,CAACW,eAAe,CAAC,CAAC,CAAC,CAAC;EAE1C;EAEhBE,QAAQA,CAAA,GACR;EAAC,QAAAC,CAAA,G;qBARUL,wBAAwB;EAAA;EAAA,QAAAM,EAAA,G;UAAxBN,wBAAwB;IAAAO,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCTrCrB,EAAA,CAAAC,cAAA,qBAAgB;QAEZD,EAAA,CAAAuB,UAAA,IAAAC,8CAAA,wBAEa;QACfxB,EAAA,CAAAG,YAAA,EAAa;QAGfH,EAAA,CAAAC,cAAA,aAA8C;QAO1CD,EAAA,CAAAE,MAAA,eACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;QAfkCH,EAAA,CAAAM,SAAA,EAAwB;QAAxBN,EAAA,CAAAI,UAAA,gBAAAkB,GAAA,CAAAX,QAAA,CAAwB;QAC1BX,EAAA,CAAAM,SAAA,EAAkB;QAAlBN,EAAA,CAAAI,UAAA,YAAAkB,GAAA,CAAAZ,eAAA,CAAkB;QASnDV,EAAA,CAAAM,SAAA,GAAqC;QAArCN,EAAA,CAAAI,UAAA,uBAAAkB,GAAA,CAAAX,QAAA,CAAAc,KAAA,CAAqC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}