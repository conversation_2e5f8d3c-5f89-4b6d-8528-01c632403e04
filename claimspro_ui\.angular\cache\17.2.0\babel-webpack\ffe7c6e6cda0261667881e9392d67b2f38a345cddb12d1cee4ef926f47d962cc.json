{"ast": null, "code": "import { AnalyticsComponent } from './analytics/analytics.component';\nimport { DashboardDarkComponent } from './dashboard-dark/dashboard-dark.component';\nimport { CryptocurrencyComponent } from './cryptocurrency/cryptocurrency.component';\nimport { LearningManagementComponent } from './learning-management/learning-management.component';\nimport { AnalyticsAltComponent } from './analytics-alt/analytics-alt.component';\nexport const DashboardRoutes = [{\n  path: 'learning-management',\n  component: LearningManagementComponent,\n  data: {\n    title: 'Learning management',\n    breadcrumb: 'Learning management'\n  }\n}, {\n  path: 'analytics',\n  component: AnalyticsComponent,\n  data: {\n    title: 'Analytics',\n    breadcrumb: 'Analytics'\n  }\n}, {\n  path: 'analytics-alt',\n  component: AnalyticsAltComponent,\n  data: {\n    title: 'Analytics Alternative',\n    breadcrumb: 'Analytics Alternative'\n  }\n}, {\n  path: 'crypto',\n  component: CryptocurrencyComponent,\n  data: {\n    title: 'Cryptocurrency',\n    breadcrumb: 'Cryptocurrency'\n  }\n}, {\n  path: 'dark',\n  component: DashboardDarkComponent,\n  data: {\n    title: 'Dark Cards',\n    breadcrumb: 'Dark Cards'\n  }\n}];", "map": {"version": 3, "names": ["AnalyticsComponent", "DashboardDarkComponent", "CryptocurrencyComponent", "LearningManagementComponent", "AnalyticsAltComponent", "DashboardRoutes", "path", "component", "data", "title", "breadcrumb"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\views\\dashboard\\dashboard.routing.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\n\nimport { AnalyticsComponent } from './analytics/analytics.component';\nimport { DashboardDarkComponent } from './dashboard-dark/dashboard-dark.component';\nimport { CryptocurrencyComponent } from './cryptocurrency/cryptocurrency.component';\nimport { DefaultDashboardComponent } from './default-dashboard/default-dashboard.component';\nimport { UserRoleGuard } from 'app/shared/guards/user-role.guard';\nimport { LearningManagementComponent } from './learning-management/learning-management.component';\nimport { AnalyticsAltComponent } from './analytics-alt/analytics-alt.component';\nimport { config } from 'config';\n\nexport const DashboardRoutes: Routes = [\n  {\n    path: 'learning-management',\n    component: LearningManagementComponent,\n    data: { title: 'Learning management', breadcrumb: 'Learning management' }\n  },\n  {\n    path: 'analytics',\n    component: AnalyticsComponent,\n    data: { title: 'Analytics', breadcrumb: 'Analytics' }\n  },\n  {\n    path: 'analytics-alt',\n    component: AnalyticsAltComponent,\n    data: { title: 'Analytics Alternative', breadcrumb: 'Analytics Alternative' }\n  },\n  {\n    path: 'crypto',\n    component: CryptocurrencyComponent,\n    data: { title: 'Cryptocurrency', breadcrumb: 'Cryptocurrency' }\n  },\n  {\n    path: 'dark',\n    component: DashboardDarkComponent,\n    data: { title: 'Dark Cards', breadcrumb: 'Dark Cards' }\n  }\n];\n"], "mappings": "AAEA,SAASA,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,uBAAuB,QAAQ,2CAA2C;AAGnF,SAASC,2BAA2B,QAAQ,qDAAqD;AACjG,SAASC,qBAAqB,QAAQ,yCAAyC;AAG/E,OAAO,MAAMC,eAAe,GAAW,CACrC;EACEC,IAAI,EAAE,qBAAqB;EAC3BC,SAAS,EAAEJ,2BAA2B;EACtCK,IAAI,EAAE;IAAEC,KAAK,EAAE,qBAAqB;IAAEC,UAAU,EAAE;EAAqB;CACxE,EACD;EACEJ,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEP,kBAAkB;EAC7BQ,IAAI,EAAE;IAAEC,KAAK,EAAE,WAAW;IAAEC,UAAU,EAAE;EAAW;CACpD,EACD;EACEJ,IAAI,EAAE,eAAe;EACrBC,SAAS,EAAEH,qBAAqB;EAChCI,IAAI,EAAE;IAAEC,KAAK,EAAE,uBAAuB;IAAEC,UAAU,EAAE;EAAuB;CAC5E,EACD;EACEJ,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAEL,uBAAuB;EAClCM,IAAI,EAAE;IAAEC,KAAK,EAAE,gBAAgB;IAAEC,UAAU,EAAE;EAAgB;CAC9D,EACD;EACEJ,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEN,sBAAsB;EACjCO,IAAI,EAAE;IAAEC,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAE;EAAY;CACtD,CACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}