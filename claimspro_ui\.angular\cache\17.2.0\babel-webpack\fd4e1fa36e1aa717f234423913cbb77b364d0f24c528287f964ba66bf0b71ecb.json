{"ast": null, "code": "/*\nLanguage: DNS Zone\nAuthor: <PERSON> <<EMAIL>>\nCategory: config\nWebsite: https://en.wikipedia.org/wiki/Zone_file\n*/\n\n/** @type LanguageFn */\nfunction dns(hljs) {\n  const KEYWORDS = [\"IN\", \"A\", \"AAAA\", \"AFSDB\", \"APL\", \"CAA\", \"CDNSKEY\", \"CDS\", \"CERT\", \"CNAME\", \"DHCID\", \"DLV\", \"DNAME\", \"DNSKEY\", \"DS\", \"HIP\", \"IPSECKEY\", \"KEY\", \"KX\", \"LOC\", \"MX\", \"NAPTR\", \"NS\", \"NSEC\", \"NSEC3\", \"NSEC3PARAM\", \"PTR\", \"RRSIG\", \"RP\", \"SIG\", \"SOA\", \"SRV\", \"SSHFP\", \"TA\", \"TKEY\", \"TLSA\", \"TSIG\", \"TXT\"];\n  return {\n    name: 'DNS Zone',\n    aliases: ['bind', 'zone'],\n    keywords: KEYWORDS,\n    contains: [hljs.COMMENT(';', '$', {\n      relevance: 0\n    }), {\n      className: 'meta',\n      begin: /^\\$(TTL|GENERATE|INCLUDE|ORIGIN)\\b/\n    },\n    // IPv6\n    {\n      className: 'number',\n      begin: '((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3}))|:)))\\\\b'\n    },\n    // IPv4\n    {\n      className: 'number',\n      begin: '((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\\\b'\n    }, hljs.inherit(hljs.NUMBER_MODE, {\n      begin: /\\b\\d+[dhwm]?/\n    })]\n  };\n}\nmodule.exports = dns;", "map": {"version": 3, "names": ["dns", "hljs", "KEYWORDS", "name", "aliases", "keywords", "contains", "COMMENT", "relevance", "className", "begin", "inherit", "NUMBER_MODE", "module", "exports"], "sources": ["C:/OneDrive/Srikant/SWIP/ClaimsPro/claimspro200-src/claimspro_ui/node_modules/highlight.js/lib/languages/dns.js"], "sourcesContent": ["/*\nLanguage: DNS Zone\nAuthor: <PERSON> <<EMAIL>>\nCategory: config\nWebsite: https://en.wikipedia.org/wiki/Zone_file\n*/\n\n/** @type LanguageFn */\nfunction dns(hljs) {\n  const KEYWORDS = [\n    \"IN\",\n    \"A\",\n    \"AAAA\",\n    \"AFSDB\",\n    \"APL\",\n    \"CAA\",\n    \"CDNSKEY\",\n    \"CDS\",\n    \"CERT\",\n    \"CNAME\",\n    \"DHCID\",\n    \"DLV\",\n    \"DNAME\",\n    \"DNSKEY\",\n    \"DS\",\n    \"HIP\",\n    \"IPSECKEY\",\n    \"KEY\",\n    \"KX\",\n    \"LOC\",\n    \"MX\",\n    \"NAPTR\",\n    \"NS\",\n    \"NSEC\",\n    \"NSEC3\",\n    \"NSEC3PARAM\",\n    \"PTR\",\n    \"RRSIG\",\n    \"RP\",\n    \"SIG\",\n    \"SOA\",\n    \"SRV\",\n    \"SSHFP\",\n    \"TA\",\n    \"TKEY\",\n    \"TLSA\",\n    \"TSIG\",\n    \"TXT\"\n  ];\n  return {\n    name: 'DNS Zone',\n    aliases: [\n      'bind',\n      'zone'\n    ],\n    keywords: KEYWORDS,\n    contains: [\n      hljs.COMMENT(';', '$', { relevance: 0 }),\n      {\n        className: 'meta',\n        begin: /^\\$(TTL|GENERATE|INCLUDE|ORIGIN)\\b/\n      },\n      // IPv6\n      {\n        className: 'number',\n        begin: '((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]?\\\\d)){3}))|:)))\\\\b'\n      },\n      // IPv4\n      {\n        className: 'number',\n        begin: '((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\\\b'\n      },\n      hljs.inherit(hljs.NUMBER_MODE, { begin: /\\b\\d+[dhwm]?/ })\n    ]\n  };\n}\n\nmodule.exports = dns;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB,MAAMC,QAAQ,GAAG,CACf,IAAI,EACJ,GAAG,EACH,MAAM,EACN,OAAO,EACP,KAAK,EACL,KAAK,EACL,SAAS,EACT,KAAK,EACL,MAAM,EACN,OAAO,EACP,OAAO,EACP,KAAK,EACL,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,UAAU,EACV,KAAK,EACL,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,MAAM,EACN,OAAO,EACP,YAAY,EACZ,KAAK,EACL,OAAO,EACP,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,OAAO,EACP,IAAI,EACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,KAAK,CACN;EACD,OAAO;IACLC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,CACP,MAAM,EACN,MAAM,CACP;IACDC,QAAQ,EAAEH,QAAQ;IAClBI,QAAQ,EAAE,CACRL,IAAI,CAACM,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE;MAAEC,SAAS,EAAE;IAAE,CAAC,CAAC,EACxC;MACEC,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE;IACT,CAAC;IACD;IACA;MACED,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE;IACT,CAAC;IACD;IACA;MACED,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE;IACT,CAAC,EACDT,IAAI,CAACU,OAAO,CAACV,IAAI,CAACW,WAAW,EAAE;MAAEF,KAAK,EAAE;IAAe,CAAC,CAAC;EAE7D,CAAC;AACH;AAEAG,MAAM,CAACC,OAAO,GAAGd,GAAG"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}