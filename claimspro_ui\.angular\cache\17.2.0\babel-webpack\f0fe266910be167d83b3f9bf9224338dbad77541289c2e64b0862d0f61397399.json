{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport createSeriesData from '../helper/createSeriesData.js';\nimport SeriesModel from '../../model/Series.js';\nvar EffectScatterSeriesModel = /** @class */\nfunction (_super) {\n  __extends(EffectScatterSeriesModel, _super);\n  function EffectScatterSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = EffectScatterSeriesModel.type;\n    _this.hasSymbolVisual = true;\n    return _this;\n  }\n  EffectScatterSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesData(null, this, {\n      useEncodeDefaulter: true\n    });\n  };\n  EffectScatterSeriesModel.prototype.brushSelector = function (dataIndex, data, selectors) {\n    return selectors.point(data.getItemLayout(dataIndex));\n  };\n  EffectScatterSeriesModel.type = 'series.effectScatter';\n  EffectScatterSeriesModel.dependencies = ['grid', 'polar'];\n  EffectScatterSeriesModel.defaultOption = {\n    coordinateSystem: 'cartesian2d',\n    // zlevel: 0,\n    z: 2,\n    legendHoverLink: true,\n    effectType: 'ripple',\n    progressive: 0,\n    // When to show the effect, option: 'render'|'emphasis'\n    showEffectOn: 'render',\n    clip: true,\n    // Ripple effect config\n    rippleEffect: {\n      period: 4,\n      // Scale of ripple\n      scale: 2.5,\n      // Brush type can be fill or stroke\n      brushType: 'fill',\n      // Ripple number\n      number: 3\n    },\n    universalTransition: {\n      divideShape: 'clone'\n    },\n    // Cartesian coordinate system\n    // xAxisIndex: 0,\n    // yAxisIndex: 0,\n    // Polar coordinate system\n    // polarIndex: 0,\n    // Geo coordinate system\n    // geoIndex: 0,\n    // symbol: null,        // 图形类型\n    symbolSize: 10 // 图形大小，半宽（半径）参数，当图形为方向或菱形则总宽度为symbolSize * 2\n    // symbolRotate: null,  // 图形旋转控制\n    // itemStyle: {\n    //     opacity: 1\n    // }\n  };\n  return EffectScatterSeriesModel;\n}(SeriesModel);\nexport default EffectScatterSeriesModel;", "map": {"version": 3, "names": ["__extends", "createSeriesData", "SeriesModel", "EffectScatterSeriesModel", "_super", "_this", "apply", "arguments", "type", "hasSymbolVisual", "prototype", "getInitialData", "option", "ecModel", "useEncodeDefaulter", "brushSelector", "dataIndex", "data", "selectors", "point", "getItemLayout", "dependencies", "defaultOption", "coordinateSystem", "z", "legendHoverLink", "effectType", "progressive", "showEffectOn", "clip", "rippleEffect", "period", "scale", "brushType", "number", "universalTransition", "divideShape", "symbolSize"], "sources": ["C:/OneDrive/Srikant/SWIP/ClaimsPro/claimspro200-src/claimspro_ui/node_modules/echarts/lib/chart/effectScatter/EffectScatterSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport createSeriesData from '../helper/createSeriesData.js';\nimport SeriesModel from '../../model/Series.js';\n\nvar EffectScatterSeriesModel =\n/** @class */\nfunction (_super) {\n  __extends(EffectScatterSeriesModel, _super);\n\n  function EffectScatterSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n\n    _this.type = EffectScatterSeriesModel.type;\n    _this.hasSymbolVisual = true;\n    return _this;\n  }\n\n  EffectScatterSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesData(null, this, {\n      useEncodeDefaulter: true\n    });\n  };\n\n  EffectScatterSeriesModel.prototype.brushSelector = function (dataIndex, data, selectors) {\n    return selectors.point(data.getItemLayout(dataIndex));\n  };\n\n  EffectScatterSeriesModel.type = 'series.effectScatter';\n  EffectScatterSeriesModel.dependencies = ['grid', 'polar'];\n  EffectScatterSeriesModel.defaultOption = {\n    coordinateSystem: 'cartesian2d',\n    // zlevel: 0,\n    z: 2,\n    legendHoverLink: true,\n    effectType: 'ripple',\n    progressive: 0,\n    // When to show the effect, option: 'render'|'emphasis'\n    showEffectOn: 'render',\n    clip: true,\n    // Ripple effect config\n    rippleEffect: {\n      period: 4,\n      // Scale of ripple\n      scale: 2.5,\n      // Brush type can be fill or stroke\n      brushType: 'fill',\n      // Ripple number\n      number: 3\n    },\n    universalTransition: {\n      divideShape: 'clone'\n    },\n    // Cartesian coordinate system\n    // xAxisIndex: 0,\n    // yAxisIndex: 0,\n    // Polar coordinate system\n    // polarIndex: 0,\n    // Geo coordinate system\n    // geoIndex: 0,\n    // symbol: null,        // 图形类型\n    symbolSize: 10 // 图形大小，半宽（半径）参数，当图形为方向或菱形则总宽度为symbolSize * 2\n    // symbolRotate: null,  // 图形旋转控制\n    // itemStyle: {\n    //     opacity: 1\n    // }\n\n  };\n  return EffectScatterSeriesModel;\n}(SeriesModel);\n\nexport default EffectScatterSeriesModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,WAAW,MAAM,uBAAuB;AAE/C,IAAIC,wBAAwB,GAC5B;AACA,UAAUC,MAAM,EAAE;EAChBJ,SAAS,CAACG,wBAAwB,EAAEC,MAAM,CAAC;EAE3C,SAASD,wBAAwBA,CAAA,EAAG;IAClC,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IAEpEF,KAAK,CAACG,IAAI,GAAGL,wBAAwB,CAACK,IAAI;IAC1CH,KAAK,CAACI,eAAe,GAAG,IAAI;IAC5B,OAAOJ,KAAK;EACd;EAEAF,wBAAwB,CAACO,SAAS,CAACC,cAAc,GAAG,UAAUC,MAAM,EAAEC,OAAO,EAAE;IAC7E,OAAOZ,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE;MAClCa,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ,CAAC;EAEDX,wBAAwB,CAACO,SAAS,CAACK,aAAa,GAAG,UAAUC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAE;IACvF,OAAOA,SAAS,CAACC,KAAK,CAACF,IAAI,CAACG,aAAa,CAACJ,SAAS,CAAC,CAAC;EACvD,CAAC;EAEDb,wBAAwB,CAACK,IAAI,GAAG,sBAAsB;EACtDL,wBAAwB,CAACkB,YAAY,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC;EACzDlB,wBAAwB,CAACmB,aAAa,GAAG;IACvCC,gBAAgB,EAAE,aAAa;IAC/B;IACAC,CAAC,EAAE,CAAC;IACJC,eAAe,EAAE,IAAI;IACrBC,UAAU,EAAE,QAAQ;IACpBC,WAAW,EAAE,CAAC;IACd;IACAC,YAAY,EAAE,QAAQ;IACtBC,IAAI,EAAE,IAAI;IACV;IACAC,YAAY,EAAE;MACZC,MAAM,EAAE,CAAC;MACT;MACAC,KAAK,EAAE,GAAG;MACV;MACAC,SAAS,EAAE,MAAM;MACjB;MACAC,MAAM,EAAE;IACV,CAAC;IACDC,mBAAmB,EAAE;MACnBC,WAAW,EAAE;IACf,CAAC;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC,UAAU,EAAE,EAAE,CAAC;IACf;IACA;IACA;IACA;EAEF,CAAC;EACD,OAAOlC,wBAAwB;AACjC,CAAC,CAACD,WAAW,CAAC;AAEd,eAAeC,wBAAwB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}