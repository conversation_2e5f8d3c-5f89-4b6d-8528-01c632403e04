{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/material/input\";\nimport * as i3 from \"@angular/material/form-field\";\nexport class BasicInputComponent {\n  constructor() {}\n  ngOnInit() {}\n  static #_ = this.ɵfac = function BasicInputComponent_Factory(t) {\n    return new (t || BasicInputComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: BasicInputComponent,\n    selectors: [[\"app-basic-input\"]],\n    decls: 5,\n    vars: 0,\n    consts: [[1, \"example-form\"], [1, \"example-full-width\"], [\"matInput\", \"\", \"placeholder\", \"Favorite food\", \"value\", \"Sushi\"], [\"matInput\", \"\", \"placeholder\", \"Leave a comment\"]],\n    template: function BasicInputComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"form\", 0)(1, \"mat-form-field\", 1);\n        i0.ɵɵelement(2, \"input\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"mat-form-field\", 1);\n        i0.ɵɵelement(4, \"textarea\", 3);\n        i0.ɵɵelementEnd()();\n      }\n    },\n    dependencies: [i1.ɵNgNoValidate, i1.NgControlStatusGroup, i1.NgForm, i2.MatInput, i3.MatFormField],\n    styles: [\".example-form[_ngcontent-%COMP%] {\\n  min-width: 150px;\\n  max-width: 500px;\\n  width: 100%;\\n}\\n\\n.example-full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hc3NldHMvZXhhbXBsZXMvbWF0ZXJpYWwvYmFzaWMtaW5wdXQvYmFzaWMtaW5wdXQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSxnQkFBQTtFQUNBLGdCQUFBO0VBQ0EsV0FBQTtBQUNKOztBQUVFO0VBQ0UsV0FBQTtBQUNKIiwic291cmNlc0NvbnRlbnQiOlsiLmV4YW1wbGUtZm9ybSB7XG4gICAgbWluLXdpZHRoOiAxNTBweDtcbiAgICBtYXgtd2lkdGg6IDUwMHB4O1xuICAgIHdpZHRoOiAxMDAlO1xuICB9XG4gIFxuICAuZXhhbXBsZS1mdWxsLXdpZHRoIHtcbiAgICB3aWR0aDogMTAwJTtcbiAgfVxuICAiXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["BasicInputComponent", "constructor", "ngOnInit", "_", "_2", "selectors", "decls", "vars", "consts", "template", "BasicInputComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\assets\\examples\\material\\basic-input\\basic-input.component.ts", "C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\assets\\examples\\material\\basic-input\\basic-input.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-basic-input',\n  templateUrl: './basic-input.component.html',\n  styleUrls: ['./basic-input.component.scss']\n})\nexport class BasicInputComponent implements OnInit {\n\n  constructor() { }\n\n  ngOnInit() {\n  }\n\n}\n", "<form class=\"example-form\">\n  <mat-form-field class=\"example-full-width\">\n    <input matInput placeholder=\"Favorite food\" value=\"Sushi\">\n  </mat-form-field>\n\n  <mat-form-field class=\"example-full-width\">\n    <textarea matInput placeholder=\"Leave a comment\"></textarea>\n  </mat-form-field>\n</form>\n"], "mappings": ";;;;AAOA,OAAM,MAAOA,mBAAmB;EAE9BC,YAAA,GAAgB;EAEhBC,QAAQA,CAAA,GACR;EAAC,QAAAC,CAAA,G;qBALUH,mBAAmB;EAAA;EAAA,QAAAI,EAAA,G;UAAnBJ,mBAAmB;IAAAK,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCPhCE,EAAA,CAAAC,cAAA,cAA2B;QAEvBD,EAAA,CAAAE,SAAA,eAA0D;QAC5DF,EAAA,CAAAG,YAAA,EAAiB;QAEjBH,EAAA,CAAAC,cAAA,wBAA2C;QACzCD,EAAA,CAAAE,SAAA,kBAA4D;QAC9DF,EAAA,CAAAG,YAAA,EAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}