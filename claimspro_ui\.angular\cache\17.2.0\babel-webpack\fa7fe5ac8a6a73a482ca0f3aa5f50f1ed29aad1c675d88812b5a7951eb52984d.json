{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/button\";\nimport * as i2 from \"@angular/material/card\";\nexport class MultiSectionCardComponent {\n  constructor() {}\n  ngOnInit() {}\n  static #_ = this.ɵfac = function MultiSectionCardComponent_Factory(t) {\n    return new (t || MultiSectionCardComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MultiSectionCardComponent,\n    selectors: [[\"app-multi-section-card\"]],\n    decls: 16,\n    vars: 0,\n    consts: [[1, \"example-card\"], [\"mat-card-avatar\", \"\", 1, \"example-header-image\"], [\"mat-card-image\", \"\", \"src\", \"https://material.angular.io/assets/img/examples/shiba2.jpg\", \"alt\", \"Photo of a Shiba Inu\"], [\"mat-button\", \"\"]],\n    template: function MultiSectionCardComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"mat-card\", 0)(1, \"mat-card-header\");\n        i0.ɵɵelement(2, \"div\", 1);\n        i0.ɵɵelementStart(3, \"mat-card-title\");\n        i0.ɵɵtext(4, \"Shiba Inu\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"mat-card-subtitle\");\n        i0.ɵɵtext(6, \"Dog Breed\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(7, \"img\", 2);\n        i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"p\");\n        i0.ɵɵtext(10, \" The Shiba Inu is the smallest of the six original and distinct spitz breeds of dog from Japan. A small, agile dog that copes very well with mountainous terrain, the Shiba Inu was originally bred for hunting. \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"mat-card-actions\")(12, \"button\", 3);\n        i0.ɵɵtext(13, \"LIKE\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"button\", 3);\n        i0.ɵɵtext(15, \"SHARE\");\n        i0.ɵɵelementEnd()()();\n      }\n    },\n    dependencies: [i1.MatButton, i2.MatCard, i2.MatCardActions, i2.MatCardAvatar, i2.MatCardContent, i2.MatCardHeader, i2.MatCardImage, i2.MatCardSubtitle, i2.MatCardTitle],\n    styles: [\".example-card[_ngcontent-%COMP%] {\\n  max-width: 400px;\\n}\\n\\n.example-header-image[_ngcontent-%COMP%] {\\n  background-image: url(\\\"https://material.angular.io/assets/img/examples/shiba1.jpg\\\");\\n  background-size: cover;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hc3NldHMvZXhhbXBsZXMvbWF0ZXJpYWwvbXVsdGktc2VjdGlvbi1jYXJkL211bHRpLXNlY3Rpb24tY2FyZC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGdCQUFBO0FBQ0o7O0FBRUU7RUFDRSxtRkFBQTtFQUNBLHNCQUFBO0FBQ0oiLCJzb3VyY2VzQ29udGVudCI6WyIuZXhhbXBsZS1jYXJkIHtcbiAgICBtYXgtd2lkdGg6IDQwMHB4O1xuICB9XG4gIFxuICAuZXhhbXBsZS1oZWFkZXItaW1hZ2Uge1xuICAgIGJhY2tncm91bmQtaW1hZ2U6IHVybCgnaHR0cHM6Ly9tYXRlcmlhbC5hbmd1bGFyLmlvL2Fzc2V0cy9pbWcvZXhhbXBsZXMvc2hpYmExLmpwZycpO1xuICAgIGJhY2tncm91bmQtc2l6ZTogY292ZXI7XG4gIH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["MultiSectionCardComponent", "constructor", "ngOnInit", "_", "_2", "selectors", "decls", "vars", "consts", "template", "MultiSectionCardComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\assets\\examples\\material\\multi-section-card\\multi-section-card.component.ts", "C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\assets\\examples\\material\\multi-section-card\\multi-section-card.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-multi-section-card',\n  templateUrl: './multi-section-card.component.html',\n  styleUrls: ['./multi-section-card.component.scss']\n})\nexport class MultiSectionCardComponent implements OnInit {\n\n  constructor() { }\n\n  ngOnInit() {\n  }\n\n}\n", "<mat-card class=\"example-card\">\n  <mat-card-header>\n    <div mat-card-avatar class=\"example-header-image\"></div>\n    <mat-card-title>Shiba Inu</mat-card-title>\n    <mat-card-subtitle>Dog Breed</mat-card-subtitle>\n  </mat-card-header>\n  <img mat-card-image src=\"https://material.angular.io/assets/img/examples/shiba2.jpg\" alt=\"Photo of a Shiba Inu\">\n  <mat-card-content>\n    <p>\n      The Shiba Inu is the smallest of the six original and distinct spitz breeds of dog from Japan.\n      A small, agile dog that copes very well with mountainous terrain, the Shiba Inu was originally\n      bred for hunting.\n    </p>\n  </mat-card-content>\n  <mat-card-actions>\n    <button mat-button>LIKE</button>\n    <button mat-button>SHARE</button>\n  </mat-card-actions>\n</mat-card>\n"], "mappings": ";;;AAOA,OAAM,MAAOA,yBAAyB;EAEpCC,YAAA,GAAgB;EAEhBC,QAAQA,CAAA,GACR;EAAC,QAAAC,CAAA,G;qBALUH,yBAAyB;EAAA;EAAA,QAAAI,EAAA,G;UAAzBJ,yBAAyB;IAAAK,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCPtCE,EAAA,CAAAC,cAAA,kBAA+B;QAE3BD,EAAA,CAAAE,SAAA,aAAwD;QACxDF,EAAA,CAAAC,cAAA,qBAAgB;QAAAD,EAAA,CAAAG,MAAA,gBAAS;QAAAH,EAAA,CAAAI,YAAA,EAAiB;QAC1CJ,EAAA,CAAAC,cAAA,wBAAmB;QAAAD,EAAA,CAAAG,MAAA,gBAAS;QAAAH,EAAA,CAAAI,YAAA,EAAoB;QAElDJ,EAAA,CAAAE,SAAA,aAAgH;QAChHF,EAAA,CAAAC,cAAA,uBAAkB;QAEdD,EAAA,CAAAG,MAAA,yNAGF;QAAAH,EAAA,CAAAI,YAAA,EAAI;QAENJ,EAAA,CAAAC,cAAA,wBAAkB;QACGD,EAAA,CAAAG,MAAA,YAAI;QAAAH,EAAA,CAAAI,YAAA,EAAS;QAChCJ,EAAA,CAAAC,cAAA,iBAAmB;QAAAD,EAAA,CAAAG,MAAA,aAAK;QAAAH,EAAA,CAAAI,YAAA,EAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}