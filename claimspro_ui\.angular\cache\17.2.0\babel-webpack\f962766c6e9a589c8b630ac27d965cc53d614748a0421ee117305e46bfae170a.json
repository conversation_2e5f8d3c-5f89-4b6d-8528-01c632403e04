{"ast": null, "code": "import { map, catchError, delay } from \"rxjs/operators\";\nimport { of, BehaviorSubject, throwError } from \"rxjs\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../local-store.service\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/router\";\n// ================= only for demo purpose ===========\nconst DEMO_TOKEN = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************.dXw0ySun5ex98dOzTEk0lkmXJvxg3Qgz4ed\";\nconst DEMO_USER = {\n  id: \"5b700c45639d2c0c54b354ba\",\n  displayName: \"Watson Joyce\",\n  role: \"SA\"\n};\n// ================= you will get those data from server =======\nexport class JwtAuthService {\n  constructor(ls, http, router, route) {\n    this.ls = ls;\n    this.http = http;\n    this.router = router;\n    this.route = route;\n    this.user = {};\n    this.user$ = new BehaviorSubject(this.user);\n    this.JWT_TOKEN = \"JWT_TOKEN\";\n    this.APP_USER = \"EGRET_USER\";\n    this.route.queryParams.subscribe(params => this.return = params['return'] || '/');\n  }\n  signin(username, password) {\n    return of({\n      token: DEMO_TOKEN,\n      user: DEMO_USER\n    }).pipe(delay(1000), map(res => {\n      this.setUserAndToken(res.token, res.user, !!res);\n      this.signingIn = false;\n      return res;\n    }), catchError(error => {\n      return throwError(error);\n    }));\n    // FOLLOWING CODE SENDS SIGNIN REQUEST TO SERVER\n    // this.signingIn = true;\n    // return this.http.post(`${environment.apiURL}/auth/local`, { username, password })\n    //   .pipe(\n    //     map((res: any) => {\n    //       this.setUserAndToken(res.token, res.user, !!res);\n    //       this.signingIn = false;\n    //       return res;\n    //     }),\n    //     catchError((error) => {\n    //       return throwError(error);\n    //     })\n    //   );\n  }\n  /*\n    checkTokenIsValid is called inside constructor of\n    shared/components/layouts/admin-layout/admin-layout.component.ts\n  */\n  checkTokenIsValid() {\n    return of(DEMO_USER).pipe(map(profile => {\n      this.setUserAndToken(this.getJwtToken(), profile, true);\n      this.signingIn = false;\n      return profile;\n    }), catchError(error => {\n      return of(error);\n    }));\n    /*\n      The following code get user data and jwt token is assigned to\n      Request header using token.interceptor\n      This checks if the existing token is valid when app is reloaded\n    */\n    // return this.http.get(`${environment.apiURL}/api/users/profile`)\n    //   .pipe(\n    //     map((profile: User) => {\n    //       this.setUserAndToken(this.getJwtToken(), profile, true);\n    //       return profile;\n    //     }),\n    //     catchError((error) => {\n    //       this.signout();\n    //       return of(error);\n    //     })\n    //   );\n  }\n  signout() {\n    this.setUserAndToken(null, null, false);\n    this.router.navigateByUrl(\"sessions/signin\");\n  }\n  isLoggedIn() {\n    return !!this.getJwtToken();\n  }\n  getJwtToken() {\n    return this.ls.getItem(this.JWT_TOKEN);\n  }\n  getUser() {\n    return this.ls.getItem(this.APP_USER);\n  }\n  setUserAndToken(token, user, isAuthenticated) {\n    this.isAuthenticated = isAuthenticated;\n    this.token = token;\n    this.user = user;\n    this.user$.next(user);\n    this.ls.setItem(this.JWT_TOKEN, token);\n    this.ls.setItem(this.APP_USER, user);\n  }\n  static #_ = this.ɵfac = function JwtAuthService_Factory(t) {\n    return new (t || JwtAuthService)(i0.ɵɵinject(i1.LocalStoreService), i0.ɵɵinject(i2.HttpClient), i0.ɵɵinject(i3.Router), i0.ɵɵinject(i3.ActivatedRoute));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: JwtAuthService,\n    factory: JwtAuthService.ɵfac,\n    providedIn: \"root\"\n  });\n}", "map": {"version": 3, "names": ["map", "catchError", "delay", "of", "BehaviorSubject", "throwError", "DEMO_TOKEN", "DEMO_USER", "id", "displayName", "role", "JwtAuthService", "constructor", "ls", "http", "router", "route", "user", "user$", "JWT_TOKEN", "APP_USER", "queryParams", "subscribe", "params", "return", "signin", "username", "password", "token", "pipe", "res", "setUserAndToken", "signingIn", "error", "checkTokenIsValid", "profile", "getJwtToken", "signout", "navigateByUrl", "isLoggedIn", "getItem", "getUser", "isAuthenticated", "next", "setItem", "_", "i0", "ɵɵinject", "i1", "LocalStoreService", "i2", "HttpClient", "i3", "Router", "ActivatedRoute", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\shared\\services\\auth\\jwt-auth.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\";\nimport { LocalStoreService } from \"../local-store.service\";\nimport { HttpClient } from \"@angular/common/http\";\nimport { Router, ActivatedRoute } from \"@angular/router\";\nimport { map, catchError, delay } from \"rxjs/operators\";\nimport { User } from \"../../models/user.model\";\nimport { of, BehaviorSubject, throwError } from \"rxjs\";\nimport { environment } from \"environments/environment\";\n\n// ================= only for demo purpose ===========\nconst DEMO_TOKEN =\n  \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************.dXw0ySun5ex98dOzTEk0lkmXJvxg3Qgz4ed\";\n\nconst DEMO_USER: User = {\n  id: \"5b700c45639d2c0c54b354ba\",\n  displayName: \"Watson Joyce\",\n  role: \"SA\",\n};\n// ================= you will get those data from server =======\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class JwtAuthService {\n  token;\n  isAuthenticated: Boolean;\n  user: User = {};\n  user$ = (new BehaviorSubject<User>(this.user));\n  signingIn: Boolean;\n  return: string;\n  JWT_TOKEN = \"JWT_TOKEN\";\n  APP_USER = \"EGRET_USER\";\n\n  constructor(\n    private ls: LocalStoreService,\n    private http: HttpClient,\n    private router: Router,\n    private route: ActivatedRoute\n  ) {\n    this.route.queryParams\n      .subscribe(params => this.return = params['return'] || '/');\n  }\n\n  public signin(username, password) {\n    return of({token: DEMO_TOKEN, user: DEMO_USER})\n      .pipe(\n        delay(1000),\n        map((res: any) => {\n          this.setUserAndToken(res.token, res.user, !!res);\n          this.signingIn = false;\n          return res;\n        }),\n        catchError((error) => {\n          return throwError(error);\n        })\n      );\n\n    // FOLLOWING CODE SENDS SIGNIN REQUEST TO SERVER\n\n    // this.signingIn = true;\n    // return this.http.post(`${environment.apiURL}/auth/local`, { username, password })\n    //   .pipe(\n    //     map((res: any) => {\n    //       this.setUserAndToken(res.token, res.user, !!res);\n    //       this.signingIn = false;\n    //       return res;\n    //     }),\n    //     catchError((error) => {\n    //       return throwError(error);\n    //     })\n    //   );\n  }\n\n  /*\n    checkTokenIsValid is called inside constructor of\n    shared/components/layouts/admin-layout/admin-layout.component.ts\n  */\n  public checkTokenIsValid() {\n    return of(DEMO_USER)\n      .pipe(\n        map((profile: User) => {\n          this.setUserAndToken(this.getJwtToken(), profile, true);\n          this.signingIn = false;\n          return profile;\n        }),\n        catchError((error) => {\n          return of(error);\n        })\n      );\n    \n    /*\n      The following code get user data and jwt token is assigned to\n      Request header using token.interceptor\n      This checks if the existing token is valid when app is reloaded\n    */\n\n    // return this.http.get(`${environment.apiURL}/api/users/profile`)\n    //   .pipe(\n    //     map((profile: User) => {\n    //       this.setUserAndToken(this.getJwtToken(), profile, true);\n    //       return profile;\n    //     }),\n    //     catchError((error) => {\n    //       this.signout();\n    //       return of(error);\n    //     })\n    //   );\n  }\n\n  public signout() {\n    this.setUserAndToken(null, null, false);\n    this.router.navigateByUrl(\"sessions/signin\");\n  }\n\n  isLoggedIn(): Boolean {\n    return !!this.getJwtToken();\n  }\n\n  getJwtToken() {\n    return this.ls.getItem(this.JWT_TOKEN);\n  }\n  getUser() {\n    return this.ls.getItem(this.APP_USER);\n  }\n\n  setUserAndToken(token: String, user: User, isAuthenticated: Boolean) {\n    this.isAuthenticated = isAuthenticated;\n    this.token = token;\n    this.user = user;\n    this.user$.next(user);\n    this.ls.setItem(this.JWT_TOKEN, token);\n    this.ls.setItem(this.APP_USER, user);\n  }\n}\n"], "mappings": "AAIA,SAASA,GAAG,EAAEC,UAAU,EAAEC,KAAK,QAAQ,gBAAgB;AAEvD,SAASC,EAAE,EAAEC,eAAe,EAAEC,UAAU,QAAQ,MAAM;;;;;AAGtD;AACA,MAAMC,UAAU,GACd,kPAAkP;AAEpP,MAAMC,SAAS,GAAS;EACtBC,EAAE,EAAE,0BAA0B;EAC9BC,WAAW,EAAE,cAAc;EAC3BC,IAAI,EAAE;CACP;AACD;AAKA,OAAM,MAAOC,cAAc;EAUzBC,YACUC,EAAqB,EACrBC,IAAgB,EAChBC,MAAc,EACdC,KAAqB;IAHrB,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IAXf,KAAAC,IAAI,GAAS,EAAE;IACf,KAAAC,KAAK,GAAI,IAAId,eAAe,CAAO,IAAI,CAACa,IAAI,CAAE;IAG9C,KAAAE,SAAS,GAAG,WAAW;IACvB,KAAAC,QAAQ,GAAG,YAAY;IAQrB,IAAI,CAACJ,KAAK,CAACK,WAAW,CACnBC,SAAS,CAACC,MAAM,IAAI,IAAI,CAACC,MAAM,GAAGD,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC;EAC/D;EAEOE,MAAMA,CAACC,QAAQ,EAAEC,QAAQ;IAC9B,OAAOxB,EAAE,CAAC;MAACyB,KAAK,EAAEtB,UAAU;MAAEW,IAAI,EAAEV;IAAS,CAAC,CAAC,CAC5CsB,IAAI,CACH3B,KAAK,CAAC,IAAI,CAAC,EACXF,GAAG,CAAE8B,GAAQ,IAAI;MACf,IAAI,CAACC,eAAe,CAACD,GAAG,CAACF,KAAK,EAAEE,GAAG,CAACb,IAAI,EAAE,CAAC,CAACa,GAAG,CAAC;MAChD,IAAI,CAACE,SAAS,GAAG,KAAK;MACtB,OAAOF,GAAG;IACZ,CAAC,CAAC,EACF7B,UAAU,CAAEgC,KAAK,IAAI;MACnB,OAAO5B,UAAU,CAAC4B,KAAK,CAAC;IAC1B,CAAC,CAAC,CACH;IAEH;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EAEA;;;;EAIOC,iBAAiBA,CAAA;IACtB,OAAO/B,EAAE,CAACI,SAAS,CAAC,CACjBsB,IAAI,CACH7B,GAAG,CAAEmC,OAAa,IAAI;MACpB,IAAI,CAACJ,eAAe,CAAC,IAAI,CAACK,WAAW,EAAE,EAAED,OAAO,EAAE,IAAI,CAAC;MACvD,IAAI,CAACH,SAAS,GAAG,KAAK;MACtB,OAAOG,OAAO;IAChB,CAAC,CAAC,EACFlC,UAAU,CAAEgC,KAAK,IAAI;MACnB,OAAO9B,EAAE,CAAC8B,KAAK,CAAC;IAClB,CAAC,CAAC,CACH;IAEH;;;;;IAMA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EAEOI,OAAOA,CAAA;IACZ,IAAI,CAACN,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;IACvC,IAAI,CAAChB,MAAM,CAACuB,aAAa,CAAC,iBAAiB,CAAC;EAC9C;EAEAC,UAAUA,CAAA;IACR,OAAO,CAAC,CAAC,IAAI,CAACH,WAAW,EAAE;EAC7B;EAEAA,WAAWA,CAAA;IACT,OAAO,IAAI,CAACvB,EAAE,CAAC2B,OAAO,CAAC,IAAI,CAACrB,SAAS,CAAC;EACxC;EACAsB,OAAOA,CAAA;IACL,OAAO,IAAI,CAAC5B,EAAE,CAAC2B,OAAO,CAAC,IAAI,CAACpB,QAAQ,CAAC;EACvC;EAEAW,eAAeA,CAACH,KAAa,EAAEX,IAAU,EAAEyB,eAAwB;IACjE,IAAI,CAACA,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACd,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACX,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,KAAK,CAACyB,IAAI,CAAC1B,IAAI,CAAC;IACrB,IAAI,CAACJ,EAAE,CAAC+B,OAAO,CAAC,IAAI,CAACzB,SAAS,EAAES,KAAK,CAAC;IACtC,IAAI,CAACf,EAAE,CAAC+B,OAAO,CAAC,IAAI,CAACxB,QAAQ,EAAEH,IAAI,CAAC;EACtC;EAAC,QAAA4B,CAAA,G;qBA7GUlC,cAAc,EAAAmC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAE,cAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAd5C,cAAc;IAAA6C,OAAA,EAAd7C,cAAc,CAAA8C,IAAA;IAAAC,UAAA,EAFb;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}