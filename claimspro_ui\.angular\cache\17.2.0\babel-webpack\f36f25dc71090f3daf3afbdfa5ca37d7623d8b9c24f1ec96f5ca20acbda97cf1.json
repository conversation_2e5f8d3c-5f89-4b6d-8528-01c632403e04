{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/button\";\nimport * as i2 from \"@angular/material/menu\";\nexport class NestedMenuComponent {\n  constructor() {}\n  ngOnInit() {}\n  static #_ = this.ɵfac = function NestedMenuComponent_Factory(t) {\n    return new (t || NestedMenuComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NestedMenuComponent,\n    selectors: [[\"app-nested-menu\"]],\n    decls: 70,\n    vars: 6,\n    consts: [[\"mat-button\", \"\", 3, \"matMenuTriggerFor\"], [\"animals\", \"matMenu\"], [\"mat-menu-item\", \"\", 3, \"matMenuTriggerFor\"], [\"vertebrates\", \"matMenu\"], [\"mat-menu-item\", \"\"], [\"invertebrates\", \"matMenu\"], [\"fish\", \"matMenu\"], [\"amphibians\", \"matMenu\"], [\"reptiles\", \"matMenu\"], [\"mat-menu-item\", \"\", \"disabled\", \"\"]],\n    template: function NestedMenuComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"button\", 0);\n        i0.ɵɵtext(1, \"Animal index\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"mat-menu\", null, 1)(4, \"button\", 2);\n        i0.ɵɵtext(5, \"Vertebrates\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"button\", 2);\n        i0.ɵɵtext(7, \"Invertebrates\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"mat-menu\", null, 3)(10, \"button\", 2);\n        i0.ɵɵtext(11, \"Fishes\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"button\", 2);\n        i0.ɵɵtext(13, \"Amphibians\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"button\", 2);\n        i0.ɵɵtext(15, \"Reptiles\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"button\", 4);\n        i0.ɵɵtext(17, \"Birds\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"button\", 4);\n        i0.ɵɵtext(19, \"Mammals\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(20, \"mat-menu\", null, 5)(22, \"button\", 4);\n        i0.ɵɵtext(23, \"Insects\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"button\", 4);\n        i0.ɵɵtext(25, \"Molluscs\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"button\", 4);\n        i0.ɵɵtext(27, \"Crustaceans\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"button\", 4);\n        i0.ɵɵtext(29, \"Corals\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"button\", 4);\n        i0.ɵɵtext(31, \"Arachnids\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(32, \"button\", 4);\n        i0.ɵɵtext(33, \"Velvet worms\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(34, \"button\", 4);\n        i0.ɵɵtext(35, \"Horseshoe crabs\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(36, \"mat-menu\", null, 6)(38, \"button\", 4);\n        i0.ɵɵtext(39, \"Baikal oilfish\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(40, \"button\", 4);\n        i0.ɵɵtext(41, \"Bala shark\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(42, \"button\", 4);\n        i0.ɵɵtext(43, \"Ballan wrasse\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(44, \"button\", 4);\n        i0.ɵɵtext(45, \"Bamboo shark\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(46, \"button\", 4);\n        i0.ɵɵtext(47, \"Banded killifish\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(48, \"mat-menu\", null, 7)(50, \"button\", 4);\n        i0.ɵɵtext(51, \"Sonoran desert toad\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(52, \"button\", 4);\n        i0.ɵɵtext(53, \"Western toad\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(54, \"button\", 4);\n        i0.ɵɵtext(55, \"Arroyo toad\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(56, \"button\", 4);\n        i0.ɵɵtext(57, \"Yosemite toad\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(58, \"mat-menu\", null, 8)(60, \"button\", 4);\n        i0.ɵɵtext(61, \"Banded Day Gecko\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(62, \"button\", 4);\n        i0.ɵɵtext(63, \"Banded Gila Monster\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(64, \"button\", 4);\n        i0.ɵɵtext(65, \"Black Tree Monitor\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(66, \"button\", 4);\n        i0.ɵɵtext(67, \"Blue Spiny Lizard\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(68, \"button\", 9);\n        i0.ɵɵtext(69, \"Velociraptor\");\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(3);\n        const _r1 = i0.ɵɵreference(9);\n        const _r2 = i0.ɵɵreference(21);\n        const _r3 = i0.ɵɵreference(37);\n        const _r4 = i0.ɵɵreference(49);\n        const _r5 = i0.ɵɵreference(59);\n        i0.ɵɵproperty(\"matMenuTriggerFor\", _r0);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"matMenuTriggerFor\", _r1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"matMenuTriggerFor\", _r2);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"matMenuTriggerFor\", _r3);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"matMenuTriggerFor\", _r4);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"matMenuTriggerFor\", _r5);\n      }\n    },\n    dependencies: [i1.MatButton, i2.MatMenu, i2.MatMenuItem, i2.MatMenuTrigger],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["NestedMenuComponent", "constructor", "ngOnInit", "_", "_2", "selectors", "decls", "vars", "consts", "template", "NestedMenuComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "_r0", "ɵɵadvance", "_r1", "_r2", "_r3", "_r4", "_r5"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\assets\\examples\\material\\nested-menu\\nested-menu.component.ts", "C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\assets\\examples\\material\\nested-menu\\nested-menu.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-nested-menu',\n  templateUrl: './nested-menu.component.html',\n  styleUrls: ['./nested-menu.component.scss']\n})\nexport class NestedMenuComponent implements OnInit {\n\n  constructor() { }\n\n  ngOnInit() {\n  }\n\n}\n", "<button mat-button [matMenuTriggerFor]=\"animals\">Animal index</button>\n\n<mat-menu #animals=\"matMenu\">\n  <button mat-menu-item [matMenuTriggerFor]=\"vertebrates\">Vertebrates</button>\n  <button mat-menu-item [matMenuTriggerFor]=\"invertebrates\">Invertebrates</button>\n</mat-menu>\n\n<mat-menu #vertebrates=\"matMenu\">\n  <button mat-menu-item [matMenuTriggerFor]=\"fish\">Fishes</button>\n  <button mat-menu-item [matMenuTriggerFor]=\"amphibians\">Amphibians</button>\n  <button mat-menu-item [matMenuTriggerFor]=\"reptiles\">Reptiles</button>\n  <button mat-menu-item>Birds</button>\n  <button mat-menu-item>Mammals</button>\n</mat-menu>\n\n<mat-menu #invertebrates=\"matMenu\">\n  <button mat-menu-item>Insects</button>\n  <button mat-menu-item>Molluscs</button>\n  <button mat-menu-item>Crustaceans</button>\n  <button mat-menu-item>Corals</button>\n  <button mat-menu-item>Arachnids</button>\n  <button mat-menu-item>Velvet worms</button>\n  <button mat-menu-item>Horseshoe crabs</button>\n</mat-menu>\n\n<mat-menu #fish=\"matMenu\">\n  <button mat-menu-item>Baikal oilfish</button>\n  <button mat-menu-item>Bala shark</button>\n  <button mat-menu-item>Ballan wrasse</button>\n  <button mat-menu-item>Bamboo shark</button>\n  <button mat-menu-item>Banded killifish</button>\n</mat-menu>\n\n<mat-menu #amphibians=\"matMenu\">\n  <button mat-menu-item>Sonoran desert toad</button>\n  <button mat-menu-item>Western toad</button>\n  <button mat-menu-item>Arroyo toad</button>\n  <button mat-menu-item>Yosemite toad</button>\n</mat-menu>\n\n<mat-menu #reptiles=\"matMenu\">\n  <button mat-menu-item>Banded Day Gecko</button>\n  <button mat-menu-item>Banded Gila Monster</button>\n  <button mat-menu-item>Black Tree Monitor</button>\n  <button mat-menu-item>Blue Spiny Lizard</button>\n  <button mat-menu-item disabled>Velociraptor</button>\n</mat-menu>\n"], "mappings": ";;;AAOA,OAAM,MAAOA,mBAAmB;EAE9BC,YAAA,GAAgB;EAEhBC,QAAQA,CAAA,GACR;EAAC,QAAAC,CAAA,G;qBALUH,mBAAmB;EAAA;EAAA,QAAAI,EAAA,G;UAAnBJ,mBAAmB;IAAAK,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCPhCE,EAAA,CAAAC,cAAA,gBAAiD;QAAAD,EAAA,CAAAE,MAAA,mBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAEtEH,EAAA,CAAAC,cAAA,wBAA6B;QAC6BD,EAAA,CAAAE,MAAA,kBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC5EH,EAAA,CAAAC,cAAA,gBAA0D;QAAAD,EAAA,CAAAE,MAAA,oBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAGlFH,EAAA,CAAAC,cAAA,wBAAiC;QACkBD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAChEH,EAAA,CAAAC,cAAA,iBAAuD;QAAAD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC1EH,EAAA,CAAAC,cAAA,iBAAqD;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACtEH,EAAA,CAAAC,cAAA,iBAAsB;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACpCH,EAAA,CAAAC,cAAA,iBAAsB;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAGxCH,EAAA,CAAAC,cAAA,yBAAmC;QACXD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACtCH,EAAA,CAAAC,cAAA,iBAAsB;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACvCH,EAAA,CAAAC,cAAA,iBAAsB;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC1CH,EAAA,CAAAC,cAAA,iBAAsB;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACrCH,EAAA,CAAAC,cAAA,iBAAsB;QAAAD,EAAA,CAAAE,MAAA,iBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACxCH,EAAA,CAAAC,cAAA,iBAAsB;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC3CH,EAAA,CAAAC,cAAA,iBAAsB;QAAAD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAGhDH,EAAA,CAAAC,cAAA,yBAA0B;QACFD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC7CH,EAAA,CAAAC,cAAA,iBAAsB;QAAAD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACzCH,EAAA,CAAAC,cAAA,iBAAsB;QAAAD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC5CH,EAAA,CAAAC,cAAA,iBAAsB;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC3CH,EAAA,CAAAC,cAAA,iBAAsB;QAAAD,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAGjDH,EAAA,CAAAC,cAAA,yBAAgC;QACRD,EAAA,CAAAE,MAAA,2BAAmB;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAClDH,EAAA,CAAAC,cAAA,iBAAsB;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC3CH,EAAA,CAAAC,cAAA,iBAAsB;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC1CH,EAAA,CAAAC,cAAA,iBAAsB;QAAAD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAG9CH,EAAA,CAAAC,cAAA,yBAA8B;QACND,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC/CH,EAAA,CAAAC,cAAA,iBAAsB;QAAAD,EAAA,CAAAE,MAAA,2BAAmB;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAClDH,EAAA,CAAAC,cAAA,iBAAsB;QAAAD,EAAA,CAAAE,MAAA,0BAAkB;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACjDH,EAAA,CAAAC,cAAA,iBAAsB;QAAAD,EAAA,CAAAE,MAAA,yBAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAChDH,EAAA,CAAAC,cAAA,iBAA+B;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;QA7CnCH,EAAA,CAAAI,UAAA,sBAAAC,GAAA,CAA6B;QAGxBL,EAAA,CAAAM,SAAA,GAAiC;QAAjCN,EAAA,CAAAI,UAAA,sBAAAG,GAAA,CAAiC;QACjCP,EAAA,CAAAM,SAAA,GAAmC;QAAnCN,EAAA,CAAAI,UAAA,sBAAAI,GAAA,CAAmC;QAInCR,EAAA,CAAAM,SAAA,GAA0B;QAA1BN,EAAA,CAAAI,UAAA,sBAAAK,GAAA,CAA0B;QAC1BT,EAAA,CAAAM,SAAA,GAAgC;QAAhCN,EAAA,CAAAI,UAAA,sBAAAM,GAAA,CAAgC;QAChCV,EAAA,CAAAM,SAAA,GAA8B;QAA9BN,EAAA,CAAAI,UAAA,sBAAAO,GAAA,CAA8B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}