{"ast": null, "code": "/*\nLanguage: AppleScript\nAuthors: <AUTHORS>\nCategory: scripting\nWebsite: https://developer.apple.com/library/archive/documentation/AppleScript/Conceptual/AppleScriptLangGuide/introduction/ASLR_intro.html\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction applescript(hljs) {\n  const regex = hljs.regex;\n  const STRING = hljs.inherit(hljs.QUOTE_STRING_MODE, {\n    illegal: null\n  });\n  const PARAMS = {\n    className: 'params',\n    begin: /\\(/,\n    end: /\\)/,\n    contains: ['self', hljs.C_NUMBER_MODE, STRING]\n  };\n  const COMMENT_MODE_1 = hljs.COMMENT(/--/, /$/);\n  const COMMENT_MODE_2 = hljs.COMMENT(/\\(\\*/, /\\*\\)/, {\n    contains: ['self',\n    // allow nesting\n    COMMENT_MODE_1]\n  });\n  const COMMENTS = [COMMENT_MODE_1, COMMENT_MODE_2, hljs.HASH_COMMENT_MODE];\n  const KEYWORD_PATTERNS = [/apart from/, /aside from/, /instead of/, /out of/, /greater than/, /isn't|(doesn't|does not) (equal|come before|come after|contain)/, /(greater|less) than( or equal)?/, /(starts?|ends|begins?) with/, /contained by/, /comes (before|after)/, /a (ref|reference)/, /POSIX (file|path)/, /(date|time) string/, /quoted form/];\n  const BUILT_IN_PATTERNS = [/clipboard info/, /the clipboard/, /info for/, /list (disks|folder)/, /mount volume/, /path to/, /(close|open for) access/, /(get|set) eof/, /current date/, /do shell script/, /get volume settings/, /random number/, /set volume/, /system attribute/, /system info/, /time to GMT/, /(load|run|store) script/, /scripting components/, /ASCII (character|number)/, /localized string/, /choose (application|color|file|file name|folder|from list|remote application|URL)/, /display (alert|dialog)/];\n  return {\n    name: 'AppleScript',\n    aliases: ['osascript'],\n    keywords: {\n      keyword: 'about above after against and around as at back before beginning ' + 'behind below beneath beside between but by considering ' + 'contain contains continue copy div does eighth else end equal ' + 'equals error every exit fifth first for fourth from front ' + 'get given global if ignoring in into is it its last local me ' + 'middle mod my ninth not of on onto or over prop property put ref ' + 'reference repeat returning script second set seventh since ' + 'sixth some tell tenth that the|0 then third through thru ' + 'timeout times to transaction try until where while whose with ' + 'without',\n      literal: 'AppleScript false linefeed return pi quote result space tab true',\n      built_in: 'alias application boolean class constant date file integer list ' + 'number real record string text ' + 'activate beep count delay launch log offset read round ' + 'run say summarize write ' + 'character characters contents day frontmost id item length ' + 'month name|0 paragraph paragraphs rest reverse running time version ' + 'weekday word words year'\n    },\n    contains: [STRING, hljs.C_NUMBER_MODE, {\n      className: 'built_in',\n      begin: regex.concat(/\\b/, regex.either(...BUILT_IN_PATTERNS), /\\b/)\n    }, {\n      className: 'built_in',\n      begin: /^\\s*return\\b/\n    }, {\n      className: 'literal',\n      begin: /\\b(text item delimiters|current application|missing value)\\b/\n    }, {\n      className: 'keyword',\n      begin: regex.concat(/\\b/, regex.either(...KEYWORD_PATTERNS), /\\b/)\n    }, {\n      beginKeywords: 'on',\n      illegal: /[${=;\\n]/,\n      contains: [hljs.UNDERSCORE_TITLE_MODE, PARAMS]\n    }, ...COMMENTS],\n    illegal: /\\/\\/|->|=>|\\[\\[/\n  };\n}\nmodule.exports = applescript;", "map": {"version": 3, "names": ["applescript", "hljs", "regex", "STRING", "inherit", "QUOTE_STRING_MODE", "illegal", "PARAMS", "className", "begin", "end", "contains", "C_NUMBER_MODE", "COMMENT_MODE_1", "COMMENT", "COMMENT_MODE_2", "COMMENTS", "HASH_COMMENT_MODE", "KEYWORD_PATTERNS", "BUILT_IN_PATTERNS", "name", "aliases", "keywords", "keyword", "literal", "built_in", "concat", "either", "beginKeywords", "UNDERSCORE_TITLE_MODE", "module", "exports"], "sources": ["C:/OneDrive/Srikant/SWIP/ClaimsPro/claimspro200-src/claimspro_ui/node_modules/highlight.js/lib/languages/applescript.js"], "sourcesContent": ["/*\nLanguage: AppleScript\nAuthors: <AUTHORS>\nCategory: scripting\nWebsite: https://developer.apple.com/library/archive/documentation/AppleScript/Conceptual/AppleScriptLangGuide/introduction/ASLR_intro.html\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction applescript(hljs) {\n  const regex = hljs.regex;\n  const STRING = hljs.inherit(\n    hljs.QUOTE_STRING_MODE, { illegal: null });\n  const PARAMS = {\n    className: 'params',\n    begin: /\\(/,\n    end: /\\)/,\n    contains: [\n      'self',\n      hljs.C_NUMBER_MODE,\n      STRING\n    ]\n  };\n  const COMMENT_MODE_1 = hljs.COMMENT(/--/, /$/);\n  const COMMENT_MODE_2 = hljs.COMMENT(\n    /\\(\\*/,\n    /\\*\\)/,\n    { contains: [\n      'self', // allow nesting\n      COMMENT_MODE_1\n    ] }\n  );\n  const COMMENTS = [\n    COMMENT_MODE_1,\n    COMMENT_MODE_2,\n    hljs.HASH_COMMENT_MODE\n  ];\n\n  const KEYWORD_PATTERNS = [\n    /apart from/,\n    /aside from/,\n    /instead of/,\n    /out of/,\n    /greater than/,\n    /isn't|(doesn't|does not) (equal|come before|come after|contain)/,\n    /(greater|less) than( or equal)?/,\n    /(starts?|ends|begins?) with/,\n    /contained by/,\n    /comes (before|after)/,\n    /a (ref|reference)/,\n    /POSIX (file|path)/,\n    /(date|time) string/,\n    /quoted form/\n  ];\n\n  const BUILT_IN_PATTERNS = [\n    /clipboard info/,\n    /the clipboard/,\n    /info for/,\n    /list (disks|folder)/,\n    /mount volume/,\n    /path to/,\n    /(close|open for) access/,\n    /(get|set) eof/,\n    /current date/,\n    /do shell script/,\n    /get volume settings/,\n    /random number/,\n    /set volume/,\n    /system attribute/,\n    /system info/,\n    /time to GMT/,\n    /(load|run|store) script/,\n    /scripting components/,\n    /ASCII (character|number)/,\n    /localized string/,\n    /choose (application|color|file|file name|folder|from list|remote application|URL)/,\n    /display (alert|dialog)/\n  ];\n\n  return {\n    name: 'AppleScript',\n    aliases: [ 'osascript' ],\n    keywords: {\n      keyword:\n        'about above after against and around as at back before beginning '\n        + 'behind below beneath beside between but by considering '\n        + 'contain contains continue copy div does eighth else end equal '\n        + 'equals error every exit fifth first for fourth from front '\n        + 'get given global if ignoring in into is it its last local me '\n        + 'middle mod my ninth not of on onto or over prop property put ref '\n        + 'reference repeat returning script second set seventh since '\n        + 'sixth some tell tenth that the|0 then third through thru '\n        + 'timeout times to transaction try until where while whose with '\n        + 'without',\n      literal:\n        'AppleScript false linefeed return pi quote result space tab true',\n      built_in:\n        'alias application boolean class constant date file integer list '\n        + 'number real record string text '\n        + 'activate beep count delay launch log offset read round '\n        + 'run say summarize write '\n        + 'character characters contents day frontmost id item length '\n        + 'month name|0 paragraph paragraphs rest reverse running time version '\n        + 'weekday word words year'\n    },\n    contains: [\n      STRING,\n      hljs.C_NUMBER_MODE,\n      {\n        className: 'built_in',\n        begin: regex.concat(\n          /\\b/,\n          regex.either(...BUILT_IN_PATTERNS),\n          /\\b/\n        )\n      },\n      {\n        className: 'built_in',\n        begin: /^\\s*return\\b/\n      },\n      {\n        className: 'literal',\n        begin:\n          /\\b(text item delimiters|current application|missing value)\\b/\n      },\n      {\n        className: 'keyword',\n        begin: regex.concat(\n          /\\b/,\n          regex.either(...KEYWORD_PATTERNS),\n          /\\b/\n        )\n      },\n      {\n        beginKeywords: 'on',\n        illegal: /[${=;\\n]/,\n        contains: [\n          hljs.UNDERSCORE_TITLE_MODE,\n          PARAMS\n        ]\n      },\n      ...COMMENTS\n    ],\n    illegal: /\\/\\/|->|=>|\\[\\[/\n  };\n}\n\nmodule.exports = applescript;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,WAAWA,CAACC,IAAI,EAAE;EACzB,MAAMC,KAAK,GAAGD,IAAI,CAACC,KAAK;EACxB,MAAMC,MAAM,GAAGF,IAAI,CAACG,OAAO,CACzBH,IAAI,CAACI,iBAAiB,EAAE;IAAEC,OAAO,EAAE;EAAK,CAAC,CAAC;EAC5C,MAAMC,MAAM,GAAG;IACbC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTC,QAAQ,EAAE,CACR,MAAM,EACNV,IAAI,CAACW,aAAa,EAClBT,MAAM;EAEV,CAAC;EACD,MAAMU,cAAc,GAAGZ,IAAI,CAACa,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;EAC9C,MAAMC,cAAc,GAAGd,IAAI,CAACa,OAAO,CACjC,MAAM,EACN,MAAM,EACN;IAAEH,QAAQ,EAAE,CACV,MAAM;IAAE;IACRE,cAAc;EACd,CACJ,CAAC;EACD,MAAMG,QAAQ,GAAG,CACfH,cAAc,EACdE,cAAc,EACdd,IAAI,CAACgB,iBAAiB,CACvB;EAED,MAAMC,gBAAgB,GAAG,CACvB,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,QAAQ,EACR,cAAc,EACd,iEAAiE,EACjE,iCAAiC,EACjC,6BAA6B,EAC7B,cAAc,EACd,sBAAsB,EACtB,mBAAmB,EACnB,mBAAmB,EACnB,oBAAoB,EACpB,aAAa,CACd;EAED,MAAMC,iBAAiB,GAAG,CACxB,gBAAgB,EAChB,eAAe,EACf,UAAU,EACV,qBAAqB,EACrB,cAAc,EACd,SAAS,EACT,yBAAyB,EACzB,eAAe,EACf,cAAc,EACd,iBAAiB,EACjB,qBAAqB,EACrB,eAAe,EACf,YAAY,EACZ,kBAAkB,EAClB,aAAa,EACb,aAAa,EACb,yBAAyB,EACzB,sBAAsB,EACtB,0BAA0B,EAC1B,kBAAkB,EAClB,mFAAmF,EACnF,wBAAwB,CACzB;EAED,OAAO;IACLC,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE,CAAE,WAAW,CAAE;IACxBC,QAAQ,EAAE;MACRC,OAAO,EACL,mEAAmE,GACjE,yDAAyD,GACzD,gEAAgE,GAChE,4DAA4D,GAC5D,+DAA+D,GAC/D,mEAAmE,GACnE,6DAA6D,GAC7D,2DAA2D,GAC3D,gEAAgE,GAChE,SAAS;MACbC,OAAO,EACL,kEAAkE;MACpEC,QAAQ,EACN,kEAAkE,GAChE,iCAAiC,GACjC,yDAAyD,GACzD,0BAA0B,GAC1B,6DAA6D,GAC7D,sEAAsE,GACtE;IACN,CAAC;IACDd,QAAQ,EAAE,CACRR,MAAM,EACNF,IAAI,CAACW,aAAa,EAClB;MACEJ,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAEP,KAAK,CAACwB,MAAM,CACjB,IAAI,EACJxB,KAAK,CAACyB,MAAM,CAAC,GAAGR,iBAAiB,CAAC,EAClC,IACF;IACF,CAAC,EACD;MACEX,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAE;IACT,CAAC,EACD;MACED,SAAS,EAAE,SAAS;MACpBC,KAAK,EACH;IACJ,CAAC,EACD;MACED,SAAS,EAAE,SAAS;MACpBC,KAAK,EAAEP,KAAK,CAACwB,MAAM,CACjB,IAAI,EACJxB,KAAK,CAACyB,MAAM,CAAC,GAAGT,gBAAgB,CAAC,EACjC,IACF;IACF,CAAC,EACD;MACEU,aAAa,EAAE,IAAI;MACnBtB,OAAO,EAAE,UAAU;MACnBK,QAAQ,EAAE,CACRV,IAAI,CAAC4B,qBAAqB,EAC1BtB,MAAM;IAEV,CAAC,EACD,GAAGS,QAAQ,CACZ;IACDV,OAAO,EAAE;EACX,CAAC;AACH;AAEAwB,MAAM,CAACC,OAAO,GAAG/B,WAAW"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}