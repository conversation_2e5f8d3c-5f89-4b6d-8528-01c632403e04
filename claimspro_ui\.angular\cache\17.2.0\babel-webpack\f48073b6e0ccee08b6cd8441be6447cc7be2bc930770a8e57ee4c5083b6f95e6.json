{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ProfileBlankComponent {\n  constructor() {}\n  ngOnInit() {}\n  static #_ = this.ɵfac = function ProfileBlankComponent_Factory(t) {\n    return new (t || ProfileBlankComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProfileBlankComponent,\n    selectors: [[\"app-profile-blank\"]],\n    decls: 2,\n    vars: 0,\n    template: function ProfileBlankComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"p\");\n        i0.ɵɵtext(1, \" profile-blank works!\\n\");\n        i0.ɵɵelementEnd();\n      }\n    },\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["ProfileBlankComponent", "constructor", "ngOnInit", "_", "_2", "selectors", "decls", "vars", "template", "ProfileBlankComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\views\\profile\\profile-blank\\profile-blank.component.ts", "C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\views\\profile\\profile-blank\\profile-blank.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-profile-blank',\n  templateUrl: './profile-blank.component.html',\n  styleUrls: ['./profile-blank.component.css']\n})\nexport class ProfileBlankComponent implements OnInit {\n\n  constructor() { }\n\n  ngOnInit() {\n  }\n\n}\n", "<p>\n  profile-blank works!\n</p>\n"], "mappings": ";AAOA,OAAM,MAAOA,qBAAqB;EAEhCC,YAAA,GAAgB;EAEhBC,QAAQA,CAAA,GACR;EAAC,QAAAC,CAAA,G;qBALUH,qBAAqB;EAAA;EAAA,QAAAI,EAAA,G;UAArBJ,qBAAqB;IAAAK,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCPlCE,EAAA,CAAAC,cAAA,QAAG;QACDD,EAAA,CAAAE,MAAA,8BACF;QAAAF,EAAA,CAAAG,YAAA,EAAI"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}