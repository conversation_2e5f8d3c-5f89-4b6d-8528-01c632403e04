{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/input\";\nimport * as i4 from \"@angular/material/form-field\";\nimport * as i5 from \"@angular/material/core\";\nimport * as i6 from \"@angular/material/select\";\nfunction ThemingFormFieldComponent_mat_error_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \"Min size: 10px\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class ThemingFormFieldComponent {\n  constructor(fb) {\n    this.options = fb.group({\n      color: 'primary',\n      fontSize: [16, Validators.min(10)]\n    });\n  }\n  getFontSize() {\n    return Math.max(10, this.options.value.fontSize);\n  }\n  ngOnInit() {}\n  static #_ = this.ɵfac = function ThemingFormFieldComponent_Factory(t) {\n    return new (t || ThemingFormFieldComponent)(i0.ɵɵdirectiveInject(i1.UntypedFormBuilder));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ThemingFormFieldComponent,\n    selectors: [[\"app-theming-form-field\"]],\n    decls: 12,\n    vars: 6,\n    consts: [[1, \"example-container\", 3, \"formGroup\"], [3, \"color\"], [\"placeholder\", \"Color\", \"formControlName\", \"color\"], [\"value\", \"primary\"], [\"value\", \"accent\"], [\"value\", \"warn\"], [\"matInput\", \"\", \"type\", \"number\", \"placeholder\", \"Font size (px)\", \"formControlName\", \"fontSize\", \"min\", \"10\"], [4, \"ngIf\"]],\n    template: function ThemingFormFieldComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"form\", 0)(1, \"mat-form-field\", 1)(2, \"mat-select\", 2)(3, \"mat-option\", 3);\n        i0.ɵɵtext(4, \"Primary\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"mat-option\", 4);\n        i0.ɵɵtext(6, \"Accent\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"mat-option\", 5);\n        i0.ɵɵtext(8, \"Warn\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(9, \"mat-form-field\", 1);\n        i0.ɵɵelement(10, \"input\", 6);\n        i0.ɵɵtemplate(11, ThemingFormFieldComponent_mat_error_11_Template, 2, 0, \"mat-error\", 7);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        let tmp_4_0;\n        i0.ɵɵstyleProp(\"font-size\", ctx.getFontSize(), \"px\");\n        i0.ɵɵproperty(\"formGroup\", ctx.options);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"color\", ctx.options.value.color);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"color\", ctx.options.value.color);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx.options.get(\"fontSize\")) == null ? null : tmp_4_0.invalid);\n      }\n    },\n    dependencies: [i2.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.FormGroupDirective, i1.FormControlName, i3.MatInput, i4.MatFormField, i4.MatError, i5.MatOption, i6.MatSelect],\n    styles: [\".example-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.example-container[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hc3NldHMvZXhhbXBsZXMvbWF0ZXJpYWwvdGhlbWluZy1mb3JtLWZpZWxkL3RoZW1pbmctZm9ybS1maWVsZC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGFBQUE7RUFDQSxzQkFBQTtBQUNKOztBQUVFO0VBQ0UsV0FBQTtBQUNKIiwic291cmNlc0NvbnRlbnQiOlsiLmV4YW1wbGUtY29udGFpbmVyIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIH1cbiAgXG4gIC5leGFtcGxlLWNvbnRhaW5lciA+ICoge1xuICAgIHdpZHRoOiAxMDAlO1xuICB9XG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ThemingFormFieldComponent", "constructor", "fb", "options", "group", "color", "fontSize", "min", "getFontSize", "Math", "max", "value", "ngOnInit", "_", "ɵɵdirectiveInject", "i1", "UntypedFormBuilder", "_2", "selectors", "decls", "vars", "consts", "template", "ThemingFormFieldComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "ThemingFormFieldComponent_mat_error_11_Template", "ɵɵstyleProp", "ɵɵproperty", "ɵɵadvance", "tmp_4_0", "get", "invalid"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\assets\\examples\\material\\theming-form-field\\theming-form-field.component.ts", "C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\assets\\examples\\material\\theming-form-field\\theming-form-field.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport {UntypedFormBuilder, UntypedFormGroup, Validators} from '@angular/forms';\n\n@Component({\n  selector: 'app-theming-form-field',\n  templateUrl: './theming-form-field.component.html',\n  styleUrls: ['./theming-form-field.component.scss']\n})\nexport class ThemingFormFieldComponent implements OnInit {\n\n  options: UntypedFormGroup;\n\n  constructor(fb: UntypedFormBuilder) {\n    this.options = fb.group({\n      color: 'primary',\n      fontSize: [16, Validators.min(10)],\n    });\n  }\n\n  getFontSize() {\n    return Math.max(10, this.options.value.fontSize);\n  }\n\n  ngOnInit() {\n  }\n\n}\n", "<form class=\"example-container\" [formGroup]=\"options\" [style.fontSize.px]=\"getFontSize()\">\n  <mat-form-field [color]=\"options.value.color\">\n    <mat-select placeholder=\"Color\" formControlName=\"color\">\n      <mat-option value=\"primary\">Primary</mat-option>\n      <mat-option value=\"accent\">Accent</mat-option>\n      <mat-option value=\"warn\">Warn</mat-option>\n    </mat-select>\n  </mat-form-field>\n\n  <mat-form-field [color]=\"options.value.color\">\n    <input matInput type=\"number\" placeholder=\"Font size (px)\" formControlName=\"fontSize\" min=\"10\">\n    <mat-error *ngIf=\"options.get('fontSize')?.invalid\">Min size: 10px</mat-error>\n  </mat-form-field>\n</form>\n"], "mappings": "AACA,SAA8CA,UAAU,QAAO,gBAAgB;;;;;;;;;;ICU3EC,EAAA,CAAAC,cAAA,gBAAoD;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;ADHlF,OAAM,MAAOC,yBAAyB;EAIpCC,YAAYC,EAAsB;IAChC,IAAI,CAACC,OAAO,GAAGD,EAAE,CAACE,KAAK,CAAC;MACtBC,KAAK,EAAE,SAAS;MAChBC,QAAQ,EAAE,CAAC,EAAE,EAAEX,UAAU,CAACY,GAAG,CAAC,EAAE,CAAC;KAClC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,OAAOC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAE,IAAI,CAACP,OAAO,CAACQ,KAAK,CAACL,QAAQ,CAAC;EAClD;EAEAM,QAAQA,CAAA,GACR;EAAC,QAAAC,CAAA,G;qBAhBUb,yBAAyB,EAAAJ,EAAA,CAAAkB,iBAAA,CAAAC,EAAA,CAAAC,kBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAzBjB,yBAAyB;IAAAkB,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCRtC5B,EAAA,CAAAC,cAAA,cAA0F;QAGxDD,EAAA,CAAAE,MAAA,cAAO;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAChDH,EAAA,CAAAC,cAAA,oBAA2B;QAAAD,EAAA,CAAAE,MAAA,aAAM;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAC9CH,EAAA,CAAAC,cAAA,oBAAyB;QAAAD,EAAA,CAAAE,MAAA,WAAI;QAAAF,EAAA,CAAAG,YAAA,EAAa;QAI9CH,EAAA,CAAAC,cAAA,wBAA8C;QAC5CD,EAAA,CAAA8B,SAAA,gBAA+F;QAC/F9B,EAAA,CAAA+B,UAAA,KAAAC,+CAAA,uBAA8E;QAChFhC,EAAA,CAAAG,YAAA,EAAiB;;;;QAZmCH,EAAA,CAAAiC,WAAA,cAAAJ,GAAA,CAAAjB,WAAA,SAAmC;QAAzDZ,EAAA,CAAAkC,UAAA,cAAAL,GAAA,CAAAtB,OAAA,CAAqB;QACnCP,EAAA,CAAAmC,SAAA,EAA6B;QAA7BnC,EAAA,CAAAkC,UAAA,UAAAL,GAAA,CAAAtB,OAAA,CAAAQ,KAAA,CAAAN,KAAA,CAA6B;QAQ7BT,EAAA,CAAAmC,SAAA,GAA6B;QAA7BnC,EAAA,CAAAkC,UAAA,UAAAL,GAAA,CAAAtB,OAAA,CAAAQ,KAAA,CAAAN,KAAA,CAA6B;QAE/BT,EAAA,CAAAmC,SAAA,GAAsC;QAAtCnC,EAAA,CAAAkC,UAAA,UAAAE,OAAA,GAAAP,GAAA,CAAAtB,OAAA,CAAA8B,GAAA,+BAAAD,OAAA,CAAAE,OAAA,CAAsC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}