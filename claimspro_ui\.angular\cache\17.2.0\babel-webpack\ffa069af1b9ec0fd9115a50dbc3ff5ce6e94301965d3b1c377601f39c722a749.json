{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatListModule } from '@angular/material/list';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { SharedModule } from '../../shared/shared.module';\nimport { HcpcsCodesTableComponent } from './hcpcsCodes-table/hcpcsCodes-table.component';\nimport { HcpcsCodesRoutes } from './hcpcsCodes.routing';\nimport { HcpcsCodesService } from './hcpcsCodes.service';\nimport { HcpcsCodesTablePopupComponent } from './hcpcsCodes-table/hcpcsCodes-table-popup/hcpcsCodes-table-popup.component';\nimport { TranslateModule } from '@ngx-translate/core';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class HcpcsCodesModule {\n  static #_ = this.ɵfac = function HcpcsCodesModule_Factory(t) {\n    return new (t || HcpcsCodesModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: HcpcsCodesModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [HcpcsCodesService],\n    imports: [CommonModule, ReactiveFormsModule, MatInputModule, MatIconModule, MatCardModule, MatMenuModule, MatButtonModule, MatChipsModule, MatListModule, MatPaginatorModule, MatTooltipModule, MatTableModule, MatDialogModule, MatSnackBarModule, MatSlideToggleModule, TranslateModule, SharedModule, RouterModule.forChild(HcpcsCodesRoutes)]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(HcpcsCodesModule, {\n    declarations: [HcpcsCodesTableComponent, HcpcsCodesTablePopupComponent],\n    imports: [CommonModule, ReactiveFormsModule, MatInputModule, MatIconModule, MatCardModule, MatMenuModule, MatButtonModule, MatChipsModule, MatListModule, MatPaginatorModule, MatTooltipModule, MatTableModule, MatDialogModule, MatSnackBarModule, MatSlideToggleModule, TranslateModule, SharedModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "ReactiveFormsModule", "MatButtonModule", "MatCardModule", "MatChipsModule", "MatDialogModule", "MatIconModule", "MatInputModule", "MatListModule", "MatMenuModule", "MatSlideToggleModule", "MatSnackBarModule", "MatTooltipModule", "SharedModule", "HcpcsCodesTableComponent", "HcpcsCodesRoutes", "HcpcsCodesService", "HcpcsCodesTablePopupComponent", "TranslateModule", "MatTableModule", "MatPaginatorModule", "HcpcsCodesModule", "_", "_2", "_3", "imports", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "i1"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\views\\hcpcsCodes\\hcpcsCodes.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterModule } from '@angular/router';\r\nimport { ReactiveFormsModule } from '@angular/forms';\r\nimport { MatButtonModule as MatButtonModule } from '@angular/material/button';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatChipsModule as MatChipsModule } from '@angular/material/chips';\r\nimport { MatDialogModule as MatDialogModule } from '@angular/material/dialog';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatInputModule as MatInputModule } from '@angular/material/input';\r\nimport { MatListModule } from '@angular/material/list';\r\nimport { MatMenuModule as MatMenuModule } from '@angular/material/menu';\r\nimport { MatSlideToggleModule as MatSlideToggleModule } from '@angular/material/slide-toggle';\r\nimport { MatSnackBarModule as MatSnackBarModule } from '@angular/material/snack-bar';\r\nimport { MatTooltipModule as MatTooltipModule } from '@angular/material/tooltip';\r\nimport { SharedModule } from '../../shared/shared.module';\r\nimport { HcpcsCodesTableComponent } from './hcpcsCodes-table/hcpcsCodes-table.component';\r\n\r\nimport { HcpcsCodesRoutes } from './hcpcsCodes.routing';\r\nimport { HcpcsCodesService } from './hcpcsCodes.service';\r\nimport { HcpcsCodesTablePopupComponent } from './hcpcsCodes-table/hcpcsCodes-table-popup/hcpcsCodes-table-popup.component'\r\nimport { TranslateModule } from '@ngx-translate/core';\r\nimport { MatTableModule as MatTableModule } from '@angular/material/table';\r\nimport { MatPaginatorModule as MatPaginatorModule } from '@angular/material/paginator';\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    MatInputModule,\r\n    MatIconModule,\r\n    MatCardModule,\r\n    MatMenuModule,\r\n    MatButtonModule,\r\n    MatChipsModule,\r\n    MatListModule,\r\n    MatPaginatorModule,\r\n    MatTooltipModule,\r\n    MatTableModule,\r\n    MatDialogModule,\r\n    MatSnackBarModule,\r\n    MatSlideToggleModule,\r\n    TranslateModule,\r\n    SharedModule,\r\n    RouterModule.forChild(HcpcsCodesRoutes)\r\n  ],\r\n  declarations: [HcpcsCodesTableComponent, HcpcsCodesTablePopupComponent],\r\n  providers: [HcpcsCodesService]\r\n})\r\nexport class HcpcsCodesModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,eAAkC,QAAQ,0BAA0B;AAC7E,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAgC,QAAQ,yBAAyB;AAC1E,SAASC,eAAkC,QAAQ,0BAA0B;AAC7E,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAgC,QAAQ,yBAAyB;AAC1E,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAA8B,QAAQ,wBAAwB;AACvE,SAASC,oBAA4C,QAAQ,gCAAgC;AAC7F,SAASC,iBAAsC,QAAQ,6BAA6B;AACpF,SAASC,gBAAoC,QAAQ,2BAA2B;AAChF,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,wBAAwB,QAAQ,+CAA+C;AAExF,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,6BAA6B,QAAQ,4EAA4E;AAC1H,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,cAAgC,QAAQ,yBAAyB;AAC1E,SAASC,kBAAwC,QAAQ,6BAA6B;;;AA0BtF,OAAM,MAAOC,gBAAgB;EAAA,QAAAC,CAAA,G;qBAAhBD,gBAAgB;EAAA;EAAA,QAAAE,EAAA,G;UAAhBF;EAAgB;EAAA,QAAAG,EAAA,G;eAFhB,CAACR,iBAAiB,CAAC;IAAAS,OAAA,GApB5B1B,YAAY,EACZE,mBAAmB,EACnBM,cAAc,EACdD,aAAa,EACbH,aAAa,EACbM,aAAa,EACbP,eAAe,EACfE,cAAc,EACdI,aAAa,EACbY,kBAAkB,EAClBR,gBAAgB,EAChBO,cAAc,EACdd,eAAe,EACfM,iBAAiB,EACjBD,oBAAoB,EACpBQ,eAAe,EACfL,YAAY,EACZb,YAAY,CAAC0B,QAAQ,CAACX,gBAAgB,CAAC;EAAA;;;2EAK9BM,gBAAgB;IAAAM,YAAA,GAHZb,wBAAwB,EAAEG,6BAA6B;IAAAQ,OAAA,GAnBpE1B,YAAY,EACZE,mBAAmB,EACnBM,cAAc,EACdD,aAAa,EACbH,aAAa,EACbM,aAAa,EACbP,eAAe,EACfE,cAAc,EACdI,aAAa,EACbY,kBAAkB,EAClBR,gBAAgB,EAChBO,cAAc,EACdd,eAAe,EACfM,iBAAiB,EACjBD,oBAAoB,EACpBQ,eAAe,EACfL,YAAY,EAAAe,EAAA,CAAA5B,YAAA;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}