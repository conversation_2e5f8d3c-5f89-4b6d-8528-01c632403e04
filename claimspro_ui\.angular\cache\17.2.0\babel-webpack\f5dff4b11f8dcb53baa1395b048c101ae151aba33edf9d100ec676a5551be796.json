{"ast": null, "code": "import { PatientsTableComponent } from './patients-table/patients-table.component';\nexport const PatientsRoutes = [{\n  path: 'patients-table',\n  component: PatientsTableComponent,\n  data: {\n    title: 'Table',\n    breadcrumb: 'Table'\n  }\n}];", "map": {"version": 3, "names": ["PatientsTableComponent", "PatientsRoutes", "path", "component", "data", "title", "breadcrumb"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\views\\patients\\patients.routing.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\r\nimport { PatientsTableComponent } from './patients-table/patients-table.component';\r\n\r\nexport const PatientsRoutes: Routes = [\r\n  { \r\n    path: 'patients-table', \r\n    component: PatientsTableComponent, \r\n    data: { title: 'Table', breadcrumb: 'Table' } \r\n  }\r\n];"], "mappings": "AACA,SAASA,sBAAsB,QAAQ,2CAA2C;AAElF,OAAO,MAAMC,cAAc,GAAW,CACpC;EACEC,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEH,sBAAsB;EACjCI,IAAI,EAAE;IAAEC,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE;EAAO;CAC5C,CACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}