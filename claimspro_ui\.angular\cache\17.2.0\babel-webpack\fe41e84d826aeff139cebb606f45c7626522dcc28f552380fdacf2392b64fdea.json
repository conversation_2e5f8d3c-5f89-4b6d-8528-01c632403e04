{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"../../../../app/shared/components/button-loading/button-loading.component\";\nfunction CustomLoadingButtonsComponent_button_loading_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button-loading\", 2);\n    i0.ɵɵlistener(\"click\", function CustomLoadingButtonsComponent_button_loading_5_Template_button_loading_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const btn_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.showLoading(btn_r1));\n    });\n    i0.ɵɵtext(1, \"Click Me\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const btn_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"loading\", btn_r1.loading)(\"color\", btn_r1.name);\n  }\n}\nexport class CustomLoadingButtonsComponent {\n  constructor(cdr) {\n    this.cdr = cdr;\n    this.buttons = [{\n      name: \"default\",\n      loading: false\n    }, {\n      name: \"primary\",\n      loading: false\n    }, {\n      name: \"accent\",\n      loading: false\n    }, {\n      name: \"warn\",\n      loading: false\n    }];\n  }\n  ngOnInit() {}\n  showLoading(button) {\n    button.loading = true;\n    setTimeout(() => {\n      button.loading = false;\n      this.cdr.detectChanges();\n    }, 3000);\n  }\n  static #_ = this.ɵfac = function CustomLoadingButtonsComponent_Factory(t) {\n    return new (t || CustomLoadingButtonsComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: CustomLoadingButtonsComponent,\n    selectors: [[\"app-custom-loading-buttons\"]],\n    decls: 6,\n    vars: 1,\n    consts: [[1, \"text-muted\", \"mb-6\"], [\"class\", \"mr-4 rtl:ml-4\", 3, \"loading\", \"color\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"mr-4\", \"rtl:ml-4\", 3, \"loading\", \"color\", \"click\"]],\n    template: function CustomLoadingButtonsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"h3\");\n        i0.ɵɵtext(1, \"ngModule\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"p\", 0)(3, \"span\");\n        i0.ɵɵtext(4, \"SharedComponentsModule\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(5, CustomLoadingButtonsComponent_button_loading_5_Template, 2, 2, \"button-loading\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngForOf\", ctx.buttons);\n      }\n    },\n    dependencies: [i1.NgForOf, i2.ButtonLoadingComponent],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵlistener", "CustomLoadingButtonsComponent_button_loading_5_Template_button_loading_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r3", "btn_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "showLoading", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "loading", "name", "CustomLoadingButtonsComponent", "constructor", "cdr", "buttons", "ngOnInit", "button", "setTimeout", "detectChanges", "_", "ɵɵdirectiveInject", "ChangeDetectorRef", "_2", "selectors", "decls", "vars", "consts", "template", "CustomLoadingButtonsComponent_Template", "rf", "ctx", "ɵɵtemplate", "CustomLoadingButtonsComponent_button_loading_5_Template", "ɵɵadvance"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\assets\\examples\\material\\custom-loading-buttons\\custom-loading-buttons.component.ts", "C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\assets\\examples\\material\\custom-loading-buttons\\custom-loading-buttons.component.html"], "sourcesContent": ["import { Component, OnInit, ChangeDetectorRef } from \"@angular/core\";\n\n@Component({\n  selector: \"app-custom-loading-buttons\",\n  templateUrl: \"./custom-loading-buttons.component.html\",\n  styleUrls: [\"./custom-loading-buttons.component.scss\"]\n})\nexport class CustomLoadingButtonsComponent implements OnInit {\n  defaultLoading: boolean;\n  primaryLoading: boolean;\n  accentLoading: boolean;\n  warnLoading: boolean;\n  buttons = [\n    {\n      name: \"default\",\n      loading: false\n    },\n    {\n      name: \"primary\",\n      loading: false\n    },\n    {\n      name: \"accent\",\n      loading: false\n    },\n    {\n      name: \"warn\",\n      loading: false\n    }\n  ];\n\n  constructor(\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  ngOnInit() {}\n\n  showLoading(button) {\n    button.loading = true;\n    setTimeout(() => {\n      button.loading = false;\n      this.cdr.detectChanges();\n    }, 3000);\n  }\n}\n", "<h3>ngModule</h3>\n<p class=\"text-muted mb-6\"><span>SharedComponentsModule</span></p>\n\n<button-loading\n  *ngFor=\"let btn of buttons\"\n  [loading]=\"btn.loading\"\n  class=\"mr-4 rtl:ml-4\"\n  [color]=\"btn.name\"\n  (click)=\"showLoading(btn)\"\n  >Click Me</button-loading\n>\n"], "mappings": ";;;;;;ICGAA,EAAA,CAAAC,cAAA,wBAMG;IADDD,EAAA,CAAAE,UAAA,mBAAAC,wFAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,WAAA,CAAAL,MAAA,CAAgB;IAAA,EAAC;IACzBP,EAAA,CAAAa,MAAA,eAAQ;IAAAb,EAAA,CAAAc,YAAA,EACV;;;;IALCd,EAAA,CAAAe,UAAA,YAAAR,MAAA,CAAAS,OAAA,CAAuB,UAAAT,MAAA,CAAAU,IAAA;;;ADEzB,OAAM,MAAOC,6BAA6B;EAwBxCC,YACUC,GAAsB;IAAtB,KAAAA,GAAG,GAAHA,GAAG;IApBb,KAAAC,OAAO,GAAG,CACR;MACEJ,IAAI,EAAE,SAAS;MACfD,OAAO,EAAE;KACV,EACD;MACEC,IAAI,EAAE,SAAS;MACfD,OAAO,EAAE;KACV,EACD;MACEC,IAAI,EAAE,QAAQ;MACdD,OAAO,EAAE;KACV,EACD;MACEC,IAAI,EAAE,MAAM;MACZD,OAAO,EAAE;KACV,CACF;EAIE;EAEHM,QAAQA,CAAA,GAAI;EAEZV,WAAWA,CAACW,MAAM;IAChBA,MAAM,CAACP,OAAO,GAAG,IAAI;IACrBQ,UAAU,CAAC,MAAK;MACdD,MAAM,CAACP,OAAO,GAAG,KAAK;MACtB,IAAI,CAACI,GAAG,CAACK,aAAa,EAAE;IAC1B,CAAC,EAAE,IAAI,CAAC;EACV;EAAC,QAAAC,CAAA,G;qBApCUR,6BAA6B,EAAAlB,EAAA,CAAA2B,iBAAA,CAAA3B,EAAA,CAAA4B,iBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA7BX,6BAA6B;IAAAY,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCP1CpC,EAAA,CAAAC,cAAA,SAAI;QAAAD,EAAA,CAAAa,MAAA,eAAQ;QAAAb,EAAA,CAAAc,YAAA,EAAK;QACjBd,EAAA,CAAAC,cAAA,WAA2B;QAAMD,EAAA,CAAAa,MAAA,6BAAsB;QAAAb,EAAA,CAAAc,YAAA,EAAO;QAE9Dd,EAAA,CAAAsC,UAAA,IAAAC,uDAAA,4BAOC;;;QANiBvC,EAAA,CAAAwC,SAAA,GAAU;QAAVxC,EAAA,CAAAe,UAAA,YAAAsB,GAAA,CAAAhB,OAAA,CAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}