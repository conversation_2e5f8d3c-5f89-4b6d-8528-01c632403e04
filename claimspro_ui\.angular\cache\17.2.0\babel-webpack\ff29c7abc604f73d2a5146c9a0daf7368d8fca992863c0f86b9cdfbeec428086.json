{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { InvoiceRoutingModule } from './invoice-routing.module';\nimport { InvoiceListComponent } from './invoice-list/invoice-list.component';\nimport { InvoiceService } from './invoice.service';\nimport { InvoiceDetailsComponent } from './invoice-details/invoice-details.component';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SharedComponentsModule } from 'app/shared/components/shared-components.module';\nimport { SharedMaterialModule } from 'app/shared/shared-material.module';\nimport * as i0 from \"@angular/core\";\nexport class InvoiceModule {\n  static #_ = this.ɵfac = function InvoiceModule_Factory(t) {\n    return new (t || InvoiceModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: InvoiceModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [InvoiceService],\n    imports: [CommonModule, InvoiceRoutingModule, SharedMaterialModule, ReactiveFormsModule, SharedComponentsModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(InvoiceModule, {\n    declarations: [InvoiceListComponent, InvoiceDetailsComponent],\n    imports: [CommonModule, InvoiceRoutingModule, SharedMaterialModule, ReactiveFormsModule, SharedComponentsModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "InvoiceRoutingModule", "InvoiceListComponent", "InvoiceService", "InvoiceDetailsComponent", "ReactiveFormsModule", "SharedComponentsModule", "SharedMaterialModule", "InvoiceModule", "_", "_2", "_3", "imports", "declarations"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\app\\views\\invoice\\invoice.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nimport { InvoiceRoutingModule } from './invoice-routing.module';\nimport { InvoiceListComponent } from './invoice-list/invoice-list.component';\nimport { InvoiceService } from './invoice.service';\nimport { InvoiceDetailsComponent } from './invoice-details/invoice-details.component';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { SharedComponentsModule } from 'app/shared/components/shared-components.module';\nimport { SharedMaterialModule } from 'app/shared/shared-material.module';\n\n@NgModule({\n  imports: [\n    CommonModule,\n    InvoiceRoutingModule,\n    SharedMaterialModule,\n    ReactiveFormsModule,\n    SharedComponentsModule,\n  ],\n  declarations: [InvoiceListComponent, InvoiceDetailsComponent],\n  providers: [InvoiceService]\n})\n\nexport class InvoiceModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,sBAAsB,QAAQ,gDAAgD;AACvF,SAASC,oBAAoB,QAAQ,mCAAmC;;AAcxE,OAAM,MAAOC,aAAa;EAAA,QAAAC,CAAA,G;qBAAbD,aAAa;EAAA;EAAA,QAAAE,EAAA,G;UAAbF;EAAa;EAAA,QAAAG,EAAA,G;eAHb,CAACR,cAAc,CAAC;IAAAS,OAAA,GAPzBZ,YAAY,EACZC,oBAAoB,EACpBM,oBAAoB,EACpBF,mBAAmB,EACnBC,sBAAsB;EAAA;;;2EAMbE,aAAa;IAAAK,YAAA,GAJTX,oBAAoB,EAAEE,uBAAuB;IAAAQ,OAAA,GAN1DZ,YAAY,EACZC,oBAAoB,EACpBM,oBAAoB,EACpBF,mBAAmB,EACnBC,sBAAsB;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}