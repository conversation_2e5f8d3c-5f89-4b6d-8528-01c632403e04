{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/progress-bar\";\nexport class IndeterminateBarComponent {\n  constructor() {}\n  ngOnInit() {}\n  static #_ = this.ɵfac = function IndeterminateBarComponent_Factory(t) {\n    return new (t || IndeterminateBarComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: IndeterminateBarComponent,\n    selectors: [[\"app-indeterminate-bar\"]],\n    decls: 1,\n    vars: 0,\n    consts: [[\"mode\", \"indeterminate\"]],\n    template: function IndeterminateBarComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"mat-progress-bar\", 0);\n      }\n    },\n    dependencies: [i1.MatProgressBar],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["IndeterminateBarComponent", "constructor", "ngOnInit", "_", "_2", "selectors", "decls", "vars", "consts", "template", "IndeterminateBarComponent_Template", "rf", "ctx", "i0", "ɵɵelement"], "sources": ["C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\assets\\examples\\material\\indeterminate-bar\\indeterminate-bar.component.ts", "C:\\OneDrive\\Srikant\\SWIP\\ClaimsPro\\claimspro200-src\\claimspro_ui\\src\\assets\\examples\\material\\indeterminate-bar\\indeterminate-bar.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-indeterminate-bar',\n  templateUrl: './indeterminate-bar.component.html',\n  styleUrls: ['./indeterminate-bar.component.scss']\n})\nexport class IndeterminateBarComponent implements OnInit {\n\n  constructor() { }\n\n  ngOnInit() {\n  }\n\n}\n", "<mat-progress-bar mode=\"indeterminate\"></mat-progress-bar>\n<!-- mode can be query -->"], "mappings": ";;AAOA,OAAM,MAAOA,yBAAyB;EAEpCC,YAAA,GAAgB;EAEhBC,QAAQA,CAAA,GACR;EAAC,QAAAC,CAAA,G;qBALUH,yBAAyB;EAAA;EAAA,QAAAI,EAAA,G;UAAzBJ,yBAAyB;IAAAK,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCPtCE,EAAA,CAAAC,SAAA,0BAA0D"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}